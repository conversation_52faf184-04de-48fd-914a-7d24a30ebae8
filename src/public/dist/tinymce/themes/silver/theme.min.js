/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.5.1 (2020-10-01)
 */
!function(){"use strict";var $=function(){},i=function(e,o){return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return e(o.apply(null,t))}},at=function(t){return function(){return t}},ct=function(t){return t};function g(o){for(var r=[],t=1;t<arguments.length;t++)r[t-1]=arguments[t];return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];var e=r.concat(t);return o.apply(null,e)}}var x=function(n){return function(t){return!n(t)}},u=function(t){return function(){throw new Error(t)}},c=at(!1),b=at(!0),t=tinymce.util.Tools.resolve("tinymce.ThemeManager"),nt=function(){return(nt=Object.assign||function(t){for(var n,e=1,o=arguments.length;e<o;e++)for(var r in n=arguments[e])Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r]);return t}).apply(this,arguments)};function y(t,n){var e={};for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&n.indexOf(o)<0&&(e[o]=t[o]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,o=Object.getOwnPropertySymbols(t);r<o.length;r++)n.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(t,o[r])&&(e[o[r]]=t[o[r]]);return e}function w(){for(var t=0,n=0,e=arguments.length;n<e;n++)t+=arguments[n].length;for(var o=Array(t),r=0,n=0;n<e;n++)for(var i=arguments[n],u=0,a=i.length;u<a;u++,r++)o[r]=i[u];return o}var n,e,o,r,s,a,l=function(){return f},f=(n=function(t){return t.isNone()},{fold:function(t,n){return t()},is:c,isSome:c,isNone:b,getOr:o=function(t){return t},getOrThunk:e=function(t){return t()},getOrDie:function(t){throw new Error(t||"error: getOrDie called on none.")},getOrNull:at(null),getOrUndefined:at(undefined),or:o,orThunk:e,map:l,each:$,bind:l,exists:c,forall:b,filter:l,equals:n,equals_:n,toArray:function(){return[]},toString:at("none()")}),d=function(e){var t=at(e),n=function(){return r},o=function(t){return t(e)},r={fold:function(t,n){return n(e)},is:function(t){return e===t},isSome:b,isNone:c,getOr:t,getOrThunk:t,getOrDie:t,getOrNull:t,getOrUndefined:t,or:n,orThunk:n,map:function(t){return d(t(e))},each:function(t){t(e)},bind:o,exists:o,forall:o,filter:function(t){return t(e)?r:f},toArray:function(){return[e]},toString:function(){return"some("+e+")"},equals:function(t){return t.is(e)},equals_:function(t,n){return t.fold(c,function(t){return n(e,t)})}};return r},st={some:d,none:l,from:function(t){return null===t||t===undefined?f:d(t)}},m=function(o){return function(t){return e=typeof(n=t),(null===n?"null":"object"==e&&(Array.prototype.isPrototypeOf(n)||n.constructor&&"Array"===n.constructor.name)?"array":"object"==e&&(String.prototype.isPrototypeOf(n)||n.constructor&&"String"===n.constructor.name)?"string":e)===o;var n,e}},p=function(n){return function(t){return typeof t===n}},S=m("string"),k=m("object"),h=m("array"),C=p("boolean"),v=(r=undefined,function(t){return r===t}),O=function(t){return!(null===(n=t)||n===undefined);var n},_=p("function"),et=p("number"),T=function(t,n){if(h(t)){for(var e=0,o=t.length;e<o;++e)if(!n(t[e]))return!1;return!0}return!1},E=Array.prototype.slice,B=Array.prototype.indexOf,D=Array.prototype.push,A=function(t,n){return B.call(t,n)},M=function(t,n){return-1<A(t,n)},F=function(t,n){for(var e=0,o=t.length;e<o;e++){if(n(t[e],e))return!0}return!1},I=function(t,n){for(var e=[],o=0;o<t;o++)e.push(n(o));return e},R=function(t,n){for(var e=[],o=0;o<t.length;o+=n){var r=E.call(t,o,o+n);e.push(r)}return e},V=function(t,n){for(var e=t.length,o=new Array(e),r=0;r<e;r++){var i=t[r];o[r]=n(i,r)}return o},ot=function(t,n){for(var e=0,o=t.length;e<o;e++){n(t[e],e)}},P=function(t,n){for(var e=[],o=[],r=0,i=t.length;r<i;r++){var u=t[r];(n(u,r)?e:o).push(u)}return{pass:e,fail:o}},H=function(t,n){for(var e=[],o=0,r=t.length;o<r;o++){var i=t[o];n(i,o)&&e.push(i)}return e},z=function(t,n,e){return function(t,n){for(var e=t.length-1;0<=e;e--){n(t[e],e)}}(t,function(t){e=n(e,t)}),e},N=function(t,n,e){return ot(t,function(t){e=n(e,t)}),e},L=function(t,n){return function(t,n,e){for(var o=0,r=t.length;o<r;o++){var i=t[o];if(n(i,o))return st.some(i);if(e(i,o))break}return st.none()}(t,n,c)},j=function(t,n){for(var e=0,o=t.length;e<o;e++){if(n(t[e],e))return st.some(e)}return st.none()},rt=function(t){for(var n=[],e=0,o=t.length;e<o;++e){if(!h(t[e]))throw new Error("Arr.flatten item "+e+" was not an array, input: "+t);D.apply(n,t[e])}return n},U=function(t,n){return rt(V(t,n))},W=function(t,n){for(var e=0,o=t.length;e<o;++e){if(!0!==n(t[e],e))return!1}return!0},G=function(t){var n=E.call(t,0);return n.reverse(),n},X=function(t,n){return H(t,function(t){return!M(n,t)})},Y=function(t,n){var e=E.call(t,0);return e.sort(n),e},q=function(t){return 0===t.length?st.none():st.some(t[0])},K=function(t){return 0===t.length?st.none():st.some(t[t.length-1])},J=_(Array.from)?Array.from:function(t){return E.call(t)},Q=function(t,n){for(var e=0;e<t.length;e++){var o=n(t[e],e);if(o.isSome())return o}return st.none()},Z=function(e){return{is:function(t){return e===t},isValue:b,isError:c,getOr:at(e),getOrThunk:at(e),getOrDie:at(e),or:function(t){return Z(e)},orThunk:function(t){return Z(e)},fold:function(t,n){return n(e)},map:function(t){return Z(t(e))},mapError:function(t){return Z(e)},each:function(t){t(e)},bind:function(t){return t(e)},exists:function(t){return t(e)},forall:function(t){return t(e)},toOptional:function(){return st.some(e)}}},tt=function(e){return{is:c,isValue:c,isError:b,getOr:ct,getOrThunk:function(t){return t()},getOrDie:function(){return u(String(e))()},or:function(t){return t},orThunk:function(t){return t()},fold:function(t,n){return t(e)},map:function(t){return tt(e)},mapError:function(t){return tt(t(e))},each:$,bind:function(t){return tt(e)},exists:c,forall:b,toOptional:st.none}},it={value:Z,error:tt,fromOption:function(t,n){return t.fold(function(){return tt(n)},Z)}};(a=s=s||{})[a.Error=0]="Error",a[a.Value=1]="Value";var ut,lt,ft=function(t,n,e){return t.stype===s.Error?n(t.serror):e(t.svalue)},dt=function(t){return{stype:s.Value,svalue:t}},mt=function(t){return{stype:s.Error,serror:t}},gt=function(t){return t.fold(mt,dt)},pt=function(t){return ft(t,it.error,it.value)},ht=dt,vt=function(t){var n=[],e=[];return ot(t,function(t){ft(t,function(t){return e.push(t)},function(t){return n.push(t)})}),{values:n,errors:e}},bt=mt,yt=function(t,n){return t.stype===s.Value?n(t.svalue):t},xt=function(t,n){return t.stype===s.Error?n(t.serror):t},wt=function(t,n){return t.stype===s.Value?{stype:s.Value,svalue:n(t.svalue)}:t},St=function(t,n){return t.stype===s.Error?{stype:s.Error,serror:n(t.serror)}:t},kt=Object.keys,Ct=Object.hasOwnProperty,Ot=function(t,n){for(var e=kt(t),o=0,r=e.length;o<r;o++){var i=e[o];n(t[i],i)}},_t=function(t,e){return Tt(t,function(t,n){return{k:n,v:e(t,n)}})},Tt=function(t,o){var r={};return Ot(t,function(t,n){var e=o(t,n);r[e.k]=e.v}),r},Et=function(t,n){var e,o,r,i,u={};return e=n,i=u,o=function(t,n){i[n]=t},r=$,Ot(t,function(t,n){(e(t,n)?o:r)(t,n)}),u},Bt=function(t,e){var o=[];return Ot(t,function(t,n){o.push(e(t,n))}),o},Dt=function(t,n){for(var e=kt(t),o=0,r=e.length;o<r;o++){var i=e[o],u=t[i];if(n(u,i,t))return st.some(u)}return st.none()},At=function(t){return Bt(t,function(t){return t})},Mt=function(t,n){return Ft(t,n)?st.from(t[n]):st.none()},Ft=function(t,n){return Ct.call(t,n)},It=function(t,n){return Ft(t,n)&&t[n]!==undefined&&null!==t[n]},Rt=function(u){if(!h(u))throw new Error("cases must be an array");if(0===u.length)throw new Error("there must be at least one case");var a=[],e={};return ot(u,function(t,o){var n=kt(t);if(1!==n.length)throw new Error("one and only one name per case");var r=n[0],i=t[r];if(e[r]!==undefined)throw new Error("duplicate key detected:"+r);if("cata"===r)throw new Error("cannot have a case named cata (sorry)");if(!h(i))throw new Error("case arguments must be an array");a.push(r),e[r]=function(){var t=arguments.length;if(t!==i.length)throw new Error("Wrong number of arguments to case "+r+". Expected "+i.length+" ("+i+"), got "+t);for(var e=new Array(t),n=0;n<e.length;n++)e[n]=arguments[n];return{fold:function(){if(arguments.length!==u.length)throw new Error("Wrong number of arguments to fold. Expected "+u.length+", got "+arguments.length);return arguments[o].apply(null,e)},match:function(t){var n=kt(t);if(a.length!==n.length)throw new Error("Wrong number of arguments to match. Expected: "+a.join(",")+"\nActual: "+n.join(","));if(!W(a,function(t){return M(n,t)}))throw new Error("Not all branches were specified when using match. Specified: "+n.join(", ")+"\nRequired: "+a.join(", "));return t[r].apply(null,e)},log:function(t){console.log(t,{constructors:a,constructor:r,params:e})}}}}),e},Vt=Object.prototype.hasOwnProperty,Pt=function(u){return function(){for(var t=new Array(arguments.length),n=0;n<t.length;n++)t[n]=arguments[n];if(0===t.length)throw new Error("Can't merge zero objects");for(var e={},o=0;o<t.length;o++){var r=t[o];for(var i in r)Vt.call(r,i)&&(e[i]=u(e[i],r[i]))}return e}},Ht=Pt(function(t,n){return k(t)&&k(n)?Ht(t,n):n}),zt=Pt(function(t,n){return n}),Nt=function(e){var o,r=!1;return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return r||(r=!0,o=e.apply(null,t)),o}},Lt=Rt([{strict:[]},{defaultedThunk:["fallbackThunk"]},{asOption:[]},{asDefaultedOptionThunk:["fallbackThunk"]},{mergeWithThunk:["baseThunk"]}]),jt=function(t){return Lt.defaultedThunk(at(t))},Ut=Lt.strict,Wt=Lt.asOption,Gt=Lt.defaultedThunk,Xt=(Lt.asDefaultedOptionThunk,Lt.mergeWithThunk),Yt=(Rt([{bothErrors:["error1","error2"]},{firstError:["error1","value2"]},{secondError:["value1","error2"]},{bothValues:["value1","value2"]}]),function(t,n){var e={};return e[t]=n,e}),qt=function(t,n){return e=n,o={},Ot(t,function(t,n){M(e,n)||(o[n]=t)}),o;var e,o},Kt=Yt,Jt=function(t){return n={},ot(t,function(t){n[t.key]=t.value}),n;var n},$t=function(t,n){var e,o,r,i,u,a=(e=[],o=[],ot(t,function(t){t.fold(function(t){e.push(t)},function(t){o.push(t)})}),{errors:e,values:o});return 0<a.errors.length?(u=a.errors,it.error(rt(u))):(i=n,0===(r=a.values).length?it.value(i):it.value(Ht(i,zt.apply(undefined,r))))},Qt=function(t){return i(bt,rt)(t)},Zt=function(t,n){var e,o,r=vt(t);return 0<r.errors.length?Qt(r.errors):(e=r.values,o=n,0<e.length?ht(Ht(o,zt.apply(undefined,e))):ht(o))},tn=function(t){var n=vt(t);return 0<n.errors.length?Qt(n.errors):ht(n.values)},nn=function(t){return k(t)&&100<kt(t).length?" removed due to size":JSON.stringify(t,null,2)},en=function(t,n){return bt([{path:t,getErrorInfo:n}])},on=Rt([{field:["key","okey","presence","prop"]},{state:["okey","instantiator"]}]),rn=function(e,o,r){return Mt(o,r).fold(function(){return t=r,n=o,en(e,function(){return'Could not find valid *strict* value for "'+t+'" in '+nn(n)});var t,n},ht)},un=function(t,n,e){var o=Mt(t,n).fold(function(){return e(t)},ct);return ht(o)},an=function(a,c,t,s){return t.fold(function(r,e,t,o){var i=function(t){var n=o.extract(a.concat([r]),s,t);return wt(n,function(t){return Yt(e,s(t))})},u=function(t){return t.fold(function(){var t=Yt(e,s(st.none()));return ht(t)},function(t){var n=o.extract(a.concat([r]),s,t);return wt(n,function(t){return Yt(e,s(st.some(t)))})})};return t.fold(function(){return yt(rn(a,c,r),i)},function(t){return yt(un(c,r,t),i)},function(){return yt(ht(Mt(c,r)),u)},function(t){return yt((e=t,o=Mt(n=c,r).map(function(t){return!0===t?e(n):t}),ht(o)),u);var n,e,o},function(t){var n=t(c),e=wt(un(c,r,at({})),function(t){return Ht(n,t)});return yt(e,i)})},function(t,n){var e=n(c);return ht(Yt(t,s(e)))})},cn=function(o){return{extract:function(e,t,n){return xt(o(n,t),function(t){return n=t,en(e,function(){return n});var n})},toString:function(){return"val"}}},sn=function(t){var u=ln(t),a=z(t,function(n,t){return t.fold(function(t){return Ht(n,Kt(t,!0))},at(n))},{});return{extract:function(t,n,e){var o,r=C(e)?[]:kt(Et(e,function(t){return t!==undefined&&null!==t})),i=H(r,function(t){return!It(a,t)});return 0===i.length?u.extract(t,n,e):(o=i,en(t,function(){return"There are unsupported fields: ["+o.join(", ")+"] specified"}))},toString:u.toString}},ln=function(a){return{extract:function(t,n,e){return o=t,r=e,i=n,u=V(a,function(t){return an(o,r,t,i)}),Zt(u,{});var o,r,i,u},toString:function(){return"obj{\n"+V(a,function(t){return t.fold(function(t,n,e,o){return t+" -> "+o.toString()},function(t,n){return"state("+t+")"})}).join("\n")+"}"}}},fn=function(r){return{extract:function(e,o,t){var n=V(t,function(t,n){return r.extract(e.concat(["["+n+"]"]),o,t)});return tn(n)},toString:function(){return"array("+r.toString()+")"}}},dn=function(a){return{extract:function(t,n,e){for(var o=[],r=0,i=a;r<i.length;r++){var u=i[r].extract(t,n,e);if(u.stype===s.Value)return u;o.push(u)}return tn(o)},toString:function(){return"oneOf("+V(a,function(t){return t.toString()}).join(", ")+")"}}},mn=function(a,c){return{extract:function(e,o,r){var t,n,i=kt(r),u=(t=e,n=i,fn(cn(a)).extract(t,ct,n));return yt(u,function(t){var n=V(t,function(t){return on.field(t,t,Ut(),c)});return ln(n).extract(e,o,r)})},toString:function(){return"setOf("+c.toString()+")"}}},gn=at(cn(ht)),pn=i(fn,ln),hn=on.state,vn=on.field,bn=function(e,n,o,r,i){return Mt(r,i).fold(function(){return t=r,n=i,en(e,function(){return'The chosen schema: "'+n+'" did not exist in branches: '+nn(t)});var t,n},function(t){return t.extract(e.concat(["branch: "+i]),n,o)})},yn=function(r,i){return{extract:function(n,e,o){return Mt(o,r).fold(function(){return t=r,en(n,function(){return'Choice schema did not contain choice key: "'+t+'"'});var t},function(t){return bn(n,e,o,i,t)})},toString:function(){return"chooseOn("+r+"). Possible values: "+kt(i)}}},xn=cn(ht),wn=function(t){return pn(t)},Sn=function(o){return{extract:function(t,n,e){return o().extract(t,n,e)},toString:function(){return o().toString()}}},kn=function(n){return cn(function(t){return n(t).fold(bt,ht)})},Cn=function(n,t){return mn(function(t){return gt(n(t))},t)},On=function(t,n,e){return pt((o=t,r=ct,i=e,u=n.extract([o],r,i),St(u,function(t){return{input:i,errors:t}})));var o,r,i,u},_n=function(t){return t.fold(function(t){throw new Error(En(t))},ct)},Tn=function(t,n,e){return _n(On(t,n,e))},En=function(t){return"Errors: \n"+(n=t.errors,e=10<n.length?n.slice(0,10).concat([{path:[],getErrorInfo:function(){return"... (only showing first ten failures)"}}]):n,V(e,function(t){return"Failed path: ("+t.path.join(" > ")+")\n"+t.getErrorInfo()}).join("\n"))+"\n\nInput object: "+nn(t.input);var n,e},Bn=yn,Dn=function(t,n){return yn(t,_t(n,ln))},An=at(xn),Mn=function(e,o){return cn(function(t){var n=typeof t;return e(t)?ht(t):bt("Expected type: "+o+" but got: "+n)})},Fn=Mn(et,"number"),In=Mn(S,"string"),Rn=Mn(C,"boolean"),Vn=Mn(_,"function"),Pn=function(n){var t=function(t,n){for(var e=t.next();!e.done;){if(!n(e.value))return!1;e=t.next()}return!0};if(Object(n)!==n)return!0;switch({}.toString.call(n).slice(8,-1)){case"Boolean":case"Number":case"String":case"Date":case"RegExp":case"Blob":case"FileList":case"ImageData":case"ImageBitmap":case"ArrayBuffer":return!0;case"Array":case"Object":return Object.keys(n).every(function(t){return Pn(n[t])});case"Map":return t(n.keys(),Pn)&&t(n.values(),Pn);case"Set":return t(n.keys(),Pn);default:return!1}},Hn=cn(function(t){return Pn(t)?ht(t):bt("Expected value to be acceptable for sending via postMessage")}),zn=function(n){return kn(function(t){return M(n,t)?it.value(t):it.error('Unsupported value: "'+t+'", choose one of "'+n.join(", ")+'".')})},Nn=function(t){return vn(t,t,Ut(),gn())},Ln=function(t,n){return vn(t,t,Ut(),n)},jn=function(t){return Ln(t,In)},Un=function(t,n){return vn(t,t,Ut(),zn(n))},Wn=function(t){return Ln(t,Vn)},Gn=function(t,n){return vn(t,t,Ut(),ln(n))},Xn=function(t,n){return vn(t,t,Ut(),pn(n))},Yn=function(t,n){return vn(t,t,Ut(),fn(n))},qn=function(t){return vn(t,t,Wt(),gn())},Kn=function(t,n){return vn(t,t,Wt(),n)},Jn=function(t){return Kn(t,Fn)},$n=function(t){return Kn(t,In)},Qn=function(t){return Kn(t,Vn)},Zn=function(t,n){return Kn(t,ln(n))},te=function(t,n){return vn(t,t,jt(n),gn())},ne=function(t,n,e){return vn(t,t,jt(n),e)},ee=function(t,n){return ne(t,n,Fn)},oe=function(t,n){return ne(t,n,In)},re=function(t,n,e){return ne(t,n,zn(e))},ie=function(t,n){return ne(t,n,Rn)},ue=function(t,n){return ne(t,n,Vn)},ae=function(t,n,e){return ne(t,n,ln(e))},ce=function(t,n){return hn(t,n)},se=function(t){var n=t;return{get:function(){return n},set:function(t){n=t}}},le=function(t){if(null===t||t===undefined)throw new Error("Node cannot be null or undefined");return{dom:t}},fe={fromHtml:function(t,n){var e=(n||document).createElement("div");if(e.innerHTML=t,!e.hasChildNodes()||1<e.childNodes.length)throw console.error("HTML does not have a single root node",t),new Error("HTML must have a single root node");return le(e.childNodes[0])},fromTag:function(t,n){var e=(n||document).createElement(t);return le(e)},fromText:function(t,n){var e=(n||document).createTextNode(t);return le(e)},fromDom:le,fromPoint:function(t,n,e){return st.from(t.dom.elementFromPoint(n,e)).map(le)}},de=function(t,n){var e=function(t,n){for(var e=0;e<t.length;e++){var o=t[e];if(o.test(n))return o}return undefined}(t,n);if(!e)return{major:0,minor:0};var o=function(t){return Number(n.replace(e,"$"+t))};return ge(o(1),o(2))},me=function(){return ge(0,0)},ge=function(t,n){return{major:t,minor:n}},pe={nu:ge,detect:function(t,n){var e=String(n).toLowerCase();return 0===t.length?me():de(t,e)},unknown:me},he=function(t,n){var e=String(n).toLowerCase();return L(t,function(t){return t.search(e)})},ve=function(t,e){return he(t,e).map(function(t){var n=pe.detect(t.versionRegexes,e);return{current:t.name,version:n}})},be=function(t,e){return he(t,e).map(function(t){var n=pe.detect(t.versionRegexes,e);return{current:t.name,version:n}})},ye=function(t,n){return-1!==t.indexOf(n)},xe=(ut=/^\s+|\s+$/g,function(t){return t.replace(ut,"")}),we=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,Se=function(n){return function(t){return ye(t,n)}},ke=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:function(t){return ye(t,"edge/")&&ye(t,"chrome")&&ye(t,"safari")&&ye(t,"applewebkit")}},{name:"Chrome",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,we],search:function(t){return ye(t,"chrome")&&!ye(t,"chromeframe")}},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:function(t){return ye(t,"msie")||ye(t,"trident")}},{name:"Opera",versionRegexes:[we,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:Se("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:Se("firefox")},{name:"Safari",versionRegexes:[we,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:function(t){return(ye(t,"safari")||ye(t,"mobile/"))&&ye(t,"applewebkit")}}],Ce=[{name:"Windows",search:Se("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:function(t){return ye(t,"iphone")||ye(t,"ipad")},versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:Se("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"OSX",search:Se("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:Se("linux"),versionRegexes:[]},{name:"Solaris",search:Se("sunos"),versionRegexes:[]},{name:"FreeBSD",search:Se("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:Se("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}],Oe={browsers:at(ke),oses:at(Ce)},_e="Firefox",Te=function(t){var n=t.current,e=t.version,o=function(t){return function(){return n===t}};return{current:n,version:e,isEdge:o("Edge"),isChrome:o("Chrome"),isIE:o("IE"),isOpera:o("Opera"),isFirefox:o(_e),isSafari:o("Safari")}},Ee={unknown:function(){return Te({current:undefined,version:pe.unknown()})},nu:Te,edge:at("Edge"),chrome:at("Chrome"),ie:at("IE"),opera:at("Opera"),firefox:at(_e),safari:at("Safari")},Be="Windows",De="Android",Ae="Solaris",Me="FreeBSD",Fe="ChromeOS",Ie=function(t){var n=t.current,e=t.version,o=function(t){return function(){return n===t}};return{current:n,version:e,isWindows:o(Be),isiOS:o("iOS"),isAndroid:o(De),isOSX:o("OSX"),isLinux:o("Linux"),isSolaris:o(Ae),isFreeBSD:o(Me),isChromeOS:o(Fe)}},Re={unknown:function(){return Ie({current:undefined,version:pe.unknown()})},nu:Ie,windows:at(Be),ios:at("iOS"),android:at(De),linux:at("Linux"),osx:at("OSX"),solaris:at(Ae),freebsd:at(Me),chromeos:at(Fe)},Ve=function(t,n){var e,o,r,i,u,a,c,s,l,f,d,m,g=Oe.browsers(),p=Oe.oses(),h=ve(g,t).fold(Ee.unknown,Ee.nu),v=be(p,t).fold(Re.unknown,Re.nu);return{browser:h,os:v,deviceType:(o=h,r=t,i=n,u=(e=v).isiOS()&&!0===/ipad/i.test(r),a=e.isiOS()&&!u,c=e.isiOS()||e.isAndroid(),s=c||i("(pointer:coarse)"),l=u||!a&&c&&i("(min-device-width:768px)"),f=a||c&&!l,d=o.isSafari()&&e.isiOS()&&!1===/safari/i.test(r),m=!f&&!l&&!d,{isiPad:at(u),isiPhone:at(a),isTablet:at(l),isPhone:at(f),isTouch:at(s),isAndroid:e.isAndroid,isiOS:e.isiOS,isWebView:at(d),isDesktop:at(m)})}},Pe=function(t){return window.matchMedia(t).matches},He=Nt(function(){return Ve(navigator.userAgent,Pe)}),ze=function(){return He()},Ne=function(t,n){var e=t.dom;if(1!==e.nodeType)return!1;var o=e;if(o.matches!==undefined)return o.matches(n);if(o.msMatchesSelector!==undefined)return o.msMatchesSelector(n);if(o.webkitMatchesSelector!==undefined)return o.webkitMatchesSelector(n);if(o.mozMatchesSelector!==undefined)return o.mozMatchesSelector(n);throw new Error("Browser lacks native selectors")},Le=function(t){return 1!==t.nodeType&&9!==t.nodeType&&11!==t.nodeType||0===t.childElementCount},je=function(t,n){return t.dom===n.dom},Ue=function(t,n){return e=t.dom,o=n.dom,r=e,i=o,u=Node.DOCUMENT_POSITION_CONTAINED_BY,0!=(r.compareDocumentPosition(i)&u);var e,o,r,i,u},We=function(t,n){return ze().browser.isIE()?Ue(t,n):(e=n,o=t.dom,r=e.dom,o!==r&&o.contains(r));var e,o,r},Ge=function(t){return _(t)?t:c},Xe=function(t,n,e){for(var o=t.dom,r=Ge(e);o.parentNode;){o=o.parentNode;var i=fe.fromDom(o),u=n(i);if(u.isSome())return u;if(r(i))break}return st.none()},Ye=function(t,n,e){var o=n(t),r=Ge(e);return o.orThunk(function(){return r(t)?st.none():Xe(t,n,r)})},qe=function(t,n){return je(t.element,n.event.target)},Ke=function(t){if(!It(t,"can")&&!It(t,"abort")&&!It(t,"run"))throw new Error("EventHandler defined by: "+JSON.stringify(t,null,2)+" does not have can, abort, or run!");return Tn("Extracting event.handler",sn([te("can",b),te("abort",c),te("run",$)]),t)},Je=function(e){var n,o,r,i,t=(o=function(t){return t.can},function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return N(n,function(t,n){return t&&o(n).apply(undefined,e)},!0)}),u=(r=n=e,i=function(t){return t.abort},function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return N(r,function(t,n){return t||i(n).apply(undefined,e)},!1)});return Ke({can:t,abort:u,run:function(){for(var n=[],t=0;t<arguments.length;t++)n[t]=arguments[t];ot(e,function(t){t.run.apply(undefined,n)})}})},$e=at,Qe=$e("touchstart"),Ze=$e("touchmove"),to=$e("touchend"),no=$e("touchcancel"),eo=$e("mousedown"),oo=$e("mousemove"),ro=$e("mouseout"),io=$e("mouseup"),uo=$e("mouseover"),ao=$e("focusin"),co=$e("focusout"),so=$e("keydown"),lo=$e("keyup"),fo=$e("input"),mo=$e("change"),go=$e("click"),po=$e("transitionend"),ho=$e("selectstart"),vo=function(t){return at("alloy."+t)},bo={tap:vo("tap")},yo=vo("focus"),xo=vo("blur.post"),wo=vo("paste.post"),So=vo("receive"),ko=vo("execute"),Co=vo("focus.item"),Oo=bo.tap,_o=vo("longpress"),To=vo("sandbox.close"),Eo=vo("typeahead.cancel"),Bo=vo("system.init"),Do=vo("system.touchmove"),Ao=vo("system.touchend"),Mo=vo("system.scroll"),Fo=vo("system.resize"),Io=vo("system.attached"),Ro=vo("system.detached"),Vo=vo("system.dismissRequested"),Po=vo("system.repositionRequested"),Ho=vo("focusmanager.shifted"),zo=vo("slotcontainer.visibility"),No=vo("change.tab"),Lo=vo("dismiss.tab"),jo=vo("highlight"),Uo=vo("dehighlight"),Wo=function(t,n){qo(t,t.element,n,{})},Go=function(t,n,e){qo(t,t.element,n,e)},Xo=function(t){Wo(t,ko())},Yo=function(t,n,e){qo(t,n,e,{})},qo=function(t,n,e,o){var r=nt({target:n},o);t.getSystem().triggerEvent(e,n,r)},Ko=function(t,n,e,o){t.getSystem().triggerEvent(e,n,o.event)},Jo=Jt,$o=function(t,n){return{key:t,value:Ke({abort:n})}},Qo=function(t){return{key:t,value:Ke({run:function(t,n){n.event.prevent()}})}},Zo=function(t,n){return{key:t,value:Ke({run:n})}},tr=function(t,e,o){return{key:t,value:Ke({run:function(t,n){e.apply(undefined,[t,n].concat(o))}})}},nr=function(t){return function(e){return{key:t,value:Ke({run:function(t,n){qe(t,n)&&e(t,n)}})}}},er=function(t,n,e){var o,r,i=n.partUids[e];return r=i,Zo(o=t,function(t,n){t.getSystem().getByUid(r).each(function(t){Ko(t,t.element,o,n)})})},or=function(t,r){return Zo(t,function(n,t){var e=t.event,o=n.getSystem().getByDom(e.target).fold(function(){return Ye(e.target,function(t){return n.getSystem().getByDom(t).toOptional()},c).getOr(n)},function(t){return t});r(n,o,t)})},rr=function(t){return Zo(t,function(t,n){n.cut()})},ir=function(t,n){return nr(t)(n)},ur=nr(Io()),ar=nr(Ro()),cr=nr(Bo()),sr=(lt=ko(),function(t){return Zo(lt,t)}),lr=("undefined"!=typeof window||Function("return this;")(),function(t){return t.dom.nodeName.toLowerCase()}),fr=function(n){return function(t){return t.dom.nodeType===n}},dr=fr(1),mr=fr(3),gr=fr(9),pr=fr(11),hr=function(t){return fe.fromDom(t.dom.ownerDocument)},vr=function(t){return gr(t)?t:hr(t)},br=function(t){return fe.fromDom(vr(t).dom.documentElement)},yr=function(t){return fe.fromDom(vr(t).dom.defaultView)},xr=function(t){return st.from(t.dom.parentNode).map(fe.fromDom)},wr=xr,Sr=function(t){return st.from(t.dom.offsetParent).map(fe.fromDom)},kr=function(t){return V(t.dom.childNodes,fe.fromDom)},Cr=function(t,n){var e=t.dom.childNodes;return st.from(e[n]).map(fe.fromDom)},Or=function(n,e){xr(n).each(function(t){t.dom.insertBefore(e.dom,n.dom)})},_r=function(t,n){var e;(e=t,st.from(e.dom.nextSibling).map(fe.fromDom)).fold(function(){xr(t).each(function(t){Er(t,n)})},function(t){Or(t,n)})},Tr=function(n,e){Cr(n,0).fold(function(){Er(n,e)},function(t){n.dom.insertBefore(e.dom,t.dom)})},Er=function(t,n){t.dom.appendChild(n.dom)},Br=function(n,t){ot(t,function(t){Er(n,t)})},Dr=function(t){t.dom.textContent="",ot(kr(t),function(t){Ar(t)})},Ar=function(t){var n=t.dom;null!==n.parentNode&&n.parentNode.removeChild(n)},Mr=function(t){var n,e=kr(t);0<e.length&&(n=t,ot(e,function(t){Or(n,t)})),Ar(t)},Fr=function(t){return t.dom.innerHTML},Ir=function(t,n){var e,o,r=hr(t).dom,i=fe.fromDom(r.createDocumentFragment()),u=(e=n,(o=(r||document).createElement("div")).innerHTML=e,kr(fe.fromDom(o)));Br(i,u),Dr(t),Er(t,i)},Rr=function(t,n,e){if(!(S(e)||C(e)||et(e)))throw console.error("Invalid call to Attribute.set. Key ",n,":: Value ",e,":: Element ",t),new Error("Attribute value was not simple");t.setAttribute(n,e+"")},Vr=function(t,n,e){Rr(t.dom,n,e)},Pr=function(t,n){var e=t.dom.getAttribute(n);return null===e?undefined:e},Hr=function(t,n){return st.from(Pr(t,n))},zr=function(t,n){var e=t.dom;return!(!e||!e.hasAttribute)&&e.hasAttribute(n)},Nr=function(t,n){t.dom.removeAttribute(n)},Lr=function(t){return n=t,e=!1,fe.fromDom(n.dom.cloneNode(e));var n,e},jr=function(t){var n,e,o,r=Lr(t);return n=r,e=fe.fromTag("div"),o=fe.fromDom(n.dom.cloneNode(!0)),Er(e,o),Fr(e)},Ur=Jo([{key:yo(),value:Ke({can:function(t,n){var e,o,r=n.event,i=r.originator,u=r.target;return o=u,!(je(e=i,t.element)&&!je(e,o))||(console.warn(yo()+" did not get interpreted by the desired target. \nOriginator: "+jr(i)+"\nTarget: "+jr(u)+"\nCheck the "+yo()+" event handlers"),!1)}})}]),Wr=/* */Object.freeze({__proto__:null,events:Ur}),Gr=0,Xr=function(t){var n=(new Date).getTime();return t+"_"+Math.floor(1e9*Math.random())+ ++Gr+String(n)},Yr=at("alloy-id-"),qr=at("data-alloy-id"),Kr=Yr(),Jr=qr(),$r=function(t,n){Object.defineProperty(t.dom,Jr,{value:n,writable:!0})},Qr=function(t){var n=dr(t)?t.dom[Jr]:null;return st.from(n)},Zr=Xr,ti=ct,ni=function(n){var t=function(t){return function(){throw new Error("The component must be in a context to send: "+t+(n?"\n"+jr(n().element)+" is not in context.":""))}};return{debugInfo:at("fake"),triggerEvent:t("triggerEvent"),triggerFocus:t("triggerFocus"),triggerEscape:t("triggerEscape"),build:t("build"),addToWorld:t("addToWorld"),removeFromWorld:t("removeFromWorld"),addToGui:t("addToGui"),removeFromGui:t("removeFromGui"),getByUid:t("getByUid"),getByDom:t("getByDom"),broadcast:t("broadcast"),broadcastOn:t("broadcastOn"),broadcastEvent:t("broadcastEvent"),isConnected:c}},ei=ni(),oi=function(t){return V(t,function(t){return o=n="/*",r=(e=t).length-n.length,""===o||e.length>=o.length&&e.substr(r,r+o.length)===o?t.substring(0,t.length-"/*".length):t;var n,e,o,r})},ri=function(t,n){var e=t.toString(),o=e.indexOf(")")+1,r=e.indexOf("("),i=e.substring(r+1,o-1).split(/,\s*/);return t.toFunctionAnnotation=function(){return{name:n,parameters:oi(i)}},t},ii=Xr("alloy-premade"),ui=function(t){return Kt(ii,t)},ai=function(o){return t=function(t){for(var n=[],e=1;e<arguments.length;e++)n[e-1]=arguments[e];return o.apply(void 0,w([t.getApis(),t],n))},n=o.toString(),e=n.indexOf(")")+1,r=n.indexOf("("),i=n.substring(r+1,e-1).split(/,\s*/),t.toFunctionAnnotation=function(){return{name:"OVERRIDE",parameters:oi(i.slice(1))}},t;var t,n,e,r,i},ci={init:function(){return si({readState:function(){return"No State required"}})}},si=function(t){return t},li=function(t,r){var i={};return Ot(t,function(t,o){Ot(t,function(t,n){var e=Mt(i,n).getOr([]);i[n]=e.concat([r(o,t)])})}),i},fi=function(t){return{classes:t.classes!==undefined?t.classes:[],attributes:t.attributes!==undefined?t.attributes:{},styles:t.styles!==undefined?t.styles:{}}},di=function(t,n){return{cHandler:g.apply(undefined,[t.handler].concat(n)),purpose:t.purpose}},mi=function(t){return t.cHandler},gi=function(t,n){return{name:t,handler:n}},pi=function(t,n,e){var o,r,i=nt(nt({},e),(o=t,r={},ot(n,function(t){r[t.name()]=t.handlers(o)}),r));return li(i,gi)},hi=function(t){var n,i=_(n=t)?{can:at(!0),abort:at(!1),run:n}:n;return function(t,n){for(var e=[],o=2;o<arguments.length;o++)e[o-2]=arguments[o];var r=[t,n].concat(e);i.abort.apply(undefined,r)?n.stop():i.can.apply(undefined,r)&&i.run.apply(undefined,r)}},vi=function(t,n,e){var o,r,i=n[e];return i?function(u,a,t,c){try{var n=Y(t,function(t,n){var e=t[a],o=n[a],r=c.indexOf(e),i=c.indexOf(o);if(-1===r)throw new Error("The ordering for "+u+" does not have an entry for "+e+".\nOrder specified: "+JSON.stringify(c,null,2));if(-1===i)throw new Error("The ordering for "+u+" does not have an entry for "+o+".\nOrder specified: "+JSON.stringify(c,null,2));return r<i?-1:i<r?1:0});return it.value(n)}catch(e){return it.error([e])}}("Event: "+e,"name",t,i).map(function(t){var n=V(t,function(t){return t.handler});return Je(n)}):(o=e,r=t,it.error(["The event ("+o+') has more than one behaviour that listens to it.\nWhen this occurs, you must specify an event ordering for the behaviours in your spec (e.g. [ "listing", "toggling" ]).\nThe behaviours that can trigger it are: '+JSON.stringify(V(r,function(t){return t.name}),null,2)]))},bi=function(t,i){var n=Bt(t,function(o,r){return(1===o.length?it.value(o[0].handler):vi(o,i,r)).map(function(t){var n=hi(t),e=1<o.length?H(i[r],function(n){return F(o,function(t){return t.name===n})}).join(" > "):o[0].name;return Kt(r,{handler:n,purpose:e})})});return $t(n,{})},yi="alloy.base.behaviour",xi=function(t){var n,e;return On("custom.definition",ln([vn("dom","dom",Ut(),ln([Nn("tag"),te("styles",{}),te("classes",[]),te("attributes",{}),qn("value"),qn("innerHtml")])),Nn("components"),Nn("uid"),te("events",{}),te("apis",{}),vn("eventOrder","eventOrder",((n={})[ko()]=["disabling",yi,"toggling","typeaheadevents"],n[yo()]=[yi,"focusing","keying"],n[Bo()]=[yi,"disabling","toggling","representing"],n[fo()]=[yi,"representing","streaming","invalidating"],n[Ro()]=[yi,"representing","item-events","tooltipping"],n[eo()]=["focusing",yi,"item-type-events"],n[Qe()]=["focusing",yi,"item-type-events"],n[uo()]=["item-type-events","tooltipping"],n[So()]=["receiving","reflecting","tooltipping"],e=n,Lt.mergeWithThunk(at(e))),An()),qn("domModification")]),t)},wi=function(t,n){var e=Pr(t,n);return e===undefined||""===e?[]:e.split(" ")},Si=function(t){return t.dom.classList!==undefined},ki=function(t,n){return r=n,i=wi(e=t,o="class").concat([r]),Vr(e,o,i.join(" ")),!0;var e,o,r,i},Ci=function(t,n){return r=n,0<(i=H(wi(e=t,o="class"),function(t){return t!==r})).length?Vr(e,o,i.join(" ")):Nr(e,o),!1;var e,o,r,i},Oi=function(t,n){Si(t)?t.dom.classList.add(n):ki(t,n)},_i=function(t){0===(Si(t)?t.dom.classList:wi(t,"class")).length&&Nr(t,"class")},Ti=function(t,n){Si(t)?t.dom.classList.remove(n):Ci(t,n),_i(t)},Ei=function(t,n){return Si(t)&&t.dom.classList.contains(n)},Bi=function(n,t){ot(t,function(t){Oi(n,t)})},Di=function(n,t){ot(t,function(t){Ti(n,t)})},Ai=function(t){return t.style!==undefined&&_(t.style.getPropertyValue)},Mi=function(t){return pr(t)},Fi=_(Element.prototype.attachShadow)&&_(Node.prototype.getRootNode),Ii=at(Fi),Ri=Fi?function(t){return fe.fromDom(t.dom.getRootNode())}:vr,Vi=function(t){return Mi(t)?t:fe.fromDom(vr(t).dom.body)},Pi=function(t){return fe.fromDom(t.dom.host)},Hi=function(t){return O(t.dom.shadowRoot)},zi=function(t){var n=mr(t)?t.dom.parentNode:t.dom;if(n===undefined||null===n||null===n.ownerDocument)return!1;var e,o,r,i,u=n.ownerDocument;return r=fe.fromDom(n),i=Ri(r),(Mi(i)?st.some(i):st.none()).fold(function(){return u.body.contains(n)},(e=zi,o=Pi,function(t){return e(o(t))}))},Ni=function(){return Li(fe.fromDom(document))},Li=function(t){var n=t.dom.body;if(null===n||n===undefined)throw new Error("Body is not available yet");return fe.fromDom(n)},ji=function(t,n,e){if(!S(e))throw console.error("Invalid call to CSS.set. Property ",n,":: Value ",e,":: Element ",t),new Error("CSS value must be a string: "+e);Ai(t)&&t.style.setProperty(n,e)},Ui=function(t,n){Ai(t)&&t.style.removeProperty(n)},Wi=function(t,n,e){var o=t.dom;ji(o,n,e)},Gi=function(t,n){var e=t.dom;Ot(n,function(t,n){ji(e,n,t)})},Xi=function(t,n){var e=t.dom;Ot(n,function(t,n){t.fold(function(){Ui(e,n)},function(t){ji(e,n,t)})})},Yi=function(t,n){var e=t.dom,o=window.getComputedStyle(e).getPropertyValue(n);return""!==o||zi(t)?o:qi(e,n)},qi=function(t,n){return Ai(t)?t.style.getPropertyValue(n):""},Ki=function(t,n){var e=t.dom,o=qi(e,n);return st.from(o).filter(function(t){return 0<t.length})},Ji=function(t,n,e){var o=fe.fromTag(t);return Wi(o,n,e),Ki(o,n).isSome()},$i=function(t,n){var e=t.dom;Ui(e,n),Hr(t,"style").map(xe).is("")&&Nr(t,"style")},Qi=function(t){return t.dom.offsetWidth},Zi=function(t){return t.dom.value},tu=function(t,n){if(n===undefined)throw new Error("Value.set was undefined");t.dom.value=n},nu=function(t){var n,e,o,r=fe.fromTag(t.tag);n=r,e=t.attributes,o=n.dom,Ot(e,function(t,n){Rr(o,n,t)}),Bi(r,t.classes),Gi(r,t.styles),t.innerHtml.each(function(t){return Ir(r,t)});var i=t.domChildren;return Br(r,i),t.value.each(function(t){tu(r,t)}),t.uid,$r(r,t.uid),r},eu=function(t,n){return e=t,r=V(o=n,function(t){return Zn(t.name(),[Nn("config"),te("state",ci)])}),i=On("component.behaviours",ln(r),e.behaviours).fold(function(t){throw new Error(En(t)+"\nComplete spec:\n"+JSON.stringify(e,null,2))},function(t){return t}),{list:o,data:_t(i,function(t){var n=t.map(function(t){return{config:t.config,state:t.state.init(t.config)}});return function(){return n}})};var e,o,r,i},ou=function(t){var n,e,o=(n=Mt(t,"behaviours").getOr({}),e=H(kt(n),function(t){return n[t]!==undefined}),V(e,function(t){return n[t].me}));return eu(t,o)},ru=function(t,n,e){var o,r,i,u=nt(nt({},(o=t).dom),{uid:o.uid,domChildren:V(o.components,function(t){return t.element})}),a=t.domModification.fold(function(){return fi({})},fi),c={"alloy.base.modification":a},s=0<n.length?function(n,t,e,o){var r=nt({},t);ot(e,function(t){r[t.name()]=t.exhibit(n,o)});var i=li(r,function(t,n){return{name:t,modification:n}}),u=function(t){return z(t,function(t,n){return nt(nt({},n.modification),t)},{})},a=z(i.classes,function(t,n){return n.modification.concat(t)},[]),c=u(i.attributes),s=u(i.styles);return fi({classes:a,attributes:c,styles:s})}(e,c,n,u):a;return i=s,nt(nt({},r=u),{attributes:nt(nt({},r.attributes),i.attributes),styles:nt(nt({},r.styles),i.styles),classes:r.classes.concat(i.classes)})},iu=function(t,n,e){var o,r,i,u={"alloy.base.behaviour":t.events};return o=e,r=t.eventOrder,i=pi(o,n,u),bi(i,r).getOrDie()},uu=function(t){var n,e,o,r,i,u,a,c,s,l,f,d,m,g=ti(t),p=g.events,h=y(g,["events"]),v=(n=Mt(h,"components").getOr([]),V(n,lu)),b=nt(nt({},h),{events:nt(nt({},Wr),p),components:v});return it.value((o=function(){return m},r=se(ei),i=_n(xi(e=b)),u=ou(e),a=u.list,c=u.data,s=ru(i,a,c),l=nu(s),f=iu(i,a,c),d=se(i.components),m={getSystem:r.get,config:function(t){var n=c;return(_(n[t.name()])?n[t.name()]:function(){throw new Error("Could not find "+t.name()+" in "+JSON.stringify(e,null,2))})()},hasConfigured:function(t){return _(c[t.name()])},spec:e,readState:function(t){return c[t]().map(function(t){return t.state.readState()}).getOr("not enabled")},getApis:function(){return i.apis},connect:function(t){r.set(t)},disconnect:function(){r.set(ni(o))},element:l,syncComponents:function(){var t=kr(l),n=U(t,function(t){return r.get().getByDom(t).fold(function(){return[]},function(t){return[t]})});d.set(n)},components:d.get,events:f}))},au=function(t){var n=fe.fromText(t);return cu({element:n})},cu=function(t){var n=Tn("external.component",sn([Nn("element"),qn("uid")]),t),e=se(ni());n.uid.each(function(t){$r(n.element,t)});var o={getSystem:e.get,config:st.none,hasConfigured:c,connect:function(t){e.set(t)},disconnect:function(){e.set(ni(function(){return o}))},getApis:function(){return{}},element:n.element,spec:t,readState:at("No state"),syncComponents:$,components:at([]),events:{}};return ui(o)},su=Zr,lu=function(n){return Mt(n,ii).fold(function(){var t=n.hasOwnProperty("uid")?n:nt({uid:su("")},n);return uu(t).getOrDie()},function(t){return t})},fu=ui;function du(o,r){var t=function(t){var n=r(t);if(n<=0||null===n){var e=Yi(t,o);return parseFloat(e)||0}return n},i=function(r,t){return N(t,function(t,n){var e=Yi(r,n),o=e===undefined?0:parseInt(e,10);return isNaN(o)?t:t+o},0)};return{set:function(t,n){if(!et(n)&&!n.match(/^[0-9]+$/))throw new Error(o+".set accepts only positive integer values. Value was "+n);var e=t.dom;Ai(e)&&(e.style[o]=n+"px")},get:t,getOuter:t,aggregate:i,max:function(t,n,e){var o=i(t,e);return o<n?n-o:0}}}var mu=du("height",function(t){var n=t.dom;return zi(t)?n.getBoundingClientRect().height:n.offsetHeight}),gu=function(t){return mu.get(t)},pu=function(t){return mu.getOuter(t)},hu=function(e,o){return{left:e,top:o,translate:function(t,n){return hu(e+t,o+n)}}},vu=hu,bu=function(t,n){return t!==undefined?t:n!==undefined?n:0},yu=function(t){var n=t.dom.ownerDocument,e=n.body,o=n.defaultView,r=n.documentElement;if(e===t.dom)return vu(e.offsetLeft,e.offsetTop);var i=bu(null==o?void 0:o.pageYOffset,r.scrollTop),u=bu(null==o?void 0:o.pageXOffset,r.scrollLeft),a=bu(r.clientTop,e.clientTop),c=bu(r.clientLeft,e.clientLeft);return xu(t).translate(u-c,i-a)},xu=function(t){var n,e=t.dom,o=e.ownerDocument.body;return o===e?vu(o.offsetLeft,o.offsetTop):zi(t)?(n=e.getBoundingClientRect(),vu(n.left,n.top)):vu(0,0)},wu=du("width",function(t){return t.dom.offsetWidth}),Su=function(t){return wu.get(t)},ku=function(t){return wu.getOuter(t)},Cu=function(t){var n=fe.fromDom(function(t){if(Ii()&&O(t.target)){var n=fe.fromDom(t.target);if(dr(n)&&Hi(n)&&t.composed&&t.composedPath){var e=t.composedPath();if(e)return q(e)}}return st.from(t.target)}(t).getOr(t.target)),e=function(){return t.stopPropagation()},o=function(){return t.preventDefault()},r=i(o,e);return{target:n,x:t.clientX,y:t.clientY,stop:e,prevent:o,kill:r,raw:t}},Ou=function(t,n,e,o,r){var i,u,a=(i=e,u=o,function(t){i(t)&&u(Cu(t))});return t.dom.addEventListener(n,a,r),{unbind:g(_u,t,n,a,r)}},_u=function(t,n,e,o){t.dom.removeEventListener(n,e,o)},Tu=function(t){var n=t!==undefined?t.dom:document,e=n.body.scrollLeft||n.documentElement.scrollLeft,o=n.body.scrollTop||n.documentElement.scrollTop;return vu(e,o)},Eu=function(t,n,e){var o=(e!==undefined?e.dom:document).defaultView;o&&o.scrollTo(t,n)},Bu=function(t,n,e,o){return{x:t,y:n,width:e,height:o,right:t+e,bottom:n+o}},Du=function(t){var n,e,o=t===undefined?window:t,r=o.document,i=Tu(fe.fromDom(r));return e=(n=o)===undefined?window:n,st.from(e.visualViewport).fold(function(){var t=o.document.documentElement,n=t.clientWidth,e=t.clientHeight;return Bu(i.left,i.top,n,e)},function(t){return Bu(Math.max(t.pageLeft,i.left),Math.max(t.pageTop,i.top),t.width,t.height)})},Au=function(o,t){return o.view(t).fold(at([]),function(t){var n=o.owner(t),e=Au(o,n);return[t].concat(e)})},Mu=/* */Object.freeze({__proto__:null,view:function(t){var n;return(t.dom===document?st.none():st.from(null===(n=t.dom.defaultView)||void 0===n?void 0:n.frameElement)).map(fe.fromDom)},owner:hr}),Fu=function(o){var t,n,e,r,i=fe.fromDom(document),u=Tu(i);return(t=o,e=(n=Mu).owner(t),r=Au(n,e),st.some(r)).fold(g(yu,o),function(t){var n=xu(o),e=z(t,function(t,n){var e=xu(n);return{left:t.left+e.left,top:t.top+e.top}},{left:0,top:0});return vu(e.left+n.left+u.left,e.top+n.top+u.top)})},Iu=function(t,n,e,o){return{x:t,y:n,width:e,height:o,right:t+e,bottom:n+o}},Ru=function(t){var n=yu(t),e=ku(t),o=pu(t);return Iu(n.left,n.top,e,o)},Vu=function(t){var n=Fu(t),e=ku(t),o=pu(t);return Iu(n.left,n.top,e,o)},Pu=function(){return Du(window)};function Hu(t,n,e,o,r){return t(e,o)?st.some(e):_(r)&&r(e)?st.none():n(e,o,r)}var zu,Nu,Lu=function(t,n,e){for(var o=t.dom,r=_(e)?e:c;o.parentNode;){o=o.parentNode;var i=fe.fromDom(o);if(n(i))return st.some(i);if(r(i))break}return st.none()},ju=function(t,n,e){return Hu(function(t,n){return n(t)},Lu,t,n,e)},Uu=function(t,n,e){return ju(t,n,e).isSome()},Wu=function(t,n,e){return Lu(t,function(t){return Ne(t,n)},e)},Gu=function(t,n){return e=n,r=(o=t)===undefined?document:o.dom,Le(r)?st.none():st.from(r.querySelector(e)).map(fe.fromDom);var e,o,r},Xu=function(t,n,e){return Hu(Ne,Wu,t,n,e)},Yu=function(){var n=Xr("aria-owns");return{id:n,link:function(t){Vr(t,"aria-owns",n)},unlink:function(t){Nr(t,"aria-owns")}}},qu=function(n,t){return ju(t,function(t){if(!dr(t))return!1;var n=Pr(t,"id");return n!==undefined&&-1<n.indexOf("aria-owns")}).bind(function(t){var n=Pr(t,"id"),e=Ri(t);return Gu(e,'[aria-owns="'+n+'"]')}).exists(function(t){return Ku(n,t)})},Ku=function(n,t){return Uu(t,function(t){return je(t,n.element)},c)||qu(n,t)},Ju="unknown";(Nu=zu=zu||{})[Nu.STOP=0]="STOP",Nu[Nu.NORMAL=1]="NORMAL",Nu[Nu.LOGGING=2]="LOGGING";var $u,Qu,Zu=se({}),ta=["alloy/data/Fields","alloy/debugging/Debugging"],na=function(n,t,e){var o,r,i,u;switch(Mt(Zu.get(),n).orThunk(function(){var t=kt(Zu.get());return Q(t,function(t){return-1<n.indexOf(t)?st.some(Zu.get()[t]):st.none()})}).getOr(zu.NORMAL)){case zu.NORMAL:return e(ea());case zu.LOGGING:var a=(o=n,r=t,i=[],u=(new Date).getTime(),{logEventCut:function(t,n,e){i.push({outcome:"cut",target:n,purpose:e})},logEventStopped:function(t,n,e){i.push({outcome:"stopped",target:n,purpose:e})},logNoParent:function(t,n,e){i.push({outcome:"no-parent",target:n,purpose:e})},logEventNoHandlers:function(t,n){i.push({outcome:"no-handlers-left",target:n})},logEventResponse:function(t,n,e){i.push({outcome:"response",purpose:e,target:n})},write:function(){var t=(new Date).getTime();M(["mousemove","mouseover","mouseout",Bo()],o)||console.log(o,{event:o,time:t-u,target:r.dom,sequence:V(i,function(t){return M(["cut","stopped","response"],t.outcome)?"{"+t.purpose+"} "+t.outcome+" at ("+jr(t.target)+")":t.outcome})})}}),c=e(a);return a.write(),c;case zu.STOP:return!0}},ea=at({logEventCut:$,logEventStopped:$,logNoParent:$,logEventNoHandlers:$,logEventResponse:$,write:$}),oa=at([Nn("menu"),Nn("selectedMenu")]),ra=at([Nn("item"),Nn("selectedItem")]),ia=(at(ln(ra().concat(oa()))),at(ln(ra()))),ua=Gn("initSize",[Nn("numColumns"),Nn("numRows")]),aa=function(){return Gn("markers",[Nn("backgroundMenu")].concat(oa()).concat(ra()))},ca=function(t){return Gn("markers",V(t,Nn))},sa=function(t,n,e){!function(){var t=new Error;if(t.stack===undefined)return;var n=t.stack.split("\n");L(n,function(n){return 0<n.indexOf("alloy")&&!F(ta,function(t){return-1<n.indexOf(t)})}).getOr(Ju)}();return vn(n,n,e,kn(function(e){return it.value(function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return e.apply(undefined,t)})}))},la=function(t){return sa(0,t,jt($))},fa=function(t){return sa(0,t,jt(st.none))},da=function(t){return sa(0,t,Ut())},ma=function(t){return sa(0,t,Ut())},ga=function(t,n){return ce(t,at(n))},pa=function(t){return ce(t,ct)},ha=at(ua),va=function(t,n,e,o,r,i){return{x:t,y:n,bubble:e,direction:o,boundsRestriction:r,label:i}},ba=Rt([{southeast:[]},{southwest:[]},{northeast:[]},{northwest:[]},{south:[]},{north:[]},{east:[]},{west:[]}]),ya=ba.southeast,xa=ba.southwest,wa=ba.northeast,Sa=ba.northwest,ka=ba.south,Ca=ba.north,Oa=ba.east,_a=ba.west,Ta=function(n,e){return function(t,n){for(var e={},o=0,r=t.length;o<r;o++){var i=t[o];e[String(i)]=n(i,o)}return e}(["left","right","top","bottom"],function(t){return Mt(e,t).map(function(t){return function(t,n){switch(n){case 1:return t.x;case 0:return t.x+t.width;case 2:return t.y;case 3:return t.y+t.height}}(n,t)})})},Ea=function(t){return t.x},Ba=function(t,n){return t.x+t.width/2-n.width/2},Da=function(t,n){return t.x+t.width-n.width},Aa=function(t,n){return t.y-n.height},Ma=function(t){return t.y+t.height},Fa=function(t,n){return t.y+t.height/2-n.height/2},Ia=function(t,n,e){return va(Ea(t),Ma(t),e.southeast(),ya(),Ta(t,{left:1,top:3}),"layout-se")},Ra=function(t,n,e){return va(Da(t,n),Ma(t),e.southwest(),xa(),Ta(t,{right:0,top:3}),"layout-sw")},Va=function(t,n,e){return va(Ea(t),Aa(t,n),e.northeast(),wa(),Ta(t,{left:1,bottom:2}),"layout-ne")},Pa=function(t,n,e){return va(Da(t,n),Aa(t,n),e.northwest(),Sa(),Ta(t,{right:0,bottom:2}),"layout-nw")},Ha=function(t,n,e){return va(Ba(t,n),Aa(t,n),e.north(),Ca(),Ta(t,{bottom:2}),"layout-n")},za=function(t,n,e){return va(Ba(t,n),Ma(t),e.south(),ka(),Ta(t,{top:3}),"layout-s")},Na=function(t,n,e){return va((o=t).x+o.width,Fa(t,n),e.east(),Oa(),Ta(t,{left:0}),"layout-e");var o},La=function(t,n,e){return va((o=n,t.x-o.width),Fa(t,n),e.west(),_a(),Ta(t,{right:1}),"layout-w");var o},ja=function(){return[Ia,Ra,Va,Pa,za,Ha,Na,La]},Ua=function(){return[Ra,Ia,Pa,Va,za,Ha,Na,La]},Wa=function(){return[Va,Pa,Ia,Ra,Ha,za]},Ga=function(){return[Pa,Va,Ra,Ia,Ha,za]},Xa=function(){return[Ia,Ra,Va,Pa,za,Ha]},Ya=function(){return[Ra,Ia,Pa,Va,za,Ha]},qa=/* */Object.freeze({__proto__:null,events:function(c){return Jo([Zo(So(),function(r,t){var n,e,i=c.channels,o=kt(i),u=t,a=(n=o,(e=u).universal?n:H(n,function(t){return M(e.channels,t)}));ot(a,function(t){var n=i[t],e=n.schema,o=Tn("channel["+t+"] data\nReceiver: "+jr(r.element),e,u.data);n.onReceive(r,o)})})])}}),Ka=[Ln("channels",Cn(it.value,sn([da("onReceive"),te("schema",An())])))],Ja=function(e,o,r){return cr(function(t,n){r(t,e,o)})},$a=function(t,n,e,o,r,i){var u=sn(t),a=Zn(n,[Kn("config",sn(t))]);return tc(u,a,n,e,o,r,i)},Qa=function(r,i,u){var t,n,e,o,a,c;return t=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];var o=[e].concat(t);return e.config({name:at(r)}).fold(function(){throw new Error("We could not find any behaviour configuration for: "+r+". Using API: "+u)},function(t){var n=Array.prototype.slice.call(o,1);return i.apply(undefined,[e,t.config,t.state].concat(n))})},n=u,e=i.toString(),o=e.indexOf(")")+1,a=e.indexOf("("),c=e.substring(a+1,o-1).split(/,\s*/),t.toFunctionAnnotation=function(){return{name:n,parameters:oi(c.slice(0,1).concat(c.slice(3)))}},t},Za=function(t){return{key:t,value:undefined}},tc=function(e,t,o,r,n,i,u){var a=function(t){return It(t,o)?t[o]():st.none()},c=_t(n,function(t,n){return Qa(o,t,n)}),s=_t(i,ri),l=nt(nt(nt({},s),c),{revoke:g(Za,o),config:function(t){var n=Tn(o+"-config",e,t);return{key:o,value:{config:n,me:l,configAsRaw:Nt(function(){return Tn(o+"-config",e,t)}),initialConfig:t,state:u}}},schema:function(){return t},exhibit:function(t,e){return a(t).bind(function(n){return Mt(r,"exhibit").map(function(t){return t(e,n.config,n.state)})}).getOr(fi({}))},name:function(){return o},handlers:function(t){return a(t).map(function(t){return Mt(r,"events").getOr(function(){return{}})(t.config,t.state)}).getOr({})}});return l},nc=Jt,ec=sn([Nn("fields"),Nn("name"),te("active",{}),te("apis",{}),te("state",ci),te("extra",{})]),oc=function(t){var n=Tn("Creating behaviour: "+t.name,ec,t);return $a(n.fields,n.name,n.active,n.apis,n.extra,n.state)},rc=sn([Nn("branchKey"),Nn("branches"),Nn("name"),te("active",{}),te("apis",{}),te("state",ci),te("extra",{})]),ic=function(t){var n,e,o,r,i,u,a,c,s=Tn("Creating behaviour: "+t.name,rc,t);return n=Dn(s.branchKey,s.branches),e=s.name,o=s.active,r=s.apis,i=s.extra,u=s.state,c=Zn(e,[Kn("config",a=n)]),tc(a,c,e,o,r,i,u)},uc=at(undefined),ac=oc({fields:Ka,name:"receiving",active:qa}),cc=/* */Object.freeze({__proto__:null,exhibit:function(t,n){return fi({classes:[],styles:n.useFixed()?{}:{position:"relative"}})}}),sc=function(t){return t.dom.focus()},lc=function(t){return void 0===t&&(t=fe.fromDom(document)),st.from(t.dom.activeElement).map(fe.fromDom)},fc=function(n){return lc(Ri(n)).filter(function(t){return n.dom.contains(t.dom)})},dc=function(t,e){var o=Ri(e),n=lc(o).bind(function(n){var r,i,t=function(t){return je(n,t)};return t(e)?st.some(e):(r=t,(i=function(t){for(var n=0;n<t.childNodes.length;n++){var e=fe.fromDom(t.childNodes[n]);if(r(e))return st.some(e);var o=i(t.childNodes[n]);if(o.isSome())return o}return st.none()})(e.dom))}),r=t(e);return n.each(function(n){lc(o).filter(function(t){return je(t,n)}).fold(function(){sc(n)},$)}),r},mc=function(t,n,e,o,r){return{position:t,left:n,top:e,right:o,bottom:r}},gc=function(t,n){var e=function(t){return t+"px"};Xi(t,{position:st.some(n.position),left:n.left.map(e),top:n.top.map(e),right:n.right.map(e),bottom:n.bottom.map(e)})},pc=Rt([{none:[]},{relative:["x","y","width","height"]},{fixed:["x","y","width","height"]}]),hc=function(t,n,e,o,r,i){var u,a,c,s,l,f,d,m,g,p=n.x-e,h=n.y-o,v=r-(p+n.width),b=i-(h+n.height),y=st.some(p),x=st.some(h),w=st.some(v),S=st.some(b),k=st.none();return u=n.direction,a=function(){return mc(t,y,x,k,k)},c=function(){return mc(t,k,x,w,k)},s=function(){return mc(t,y,k,k,S)},l=function(){return mc(t,k,k,w,S)},f=function(){return mc(t,y,x,k,k)},d=function(){return mc(t,y,k,k,S)},m=function(){return mc(t,y,x,k,k)},g=function(){return mc(t,k,x,w,k)},u.fold(a,c,s,l,f,d,m,g)},vc=function(t,n){var e=g(Fu,n),o=t.fold(e,e,function(){var t=Tu();return Fu(n).translate(-t.left,-t.top)}),r=ku(n),i=pu(n);return Iu(o.left,o.top,r,i)},bc=function(t,n,e){var o=vu(n,e);return t.fold(at(o),at(o),function(){var t=Tu();return o.translate(-t.left,-t.top)})},yc=(pc.none,pc.relative),xc=pc.fixed,wc=function(t,n,e,o){var r=t+n;return o<r?e:r<e?o:r},Sc=function(t,n,e){return Math.min(Math.max(t,n),e)},kc=Rt([{fit:["reposition"]},{nofit:["reposition","deltaW","deltaH"]}]),Cc=function(t,n,e,o){var r,i,u,a,c,s,l,f,d,m,g,p,h,v,b,y,x,w,S,k,C,O,_,T,E,B,D,A,M,F,I,R,V,P=t.x,H=t.y,z=t.bubble.offset,N=z.left,L=z.top,j=(r=o,i=t.boundsRestriction,u=z,c=(a=function(n,e){var o="top"===n||"bottom"===n?u.top:u.left;return Mt(i,n).bind(ct).bind(function(t){return"left"===n||"top"===n?e<=t?st.some(t):st.none():t<=e?st.some(t):st.none()}).map(function(t){return t+o}).getOr(e)})("left",r.x),s=a("top",r.y),l=a("right",r.right),f=a("bottom",r.bottom),Iu(c,s,l-c,f-s)),U=j.y,W=j.bottom,G=j.x,X=j.right,Y=H+L,q=(d=P+N,m=Y,g=n,p=e,v=(h=j).x,b=h.y,y=h.width,x=h.height,S=b<=m,k=(w=v<=d)&&S,C=d+g<=v+y&&m+p<=b+x,O=Math.abs(Math.min(g,w?v+y-d:v-(d+g))),_=Math.abs(Math.min(p,S?b+x-m:b-(m+p))),T=Math.max(h.x,h.right-g),E=Math.max(h.y,h.bottom-p),{originInBounds:k,sizeInBounds:C,limitX:Sc(d,h.x,T),limitY:Sc(m,h.y,E),deltaW:O,deltaH:_}),K=q.originInBounds,J=q.sizeInBounds,$=q.limitX,Q=q.limitY,Z=q.deltaW,tt=q.deltaH,nt=at(Q+tt-U),et=at(W-Q),ot=(B=t.direction,A=D=et,M=nt,B.fold(D,D,M,M,D,M,A,A)),rt=at($+Z-G),it=at(X-$),ut={x:$,y:Q,width:Z,height:tt,maxHeight:ot,maxWidth:(F=t.direction,R=I=it,V=rt,F.fold(I,V,I,V,R,R,I,V)),direction:t.direction,classes:{on:t.bubble.classesOn,off:t.bubble.classesOff},label:t.label,candidateYforTest:Y};return K&&J?kc.fit(ut):kc.nofit(ut,Z,tt)},Oc=function(t,n,e,o){$i(n,"max-height"),$i(n,"max-width");var r,i,u,a,c,s,l,f,d,m={width:ku(r=n),height:pu(r)};return i=o.preference,u=t,a=m,c=e,s=o.bounds,l=a.width,f=a.height,d=function(t,o,r,i){var n=t(u,a,c);return Cc(n,l,f,s).fold(kc.fit,function(t,n,e){return i<e||r<n?kc.nofit(t,n,e):kc.nofit(o,r,i)})},N(i,function(t,n){var e=g(d,n);return t.fold(kc.fit,e)},kc.nofit({x:u.x,y:u.y,width:a.width,height:a.height,maxHeight:a.height,maxWidth:a.width,direction:ya(),classes:{on:[],off:[]},label:"none",candidateYforTest:u.y},-1,-1)).fold(ct,ct)},_c=function(t,n,e){var o,r;gc(t,(o=e.origin,r=n,o.fold(function(){return mc("absolute",st.some(r.x),st.some(r.y),st.none(),st.none())},function(t,n,e,o){return hc("absolute",r,t,n,e,o)},function(t,n,e,o){return hc("fixed",r,t,n,e,o)})))},Tc=function(t,n){var e,o,r;e=t,o=Math.floor(n),r=mu.max(e,o,["margin-top","border-top-width","padding-top","padding-bottom","border-bottom-width","margin-bottom"]),Wi(e,"max-height",r+"px")},Ec=at(function(t,n){Tc(t,n),Gi(t,{"overflow-x":"hidden","overflow-y":"auto"})}),Bc=at(function(t,n){Tc(t,n)}),Dc=function(t,n,e){return t[n]===undefined?e:t[n]},Ac=function(t,n,e,o,r,i){var u,a=Dc(i,"maxHeightFunction",Ec()),c=Dc(i,"maxWidthFunction",$),s=t.anchorBox,l=t.origin,f={bounds:(u=l,r.fold(function(){return u.fold(Pu,Pu,Iu)},function(e){return u.fold(e,e,function(){var t=e(),n=bc(u,t.x,t.y);return Iu(n.left,n.top,t.width,t.height)})})),origin:l,preference:o,maxHeightFunction:a,maxWidthFunction:c};Mc(s,n,e,f)},Mc=function(t,n,e,o){var r,i,u,a,c,s,l=Oc(t,n,e,o);_c(n,l,o),r=n,i=l.classes,Di(r,i.off),Bi(r,i.on),u=n,a=l,(0,o.maxHeightFunction)(u,a.maxHeight),c=n,s=l,(0,o.maxWidthFunction)(c,s.maxWidth)},Fc=["valignCentre","alignLeft","alignRight","alignCentre","top","bottom","left","right"],Ic=function(t,n,e){var r=function(t){return Mt(e,t).getOr([])},o=function(t,n,e){var o=X(Fc,e);return{offset:vu(t,n),classesOn:U(e,r),classesOff:U(o,r)}};return{southeast:function(){return o(-t,n,["top","alignLeft"])},southwest:function(){return o(t,n,["top","alignRight"])},south:function(){return o(-t/2,n,["top","alignCentre"])},northeast:function(){return o(-t,-n,["bottom","alignLeft"])},northwest:function(){return o(t,-n,["bottom","alignRight"])},north:function(){return o(-t/2,-n,["bottom","alignCentre"])},east:function(){return o(t,-n/2,["valignCentre","left"])},west:function(){return o(-t,-n/2,["valignCentre","right"])},innerNorthwest:function(){return o(-t,n,["top","alignRight"])},innerNortheast:function(){return o(t,n,["top","alignLeft"])},innerNorth:function(){return o(-t/2,n,["top","alignCentre"])},innerSouthwest:function(){return o(-t,-n,["bottom","alignRight"])},innerSoutheast:function(){return o(t,-n,["bottom","alignLeft"])},innerSouth:function(){return o(-t/2,-n,["bottom","alignCentre"])},innerWest:function(){return o(t,-n/2,["valignCentre","right"])},innerEast:function(){return o(-t,-n/2,["valignCentre","left"])}}},Rc=function(){return Ic(0,0,{})},Vc=function(n,e){return function(t){return"rtl"===Pc(t)?e:n}},Pc=function(t){return"rtl"===Yi(t,"direction")?"rtl":"ltr"};(Qu=$u=$u||{}).TopToBottom="toptobottom",Qu.BottomToTop="bottomtotop";var Hc="data-alloy-vertical-dir",zc=function(t){return Uu(t,function(t){return dr(t)&&Pr(t,"data-alloy-vertical-dir")===$u.BottomToTop})},Nc=function(){return Zn("layouts",[Nn("onLtr"),Nn("onRtl"),qn("onBottomLtr"),qn("onBottomRtl")])},Lc=function(n,t,e,o,r,i,u){var a=u.map(zc).getOr(!1),c=t.layouts.map(function(t){return t.onLtr(n)}),s=t.layouts.map(function(t){return t.onRtl(n)}),l=a?t.layouts.bind(function(t){return t.onBottomLtr.map(function(t){return t(n)})}).or(c).getOr(r):c.getOr(e),f=a?t.layouts.bind(function(t){return t.onBottomRtl.map(function(t){return t(n)})}).or(s).getOr(i):s.getOr(o);return Vc(l,f)(n)},jc=[Nn("hotspot"),qn("bubble"),te("overrides",{}),Nc(),ga("placement",function(t,n,e){var o=n.hotspot,r=vc(e,o.element),i=Lc(t.element,n,Xa(),Ya(),Wa(),Ga(),st.some(n.hotspot.element));return st.some({anchorBox:r,bubble:n.bubble.getOr(Rc()),overrides:n.overrides,layouts:i,placer:st.none()})})],Uc=[Nn("x"),Nn("y"),te("height",0),te("width",0),te("bubble",Rc()),te("overrides",{}),Nc(),ga("placement",function(t,n,e){var o=bc(e,n.x,n.y),r=Iu(o.left,o.top,n.width,n.height),i=Lc(t.element,n,ja(),Ua(),ja(),Ua(),st.none());return st.some({anchorBox:r,bubble:n.bubble,overrides:n.overrides,layouts:i,placer:st.none()})})],Wc=function(t,n,e,o){return{start:t,soffset:n,finish:e,foffset:o}},Gc=Rt([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),Xc=(Gc.before,Gc.on,Gc.after,function(t){return t.fold(ct,ct,ct)}),Yc=Rt([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),qc={domRange:Yc.domRange,relative:Yc.relative,exact:Yc.exact,exactFromRange:function(t){return Yc.exact(t.start,t.soffset,t.finish,t.foffset)},getWin:function(t){var n=t.match({domRange:function(t){return fe.fromDom(t.startContainer)},relative:function(t,n){return Xc(t)},exact:function(t,n,e,o){return t}});return yr(n)},range:Wc},Kc=function(t,n,e){var o,r,i=t.document.createRange();return o=i,n.fold(function(t){o.setStartBefore(t.dom)},function(t,n){o.setStart(t.dom,n)},function(t){o.setStartAfter(t.dom)}),r=i,e.fold(function(t){r.setEndBefore(t.dom)},function(t,n){r.setEnd(t.dom,n)},function(t){r.setEndAfter(t.dom)}),i},Jc=function(t,n,e,o,r){var i=t.document.createRange();return i.setStart(n.dom,e),i.setEnd(o.dom,r),i},$c=function(t){return{left:t.left,top:t.top,right:t.right,bottom:t.bottom,width:t.width,height:t.height}},Qc=Rt([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]),Zc=function(t,n,e){return n(fe.fromDom(e.startContainer),e.startOffset,fe.fromDom(e.endContainer),e.endOffset)},ts=function(t,n){var r,e,o,i=(r=t,n.match({domRange:function(t){return{ltr:at(t),rtl:st.none}},relative:function(t,n){return{ltr:Nt(function(){return Kc(r,t,n)}),rtl:Nt(function(){return st.some(Kc(r,n,t))})}},exact:function(t,n,e,o){return{ltr:Nt(function(){return Jc(r,t,n,e,o)}),rtl:Nt(function(){return st.some(Jc(r,e,o,t,n))})}}}));return(o=(e=i).ltr()).collapsed?e.rtl().filter(function(t){return!1===t.collapsed}).map(function(t){return Qc.rtl(fe.fromDom(t.endContainer),t.endOffset,fe.fromDom(t.startContainer),t.startOffset)}).getOrThunk(function(){return Zc(0,Qc.ltr,o)}):Zc(0,Qc.ltr,o)};Qc.ltr,Qc.rtl;var ns,es,os,rs=(ns=mr,es="text",{get:function(t){if(!ns(t))throw new Error("Can only get "+es+" value of a "+es+" node");return os(t).getOr("")},getOption:os=function(t){return ns(t)?st.from(t.dom.nodeValue):st.none()},set:function(t,n){if(!ns(t))throw new Error("Can only set raw "+es+" value of a "+es+" node");t.dom.nodeValue=n}}),is=function(t){return rs.getOption(t)},us=["img","br"],as=function(t){return is(t).filter(function(t){return 0!==t.trim().length||-1<t.indexOf("\xa0")}).isSome()||M(us,lr(t))},cs=function(t,i){var u=function(t){for(var n=kr(t),e=n.length-1;0<=e;e--){var o=n[e];if(i(o))return st.some(o);var r=u(o);if(r.isSome())return r}return st.none()};return u(t)},ss=function(t,n){return e=n,r=(o=t)===undefined?document:o.dom,Le(r)?[]:V(r.querySelectorAll(e),fe.fromDom);var e,o,r},ls=function(t,n,e,o){var r,i,u,a,c,s=(i=n,u=e,a=o,(c=hr(r=t).dom.createRange()).setStart(r.dom,i),c.setEnd(u.dom,a),c),l=je(t,e)&&n===o;return s.collapsed&&!l},fs=function(t){if(0<t.rangeCount){var n=t.getRangeAt(0),e=t.getRangeAt(t.rangeCount-1);return st.some(Wc(fe.fromDom(n.startContainer),n.startOffset,fe.fromDom(e.endContainer),e.endOffset))}return st.none()},ds=function(t){if(null===t.anchorNode||null===t.focusNode)return fs(t);var n=fe.fromDom(t.anchorNode),e=fe.fromDom(t.focusNode);return ls(n,t.anchorOffset,e,t.focusOffset)?st.some(Wc(n,t.anchorOffset,e,t.focusOffset)):fs(t)},ms=function(t){return n=t,st.from(n.getSelection()).filter(function(t){return 0<t.rangeCount}).bind(ds);var n},gs=function(t,n){var i,e,o,r,u=ts(i=t,n).match({ltr:function(t,n,e,o){var r=i.document.createRange();return r.setStart(t.dom,n),r.setEnd(e.dom,o),r},rtl:function(t,n,e,o){var r=i.document.createRange();return r.setStart(e.dom,o),r.setEnd(t.dom,n),r}});return o=(e=u).getClientRects(),0<(r=0<o.length?o[0]:e.getBoundingClientRect()).width||0<r.height?st.some(r).map($c):st.none()},ps=function(t,n){return{element:t,offset:n}},hs=function(t,n){var e=kr(t);if(0===e.length)return ps(t,n);if(n<e.length)return ps(e[n],0);var o,r=e[e.length-1],i=mr(r)?(o=r,rs.get(o).length):kr(r).length;return ps(r,i)},vs=Rt([{screen:["point"]},{absolute:["point","scrollLeft","scrollTop"]}]),bs=function(t){return t.fold(ct,function(t,n,e){return t.translate(-n,-e)})},ys=function(t){return t.fold(ct,ct)},xs=function(t){return N(t,function(t,n){return t.translate(n.left,n.top)},vu(0,0))},ws=function(t){var n=V(t,ys);return xs(n)},Ss=vs.screen,ks=vs.absolute,Cs=function(t,n,e){var o,r,i=hr(t.element),u=Tu(i),a=(o=t,r=yr(e.root).dom,st.from(r.frameElement).map(fe.fromDom).filter(function(t){var n=hr(t),e=hr(o.element);return je(n,e)}).map(yu).getOr(u));return ks(a,u.left,u.top)},Os=function(t,n,e,o){var r=t,i=n,u=e,a=o;t<0&&(r=0,u=e+t),n<0&&(i=0,a=o+n);var c=Ss(vu(r,i));return st.some({point:c,width:u,height:a})},_s=function(t,l,f,d,m){return t.map(function(t){var n,e,o,r=[l,t.point],i=(n=function(){return ws(r)},e=function(){return ws(r)},o=function(){return t=V(r,bs),xs(t);var t},d.fold(n,e,o)),u={x:i.left,y:i.top,width:t.width,height:t.height},a=(f.showAbove?Wa:Xa)(),c=(f.showAbove?Ga:Ya)(),s=Lc(m,f,a,c,a,c,st.none());return{anchorBox:u,bubble:f.bubble.getOr(Rc()),overrides:f.overrides,layouts:s,placer:st.none()}})},Ts=function(t,n){return mr(t)?{element:t,offset:n}:hs(t,n)},Es=function(t,n){return n.getSelection.getOrThunk(function(){return function(){return ms(t)}})().map(function(t){var n=Ts(t.start,t.soffset),e=Ts(t.finish,t.foffset);return qc.range(n.element,n.offset,e.element,e.offset)})},Bs=[qn("getSelection"),Nn("root"),qn("bubble"),Nc(),te("overrides",{}),te("showAbove",!1),ga("placement",function(t,n,e){var o=yr(n.root).dom,r=Cs(t,0,n),i=Es(o,n).bind(function(t){return gs(o,qc.exactFromRange(t)).orThunk(function(){var n=fe.fromText("\ufeff");return Or(t.start,n),gs(o,qc.exact(n,0,n,1)).map(function(t){return Ar(n),t})}).bind(function(t){return Os(t.left,t.top,t.width,t.height)})}),u=Es(o,n).bind(function(t){return dr(t.start)?st.some(t.start):wr(t.start)}).getOr(t.element);return _s(i,r,n,e,u)})],Ds=[Nn("node"),Nn("root"),qn("bubble"),Nc(),te("overrides",{}),te("showAbove",!1),ga("placement",function(r,i,u){var a=Cs(r,0,i);return i.node.bind(function(t){var n=t.dom.getBoundingClientRect(),e=Os(n.left,n.top,n.width,n.height),o=i.node.getOr(r.element);return _s(e,a,i,u,o)})})],As=function(t){return t.x+t.width},Ms=function(t,n){return t.x-n.width},Fs=function(t,n){return t.y-n.height+t.height},Is=function(t){return t.y},Rs=function(t,n,e){return va(As(t),Is(t),e.southeast(),ya(),Ta(t,{left:0,top:2}),"link-layout-se")},Vs=function(t,n,e){return va(Ms(t,n),Is(t),e.southwest(),xa(),Ta(t,{right:1,top:2}),"link-layout-sw")},Ps=function(t,n,e){return va(As(t),Fs(t,n),e.northeast(),wa(),Ta(t,{left:0,bottom:3}),"link-layout-ne")},Hs=function(t,n,e){return va(Ms(t,n),Fs(t,n),e.northwest(),Sa(),Ta(t,{right:1,bottom:3}),"link-layout-nw")},zs=function(){return[Rs,Vs,Ps,Hs]},Ns=function(){return[Vs,Rs,Hs,Ps]},Ls=[Nn("item"),Nc(),te("overrides",{}),ga("placement",function(t,n,e){var o=vc(e,n.item.element),r=Lc(t.element,n,zs(),Ns(),zs(),Ns(),st.none());return st.some({anchorBox:o,bubble:Rc(),overrides:n.overrides,layouts:r,placer:st.none()})})],js=Dn("anchor",{selection:Bs,node:Ds,hotspot:jc,submenu:Ls,makeshift:Uc}),Us=function(t,n,e,o,r){var i={anchorBox:e.anchorBox,origin:n};Ac(i,r.element,e.bubble,e.layouts,o,e.overrides)},Ws=function(t,n,e,o,r,i){var u=i.map(Ru);return Gs(t,n,e,o,r,u)},Gs=function(c,s,t,n,l,f){var d=Tn("positioning anchor.info",js,n);dc(function(){Wi(l.element,"position","fixed");var t=Ki(l.element,"visibility");Wi(l.element,"visibility","hidden");var n,e,o,r,i=s.useFixed()?(r=document.documentElement,xc(0,0,r.clientWidth,r.clientHeight)):(e=yu((n=c).element),o=n.element.dom.getBoundingClientRect(),yc(e.left,e.top,o.width,o.height)),u=d.placement,a=f.map(at).or(s.getBounds);u(c,d,i).each(function(t){t.placer.getOr(Us)(c,i,t,a,l)}),t.fold(function(){$i(l.element,"visibility")},function(t){Wi(l.element,"visibility",t)}),Ki(l.element,"left").isNone()&&Ki(l.element,"top").isNone()&&Ki(l.element,"right").isNone()&&Ki(l.element,"bottom").isNone()&&Ki(l.element,"position").is("fixed")&&$i(l.element,"position")},l.element)},Xs=/* */Object.freeze({__proto__:null,position:function(t,n,e,o,r){Ws(t,n,e,o,r,st.none())},positionWithin:Ws,positionWithinBounds:Gs,getMode:function(t,n,e){return n.useFixed()?"fixed":"absolute"}}),Ys=[te("useFixed",c),qn("getBounds")],qs=oc({fields:Ys,name:"positioning",active:cc,apis:Xs}),Ks=function(t){Wo(t,Ro());var n=t.components();ot(n,Ks)},Js=function(t){var n=t.components();ot(n,Js),Wo(t,Io())},$s=function(t,n){Er(t.element,n.element)},Qs=function(n,t){var e,o=n.components();ot((e=n).components(),function(t){return Ar(t.element)}),Dr(e.element),e.syncComponents();var r=X(o,t);ot(r,function(t){Ks(t),n.getSystem().removeFromWorld(t)}),ot(t,function(t){t.getSystem().isConnected()?$s(n,t):(n.getSystem().addToWorld(t),$s(n,t),zi(n.element)&&Js(t)),n.syncComponents()})},Zs=function(t,n){tl(t,n,Er)},tl=function(t,n,e){t.getSystem().addToWorld(n),e(t.element,n.element),zi(t.element)&&Js(n),t.syncComponents()},nl=function(t){Ks(t),Ar(t.element),t.getSystem().removeFromWorld(t)},el=function(n){var t=xr(n.element).bind(function(t){return n.getSystem().getByDom(t).toOptional()});nl(n),t.each(function(t){t.syncComponents()})},ol=function(t){var n=t.components();ot(n,nl),Dr(t.element),t.syncComponents()},rl=function(t,n){il(t,n,Er)},il=function(t,n,e){e(t,n.element);var o=kr(n.element);ot(o,function(t){n.getByDom(t).each(Js)})},ul=function(n){var t=kr(n.element);ot(t,function(t){n.getByDom(t).each(Ks)}),Ar(n.element)},al=function(n,t,e,o){e.get().each(function(t){ol(n)});var r=t.getAttachPoint(n);Zs(r,n);var i=n.getSystem().build(o);return Zs(n,i),e.set(i),i},cl=function(t,n,e,o){var r=al(t,n,e,o);return n.onOpen(t,r),r},sl=function(n,e,o){o.get().each(function(t){ol(n),el(n),e.onClose(n,t),o.clear()})},ll=function(t,n,e){return e.isOpen()},fl=function(t,n,e){var o,r,i,u,a=n.getAttachPoint(t);Wi(t.element,"position",qs.getMode(a)),o=t,r="visibility",i=n.cloakVisibilityAttr,u="hidden",Ki(o.element,r).fold(function(){Nr(o.element,i)},function(t){Vr(o.element,i,t)}),Wi(o.element,r,u)},dl=function(t,n,e){var o,r,i,u;o=t.element,F(["top","left","right","bottom"],function(t){return Ki(o,t).isSome()})||$i(t.element,"position"),r=t,i="visibility",u=n.cloakVisibilityAttr,Hr(r.element,u).fold(function(){return $i(r.element,i)},function(t){return Wi(r.element,i,t)})},ml=/* */Object.freeze({__proto__:null,cloak:fl,decloak:dl,open:cl,openWhileCloaked:function(t,n,e,o,r){fl(t,n),cl(t,n,e,o),r(),dl(t,n)},close:sl,isOpen:ll,isPartOf:function(n,e,t,o){return ll(0,0,t)&&t.get().exists(function(t){return e.isPartOf(n,t,o)})},getState:function(t,n,e){return e.get()},setContent:function(t,n,e,o){return e.get().map(function(){return al(t,n,e,o)})}}),gl=/* */Object.freeze({__proto__:null,events:function(e,o){return Jo([Zo(To(),function(t,n){sl(t,e,o)})])}}),pl=[la("onOpen"),la("onClose"),Nn("isPartOf"),Nn("getAttachPoint"),te("cloakVisibilityAttr","data-precloak-visibility")],hl=oc({fields:pl,name:"sandboxing",active:gl,apis:ml,state:/* */Object.freeze({__proto__:null,init:function(){var n=se(st.none()),t=at("not-implemented");return si({readState:t,isOpen:function(){return n.get().isSome()},clear:function(){n.set(st.none())},set:function(t){n.set(st.some(t))},get:function(){return n.get()}})}})}),vl=at("dismiss.popups"),bl=at("reposition.popups"),yl=at("mouse.released"),xl=sn([te("isExtraPart",c),Zn("fireEventInstead",[te("event",Vo())])]),wl=function(t){var e=Tn("Dismissal",xl,t),n={};return n[vl()]={schema:sn([Nn("target")]),onReceive:function(n,t){hl.isOpen(n)&&(hl.isPartOf(n,t.target)||e.isExtraPart(n,t.target)||e.fireEventInstead.fold(function(){return hl.close(n)},function(t){return Wo(n,t.event)}))}},n},Sl=sn([Zn("fireEventInstead",[te("event",Po())]),Wn("doReposition")]),kl=function(t){var e=Tn("Reposition",Sl,t),n={};return n[bl()]={onReceive:function(n){hl.isOpen(n)&&e.fireEventInstead.fold(function(){return e.doReposition(n)},function(t){return Wo(n,t.event)})}},n},Cl=function(t,n,e){n.store.manager.onLoad(t,n,e)},Ol=function(t,n,e){n.store.manager.onUnload(t,n,e)},_l=/* */Object.freeze({__proto__:null,onLoad:Cl,onUnload:Ol,setValue:function(t,n,e,o){n.store.manager.setValue(t,n,e,o)},getValue:function(t,n,e){return n.store.manager.getValue(t,n,e)},getState:function(t,n,e){return e}}),Tl=/* */Object.freeze({__proto__:null,events:function(e,o){var t=e.resetOnDom?[ur(function(t,n){Cl(t,e,o)}),ar(function(t,n){Ol(t,e,o)})]:[Ja(e,o,Cl)];return Jo(t)}}),El=function(){var t=se(null);return si({set:t.set,get:t.get,isNotSet:function(){return null===t.get()},clear:function(){t.set(null)},readState:function(){return{mode:"memory",value:t.get()}}})},Bl=function(){var i=se({}),u=se({});return si({readState:function(){return{mode:"dataset",dataByValue:i.get(),dataByText:u.get()}},lookup:function(t){return Mt(i.get(),t).orThunk(function(){return Mt(u.get(),t)})},update:function(t){var n=i.get(),e=u.get(),o={},r={};ot(t,function(n){o[n.value]=n,Mt(n,"meta").each(function(t){Mt(t,"text").each(function(t){r[t]=n})})}),i.set(nt(nt({},n),o)),u.set(nt(nt({},e),r))},clear:function(){i.set({}),u.set({})}})},Dl=/* */Object.freeze({__proto__:null,memory:El,dataset:Bl,manual:function(){return si({readState:function(){}})},init:function(t){return t.store.manager.state(t)}}),Al=function(t,n,e,o){var r=n.store;e.update([o]),r.setValue(t,o),n.onSetValue(t,o)},Ml=[qn("initialValue"),Nn("getFallbackEntry"),Nn("getDataKey"),Nn("setValue"),ga("manager",{setValue:Al,getValue:function(t,n,e){var o=n.store,r=o.getDataKey(t);return e.lookup(r).fold(function(){return o.getFallbackEntry(r)},function(t){return t})},onLoad:function(n,e,o){e.store.initialValue.each(function(t){Al(n,e,o,t)})},onUnload:function(t,n,e){e.clear()},state:Bl})],Fl=[Nn("getValue"),te("setValue",$),qn("initialValue"),ga("manager",{setValue:function(t,n,e,o){n.store.setValue(t,o),n.onSetValue(t,o)},getValue:function(t,n,e){return n.store.getValue(t)},onLoad:function(n,e,t){e.store.initialValue.each(function(t){e.store.setValue(n,t)})},onUnload:$,state:ci.init})],Il=[qn("initialValue"),ga("manager",{setValue:function(t,n,e,o){e.set(o),n.onSetValue(t,o)},getValue:function(t,n,e){return e.get()},onLoad:function(t,n,e){n.store.initialValue.each(function(t){e.isNotSet()&&e.set(t)})},onUnload:function(t,n,e){e.clear()},state:El})],Rl=[ne("store",{mode:"memory"},Dn("mode",{memory:Il,manual:Fl,dataset:Ml})),la("onSetValue"),te("resetOnDom",!1)],Vl=oc({fields:Rl,name:"representing",active:Tl,apis:_l,extra:{setValueFrom:function(t,n){var e=Vl.getValue(n);Vl.setValue(t,e)}},state:Dl}),Pl=function(o,t){return ae(o,{},V(t,function(t){return n=t.name(),e="Cannot configure "+t.name()+" for "+o,vn(n,n,Wt(),cn(function(t){return bt("The field: "+n+" is forbidden. "+e)}));var n,e}).concat([ce("dump",ct)]))},Hl=function(t){return t.dump},zl=function(t,n){return nt(nt({},t.dump),nc(n))},Nl=Pl,Ll=zl,jl="placeholder",Ul=Rt([{single:["required","valueThunk"]},{multiple:["required","valueThunks"]}]),Wl=function(t){return Ft(t,"uiType")},Gl=function(t,n,e,o){return Wl(e)&&e.uiType===jl?(i=e,u=o,(r=t).exists(function(t){return t!==i.owner})?Ul.single(!0,at(i)):Mt(u,i.name).fold(function(){throw new Error("Unknown placeholder component: "+i.name+"\nKnown: ["+kt(u)+"]\nNamespace: "+r.getOr("none")+"\nSpec: "+JSON.stringify(i,null,2))},function(t){return t.replace()})):Ul.single(!1,at(e));var r,i,u},Xl=function(i,u,a,c){return Gl(i,0,a,c).fold(function(t,n){var e=Wl(a)?n(u,a.config,a.validated):n(u),o=Mt(e,"components").getOr([]),r=U(o,function(t){return Xl(i,u,t,c)});return[nt(nt({},e),{components:r})]},function(t,n){if(Wl(a)){var e=n(u,a.config,a.validated);return a.validated.preprocess.getOr(ct)(e)}return n(u)})},Yl=function(n,e,t,o){var r,i,u,a=_t(o,function(t,n){return o=t,r=!1,{name:at(e=n),required:function(){return o.fold(function(t,n){return t},function(t,n){return t})},used:function(){return r},replace:function(){if(r)throw new Error("Trying to use the same placeholder more than once: "+e);return r=!0,o}};var e,o,r}),c=(r=n,i=e,u=a,U(t,function(t){return Xl(r,i,t,u)}));return Ot(a,function(t){if(!1===t.used()&&t.required())throw new Error("Placeholder: "+t.name()+" was not found in components list\nNamespace: "+n.getOr("none")+"\nComponents: "+JSON.stringify(e.components,null,2))}),c},ql=Ul.single,Kl=Ul.multiple,Jl=at(jl),$l=Rt([{required:["data"]},{external:["data"]},{optional:["data"]},{group:["data"]}]),Ql=te("factory",{sketch:ct}),Zl=te("schema",[]),tf=Nn("name"),nf=vn("pname","pname",Gt(function(t){return"<alloy."+Xr(t.name)+">"}),An()),ef=ce("schema",function(){return[qn("preprocess")]}),of=te("defaults",at({})),rf=te("overrides",at({})),uf=ln([Ql,Zl,tf,nf,of,rf]),af=ln([Ql,Zl,tf,of,rf]),cf=ln([Ql,Zl,tf,nf,of,rf]),sf=ln([Ql,ef,tf,Nn("unit"),nf,of,rf]),lf=function(t){return t.fold(st.some,st.none,st.some,st.some)},ff=function(t){var n=function(t){return t.name};return t.fold(n,n,n,n)},df=function(e,o){return function(t){var n=Tn("Converting part type",o,t);return e(n)}},mf=df($l.required,uf),gf=df($l.external,af),pf=df($l.optional,cf),hf=df($l.group,sf),vf=at("entirety"),bf=/* */Object.freeze({__proto__:null,required:mf,external:gf,optional:pf,group:hf,asNamedPart:lf,name:ff,asCommon:function(t){return t.fold(ct,ct,ct,ct)},original:vf}),yf=function(t,n,e,o){return Ht(n.defaults(t,e,o),e,{uid:t.partUids[n.name]},n.overrides(t,e,o))},xf=function(r,t){var n={};return ot(t,function(t){lf(t).each(function(e){var o=wf(r,e.pname);n[e.name]=function(t){var n=Tn("Part: "+e.name+" in "+r,ln(e.schema),t);return nt(nt({},o),{config:t,validated:n})}})}),n},wf=function(t,n){return{uiType:Jl(),owner:t,name:n}},Sf=function(t,n,e){return{uiType:Jl(),owner:t,name:n,config:e,validated:{}}},kf=function(t){return U(t,function(t){return t.fold(st.none,st.some,st.none,st.none).map(function(t){return Gn(t.name,t.schema.concat([pa(vf())]))}).toArray()})},Cf=function(t){return V(t,ff)},Of=function(t,n,e){return o=n,i={},r={},ot(e,function(t){t.fold(function(o){i[o.pname]=ql(!0,function(t,n,e){return o.factory.sketch(yf(t,o,n,e))})},function(t){var n=o.parts[t.name];r[t.name]=at(t.factory.sketch(yf(o,t,n[vf()]),n))},function(o){i[o.pname]=ql(!1,function(t,n,e){return o.factory.sketch(yf(t,o,n,e))})},function(r){i[r.pname]=Kl(!0,function(n,t,e){var o=n[r.name];return V(o,function(t){return r.factory.sketch(Ht(r.defaults(n,t,e),t,r.overrides(n,t)))})})})}),{internals:at(i),externals:at(r)};var o,i,r},_f=function(t,n,e){return Yl(st.some(t),n,n.components,e)},Tf=function(t,n,e){var o=n.partUids[e];return t.getSystem().getByUid(o).toOptional()},Ef=function(t,n,e){return Tf(t,n,e).getOrDie("Could not find part: "+e)},Bf=function(t,n,e){var o={},r=n.partUids,i=t.getSystem();return ot(e,function(t){o[t]=at(i.getByUid(r[t]))}),o},Df=function(t,n){var e=t.getSystem();return _t(n.partUids,function(t,n){return at(e.getByUid(t))})},Af=function(t){return kt(t.partUids)},Mf=function(t,n,e){var o={},r=n.partUids,i=t.getSystem();return ot(e,function(t){o[t]=at(i.getByUid(r[t]).getOrDie())}),o},Ff=function(n,t){var e=Cf(t);return Jt(V(e,function(t){return{key:t,value:n+"-"+t}}))},If=function(n){return vn("partUids","partUids",Xt(function(t){return Ff(t.uid,n)}),An())},Rf=/* */Object.freeze({__proto__:null,generate:xf,generateOne:Sf,schemas:kf,names:Cf,substitutes:Of,components:_f,defaultUids:Ff,defaultUidsSchema:If,getAllParts:Df,getAllPartNames:Af,getPart:Tf,getPartOrDie:Ef,getParts:Bf,getPartsOrDie:Mf}),Vf=function(t,n,e,o,r){var i,u,a=(u=r,(0<(i=o).length?[Gn("parts",i)]:[]).concat([Nn("uid"),te("dom",{}),te("components",[]),pa("originalSpec"),te("debug.sketcher",{})]).concat(u));return Tn(t+" [SpecSchema]",sn(a.concat(n)),e)},Pf=function(t,n,e,o,r){var i=Hf(r),u=kf(e),a=If(e),c=Vf(t,n,i,u,[a]),s=Of(0,c,e);return o(c,_f(t,c,s.internals()),i,s.externals())},Hf=function(t){return Ft(t,"uid")?t:nt(nt({},t),{uid:Zr("uid")})};var zf,Nf,Lf=sn([Nn("name"),Nn("factory"),Nn("configFields"),te("apis",{}),te("extraApis",{})]),jf=sn([Nn("name"),Nn("factory"),Nn("configFields"),Nn("partFields"),te("apis",{}),te("extraApis",{})]),Uf=function(t){var i=Tn("Sketcher for "+t.name,Lf,t),n=_t(i.apis,ai),e=_t(i.extraApis,ri);return nt(nt({name:i.name,configFields:i.configFields,sketch:function(t){return n=i.name,e=i.configFields,o=i.factory,r=Hf(t),o(Vf(n,e,r,[],[]),r);var n,e,o,r}},n),e)},Wf=function(t){var n=Tn("Sketcher for "+t.name,jf,t),e=xf(n.name,n.partFields),o=_t(n.apis,ai),r=_t(n.extraApis,ri);return nt(nt({name:n.name,partFields:n.partFields,configFields:n.configFields,sketch:function(t){return Pf(n.name,n.configFields,n.partFields,n.factory,t)},parts:e},o),r)},Gf=function(t){for(var n=[],e=function(t){n.push(t)},o=0;o<t.length;o++)t[o].each(e);return n},Xf=function(t){return"input"===lr(t)&&"radio"!==Pr(t,"type")||"textarea"===lr(t)},Yf=/* */Object.freeze({__proto__:null,getCurrent:function(t,n,e){return n.find(t)}}),qf=[Nn("find")],Kf=oc({fields:qf,name:"composing",apis:Yf}),Jf=function(e,o,t,r){var n=ss(e.element,"."+o.highlightClass);ot(n,function(n){F(r,function(t){return t.element===n})||(Ti(n,o.highlightClass),e.getSystem().getByDom(n).each(function(t){o.onDehighlight(e,t),Wo(t,Uo())}))})},$f=function(t,n,e,o){Jf(t,n,0,[o]),Qf(t,n,e,o)||(Oi(o.element,n.highlightClass),n.onHighlight(t,o),Wo(o,jo()))},Qf=function(t,n,e,o){return Ei(o.element,n.highlightClass)},Zf=function(t,n,e,o){var r=ss(t.element,"."+n.itemClass);return st.from(r[o]).fold(function(){return it.error("No element found with index "+o)},t.getSystem().getByDom)},td=function(n,t,e){return Gu(n.element,"."+t.itemClass).bind(function(t){return n.getSystem().getByDom(t).toOptional()})},nd=function(n,t,e){var o=ss(n.element,"."+t.itemClass);return(0<o.length?st.some(o[o.length-1]):st.none()).bind(function(t){return n.getSystem().getByDom(t).toOptional()})},ed=function(e,n,t,o){var r=ss(e.element,"."+n.itemClass);return j(r,function(t){return Ei(t,n.highlightClass)}).bind(function(t){var n=wc(t,o,0,r.length-1);return e.getSystem().getByDom(r[n]).toOptional()})},od=function(n,t,e){var o=ss(n.element,"."+t.itemClass);return Gf(V(o,function(t){return n.getSystem().getByDom(t).toOptional()}))},rd=/* */Object.freeze({__proto__:null,dehighlightAll:function(t,n,e){return Jf(t,n,0,[])},dehighlight:function(t,n,e,o){Qf(t,n,e,o)&&(Ti(o.element,n.highlightClass),n.onDehighlight(t,o),Wo(o,Uo()))},highlight:$f,highlightFirst:function(n,e,o){td(n,e).each(function(t){$f(n,e,o,t)})},highlightLast:function(n,e,o){nd(n,e).each(function(t){$f(n,e,o,t)})},highlightAt:function(n,e,o,t){Zf(n,e,o,t).fold(function(t){throw new Error(t)},function(t){$f(n,e,o,t)})},highlightBy:function(n,e,o,t){var r=od(n,e);L(r,t).each(function(t){$f(n,e,o,t)})},isHighlighted:Qf,getHighlighted:function(n,t,e){return Gu(n.element,"."+t.highlightClass).bind(function(t){return n.getSystem().getByDom(t).toOptional()})},getFirst:td,getLast:nd,getPrevious:function(t,n,e){return ed(t,n,0,-1)},getNext:function(t,n,e){return ed(t,n,0,1)},getCandidates:od}),id=[Nn("highlightClass"),Nn("itemClass"),la("onHighlight"),la("onDehighlight")],ud=oc({fields:id,name:"highlighting",apis:rd}),ad=[8],cd=[9],sd=[13],ld=[27],fd=[32],dd=[37],md=[38],gd=[39],pd=[40],hd=function(t,n,e){var o=G(t.slice(0,n)),r=G(t.slice(n+1));return L(o.concat(r),e)},vd=function(t,n,e){var o=G(t.slice(0,n));return L(o,e)},bd=function(t,n,e){var o=t.slice(0,n),r=t.slice(n+1);return L(r.concat(o),e)},yd=function(t,n,e){var o=t.slice(n+1);return L(o,e)},xd=function(e){return function(t){var n=t.raw;return M(e,n.which)}},wd=function(t){return function(n){return W(t,function(t){return t(n)})}},Sd=function(t){return!0===t.raw.shiftKey},kd=function(t){return!0===t.raw.ctrlKey},Cd=x(Sd),Od=function(t,n){return{matches:t,classification:n}},_d=function(t,n,e){n.exists(function(n){return e.exists(function(t){return je(t,n)})})||Go(t,Ho(),{prevFocus:n,newFocus:e})},Td=function(){var r=function(t){return fc(t.element)};return{get:r,set:function(t,n){var e=r(t);t.getSystem().triggerFocus(n,t.element);var o=r(t);_d(t,e,o)}}},Ed=function(){var r=function(t){return ud.getHighlighted(t).map(function(t){return t.element})};return{get:r,set:function(n,t){var e=r(n);n.getSystem().getByDom(t).fold($,function(t){ud.highlight(n,t)});var o=r(n);_d(n,e,o)}}};(Nf=zf=zf||{}).OnFocusMode="onFocus",Nf.OnEnterOrSpaceMode="onEnterOrSpace",Nf.OnApiMode="onApi";var Bd,Dd=function(t,n,e,o,a){var c=function(n,e,t,o,r){var i,u,a=t(n,e,o,r);return i=a,u=e.event,L(i,function(t){return t.matches(u)}).map(function(t){return t.classification}).bind(function(t){return t(n,e,o,r)})},r={schema:function(){return t.concat([te("focusManager",Td()),ne("focusInside","onFocus",kn(function(t){return M(["onFocus","onEnterOrSpace","onApi"],t)?it.value(t):it.error("Invalid value for focusInside")})),ga("handler",r),ga("state",n),ga("sendFocusIn",a)])},processKey:c,toEvents:function(i,u){var t=i.focusInside!==zf.OnFocusMode?st.none():a(i).map(function(e){return Zo(yo(),function(t,n){e(t,i,u),n.stop()})}),n=[Zo(so(),function(o,r){c(o,r,e,i,u).fold(function(){var n,e,t;n=o,e=r,t=xd(fd.concat(sd))(e.event),i.focusInside===zf.OnEnterOrSpaceMode&&t&&qe(n,e)&&a(i).each(function(t){t(n,i,u),e.stop()})},function(t){r.stop()})}),Zo(lo(),function(t,n){c(t,n,o,i,u).each(function(t){n.stop()})})];return Jo(t.toArray().concat(n))}};return r},Ad=function(t){var n=[qn("onEscape"),qn("onEnter"),te("selector",'[data-alloy-tabstop="true"]:not(:disabled)'),te("firstTabstop",0),te("useTabstopAt",b),qn("visibilitySelector")].concat([t]),u=function(t,n){var e=t.visibilitySelector.bind(function(t){return Xu(n,t)}).getOr(n);return 0<gu(e)},e=function(n,e,t){var o,r,i;o=e,r=ss(n.element,o.selector),i=H(r,function(t){return u(o,t)}),st.from(i[o.firstTabstop]).each(function(t){e.focusManager.set(n,t)})},a=function(n,t,e,o,r){return r(t,e,function(t){return u(n=o,e=t)&&n.useTabstopAt(e);var n,e}).fold(function(){return o.cyclic?st.some(!0):st.none()},function(t){return o.focusManager.set(n,t),st.some(!0)})},r=function(n,t,e,o){var r,i,u=ss(n.element,e.selector);return r=n,(i=e).focusManager.get(r).bind(function(t){return Xu(t,i.selector)}).bind(function(t){return j(u,g(je,t)).bind(function(t){return a(n,u,t,e,o)})})},o=at([Od(wd([Sd,xd(cd)]),function(t,n,e){var o=e.cyclic?hd:vd;return r(t,0,e,o)}),Od(xd(cd),function(t,n,e){var o=e.cyclic?bd:yd;return r(t,0,e,o)}),Od(xd(ld),function(n,e,t){return t.onEscape.bind(function(t){return t(n,e)})}),Od(wd([Cd,xd(sd)]),function(n,e,t){return t.onEnter.bind(function(t){return t(n,e)})})]),i=at([]);return Dd(n,ci.init,o,i,function(){return st.some(e)})},Md=Ad(ce("cyclic",c)),Fd=Ad(ce("cyclic",b)),Id=function(t,n,e){return Xf(e)&&xd(fd)(n.event)?st.none():(Yo(t,e,ko()),st.some(!0))},Rd=function(t,n){return st.some(!0)},Vd=[te("execute",Id),te("useSpace",!1),te("useEnter",!0),te("useControlEnter",!1),te("useDown",!1)],Pd=function(t,n,e){return e.execute(t,n,t.element)},Hd=Dd(Vd,ci.init,function(t,n,e,o){var r=e.useSpace&&!Xf(t.element)?fd:[],i=e.useEnter?sd:[],u=e.useDown?pd:[],a=r.concat(i).concat(u);return[Od(xd(a),Pd)].concat(e.useControlEnter?[Od(wd([kd,xd(sd)]),Pd)]:[])},function(t,n,e,o){return e.useSpace&&!Xf(t.element)?[Od(xd(fd),Rd)]:[]},function(){return st.none()}),zd=function(){var e=se(st.none());return si({readState:function(){return e.get().map(function(t){return{numRows:String(t.numRows),numColumns:String(t.numColumns)}}).getOr({numRows:"?",numColumns:"?"})},setGridSize:function(t,n){e.set(st.some({numRows:t,numColumns:n}))},getNumRows:function(){return e.get().map(function(t){return t.numRows})},getNumColumns:function(){return e.get().map(function(t){return t.numColumns})}})},Nd=/* */Object.freeze({__proto__:null,flatgrid:zd,init:function(t){return t.state(t)}}),Ld=function(i){return function(t,n,e,o){var r=i(t.element);return Gd(r,t,n,e,o)}},jd=function(t,n){var e=Vc(t,n);return Ld(e)},Ud=function(t,n){var e=Vc(n,t);return Ld(e)},Wd=function(r){return function(t,n,e,o){return Gd(r,t,n,e,o)}},Gd=function(n,e,t,o,r){return o.focusManager.get(e).bind(function(t){return n(e.element,t,o,r)}).map(function(t){return o.focusManager.set(e,t),!0})},Xd=Wd,Yd=Wd,qd=Wd,Kd=function(t){return!((n=t.dom).offsetWidth<=0&&n.offsetHeight<=0);var n},Jd=function(t,n,e){var o,r=ss(t,e),i=H(r,Kd);return j(o=i,function(t){return je(t,n)}).map(function(t){return{index:t,candidates:o}})},$d=function(t,n){return j(t,function(t){return je(n,t)})},Qd=function(e,t,o,n){return n(Math.floor(t/o),t%o).bind(function(t){var n=t.row*o+t.column;return 0<=n&&n<e.length?st.some(e[n]):st.none()})},Zd=function(r,t,i,u,a){return Qd(r,t,u,function(t,n){var e=t===i-1?r.length-t*u:u,o=wc(n,a,0,e-1);return st.some({row:t,column:o})})},tm=function(i,t,u,a,c){return Qd(i,t,a,function(t,n){var e=wc(t,c,0,u-1),o=e===u-1?i.length-e*a:a,r=Sc(n,0,o-1);return st.some({row:e,column:r})})},nm=[Nn("selector"),te("execute",Id),fa("onEscape"),te("captureTab",!1),ha()],em=function(n,e,t){Gu(n.element,e.selector).each(function(t){e.focusManager.set(n,t)})},om=function(r){return function(t,n,e,o){return Jd(t,n,e.selector).bind(function(t){return r(t.candidates,t.index,o.getNumRows().getOr(e.initSize.numRows),o.getNumColumns().getOr(e.initSize.numColumns))})}},rm=function(t,n,e){return e.captureTab?st.some(!0):st.none()},im=om(function(t,n,e,o){return Zd(t,n,e,o,-1)}),um=om(function(t,n,e,o){return Zd(t,n,e,o,1)}),am=om(function(t,n,e,o){return tm(t,n,e,o,-1)}),cm=om(function(t,n,e,o){return tm(t,n,e,o,1)}),sm=at([Od(xd(dd),jd(im,um)),Od(xd(gd),Ud(im,um)),Od(xd(md),Xd(am)),Od(xd(pd),Yd(cm)),Od(wd([Sd,xd(cd)]),rm),Od(wd([Cd,xd(cd)]),rm),Od(xd(ld),function(t,n,e){return e.onEscape(t,n)}),Od(xd(fd.concat(sd)),function(n,e,o,t){return r=n,(i=o).focusManager.get(r).bind(function(t){return Xu(t,i.selector)}).bind(function(t){return o.execute(n,e,t)});var r,i})]),lm=at([Od(xd(fd),Rd)]),fm=Dd(nm,zd,sm,lm,function(){return st.some(em)}),dm=function(t,n,e,i){var u=function(t,n,e){var o,r=wc(n,i,0,e.length-1);return r===t?st.none():(o=e[r],"button"===lr(o)&&"disabled"===Pr(o,"disabled")?u(t,r,e):st.from(e[r]))};return Jd(t,e,n).bind(function(t){var n=t.index,e=t.candidates;return u(n,n,e)})},mm=[Nn("selector"),te("getInitial",st.none),te("execute",Id),fa("onEscape"),te("executeOnMove",!1),te("allowVertical",!0)],gm=function(n,e,o){return t=n,(r=o).focusManager.get(t).bind(function(t){return Xu(t,r.selector)}).bind(function(t){return o.execute(n,e,t)});var t,r},pm=function(n,e,t){e.getInitial(n).orThunk(function(){return Gu(n.element,e.selector)}).each(function(t){e.focusManager.set(n,t)})},hm=function(t,n,e){return dm(t,e.selector,n,-1)},vm=function(t,n,e){return dm(t,e.selector,n,1)},bm=function(r){return function(t,n,e,o){return r(t,n,e,o).bind(function(){return e.executeOnMove?gm(t,n,e):st.some(!0)})}},ym=function(t,n,e){return e.onEscape(t,n)},xm=at([Od(xd(fd),Rd)]),wm=Dd(mm,ci.init,function(t,n,e,o){var r=dd.concat(e.allowVertical?md:[]),i=gd.concat(e.allowVertical?pd:[]);return[Od(xd(r),bm(jd(hm,vm))),Od(xd(i),bm(Ud(hm,vm))),Od(xd(sd),gm),Od(xd(fd),gm),Od(xd(ld),ym)]},xm,function(){return st.some(pm)}),Sm=function(t,n,e){return st.from(t[n]).bind(function(t){return st.from(t[e]).map(function(t){return{rowIndex:n,columnIndex:e,cell:t}})})},km=function(t,n,e,o){var r=t[n].length,i=wc(e,o,0,r-1);return Sm(t,n,i)},Cm=function(t,n,e,o){var r=wc(e,o,0,t.length-1),i=t[r].length,u=Sc(n,0,i-1);return Sm(t,r,u)},Om=function(t,n,e,o){var r=t[n].length,i=Sc(e+o,0,r-1);return Sm(t,n,i)},_m=function(t,n,e,o){var r=Sc(e+o,0,t.length-1),i=t[r].length,u=Sc(n,0,i-1);return Sm(t,r,u)},Tm=[Gn("selectors",[Nn("row"),Nn("cell")]),te("cycles",!0),te("previousSelector",st.none),te("execute",Id)],Em=function(n,e,t){e.previousSelector(n).orThunk(function(){var t=e.selectors;return Gu(n.element,t.cell)}).each(function(t){e.focusManager.set(n,t)})},Bm=function(t,n){return function(e,o,i){var u=i.cycles?t:n;return Xu(o,i.selectors.row).bind(function(t){var n=ss(t,i.selectors.cell);return $d(n,o).bind(function(o){var r=ss(e,i.selectors.row);return $d(r,t).bind(function(t){var n,e=(n=i,V(r,function(t){return ss(t,n.selectors.cell)}));return u(e,t,o).map(function(t){return t.cell})})})})}},Dm=Bm(function(t,n,e){return km(t,n,e,-1)},function(t,n,e){return Om(t,n,e,-1)}),Am=Bm(function(t,n,e){return km(t,n,e,1)},function(t,n,e){return Om(t,n,e,1)}),Mm=Bm(function(t,n,e){return Cm(t,e,n,-1)},function(t,n,e){return _m(t,e,n,-1)}),Fm=Bm(function(t,n,e){return Cm(t,e,n,1)},function(t,n,e){return _m(t,e,n,1)}),Im=at([Od(xd(dd),jd(Dm,Am)),Od(xd(gd),Ud(Dm,Am)),Od(xd(md),Xd(Mm)),Od(xd(pd),Yd(Fm)),Od(xd(fd.concat(sd)),function(n,e,o){return fc(n.element).bind(function(t){return o.execute(n,e,t)})})]),Rm=at([Od(xd(fd),Rd)]),Vm=Dd(Tm,ci.init,Im,Rm,function(){return st.some(Em)}),Pm=[Nn("selector"),te("execute",Id),te("moveOnTab",!1)],Hm=function(n,e,o){return o.focusManager.get(n).bind(function(t){return o.execute(n,e,t)})},zm=function(n,e,t){Gu(n.element,e.selector).each(function(t){e.focusManager.set(n,t)})},Nm=function(t,n,e){return dm(t,e.selector,n,-1)},Lm=function(t,n,e){return dm(t,e.selector,n,1)},jm=at([Od(xd(md),qd(Nm)),Od(xd(pd),qd(Lm)),Od(wd([Sd,xd(cd)]),function(t,n,e,o){return e.moveOnTab?qd(Nm)(t,n,e,o):st.none()}),Od(wd([Cd,xd(cd)]),function(t,n,e,o){return e.moveOnTab?qd(Lm)(t,n,e,o):st.none()}),Od(xd(sd),Hm),Od(xd(fd),Hm)]),Um=at([Od(xd(fd),Rd)]),Wm=Dd(Pm,ci.init,jm,Um,function(){return st.some(zm)}),Gm=[fa("onSpace"),fa("onEnter"),fa("onShiftEnter"),fa("onLeft"),fa("onRight"),fa("onTab"),fa("onShiftTab"),fa("onUp"),fa("onDown"),fa("onEscape"),te("stopSpaceKeyup",!1),qn("focusIn")],Xm=Dd(Gm,ci.init,function(t,n,e){return[Od(xd(fd),e.onSpace),Od(wd([Cd,xd(sd)]),e.onEnter),Od(wd([Sd,xd(sd)]),e.onShiftEnter),Od(wd([Sd,xd(cd)]),e.onShiftTab),Od(wd([Cd,xd(cd)]),e.onTab),Od(xd(md),e.onUp),Od(xd(pd),e.onDown),Od(xd(dd),e.onLeft),Od(xd(gd),e.onRight),Od(xd(fd),e.onSpace),Od(xd(ld),e.onEscape)]},function(t,n,e){return e.stopSpaceKeyup?[Od(xd(fd),Rd)]:[]},function(t){return t.focusIn}),Ym=Md.schema(),qm=Fd.schema(),Km=wm.schema(),Jm=fm.schema(),$m=Vm.schema(),Qm=Hd.schema(),Zm=Wm.schema(),tg=Xm.schema(),ng=ic({branchKey:"mode",branches:/* */Object.freeze({__proto__:null,acyclic:Ym,cyclic:qm,flow:Km,flatgrid:Jm,matrix:$m,execution:Qm,menu:Zm,special:tg}),name:"keying",active:{events:function(t,n){return t.handler.toEvents(t,n)}},apis:{focusIn:function(n,e,o){e.sendFocusIn(e).fold(function(){n.getSystem().triggerFocus(n.element,n.element)},function(t){t(n,e,o)})},setGridSize:function(t,n,e,o,r){It(e,"setGridSize")?e.setGridSize(o,r):console.error("Layout does not support setGridSize")}},state:Nd}),eg=function(t,n,e,o){var r=t.getSystem().build(o);tl(t,r,e)},og=function(t,n,e,o){var r=rg(t);L(r,function(t){return je(o.element,t.element)}).each(el)},rg=function(t,n){return t.components()},ig=function(n,t,e,r,o){var i=rg(n);return st.from(i[r]).map(function(t){return og(n,0,0,t),o.each(function(t){eg(n,0,function(t,n){var e,o;o=n,Cr(e=t,r).fold(function(){Er(e,o)},function(t){Or(t,o)})},t)}),t})},ug=oc({fields:[],name:"replacing",apis:/* */Object.freeze({__proto__:null,append:function(t,n,e,o){eg(t,0,Er,o)},prepend:function(t,n,e,o){eg(t,0,Tr,o)},remove:og,replaceAt:ig,replaceBy:function(n,t,e,o,r){var i=rg(n);return j(i,o).bind(function(t){return ig(n,0,0,t,r)})},set:function(n,t,e,o){dc(function(){var t=V(o,n.getSystem().build);Qs(n,t)},n.element)},contents:rg})}),ag=function(t,n){var e,o;return{key:t,value:{config:{},me:(e=t,o=Jo(n),oc({fields:[Nn("enabled")],name:e,active:{events:at(o)}})),configAsRaw:at({}),initialConfig:{},state:ci}}},cg=function(t,n){n.ignore||(sc(t.element),n.onFocus(t))},sg=/* */Object.freeze({__proto__:null,focus:cg,blur:function(t,n){n.ignore||t.element.dom.blur()},isFocused:function(t){return n=t.element,e=Ri(n).dom,n.dom===e.activeElement;var n,e}}),lg=/* */Object.freeze({__proto__:null,exhibit:function(t,n){var e=n.ignore?{}:{attributes:{tabindex:"-1"}};return fi(e)},events:function(e){return Jo([Zo(yo(),function(t,n){cg(t,e),n.stop()})].concat(e.stopMousedown?[Zo(eo(),function(t,n){n.event.prevent()})]:[]))}}),fg=[la("onFocus"),te("stopMousedown",!1),te("ignore",!1)],dg=oc({fields:fg,name:"focusing",active:lg,apis:sg}),mg=function(t,n,e){var o=n.aria;o.update(t,o,e.get())},gg=function(n,t,e){t.toggleClass.each(function(t){(e.get()?Oi:Ti)(n.element,t)})},pg=function(t,n,e){bg(t,n,e,!e.get())},hg=function(t,n,e){e.set(!0),gg(t,n,e),mg(t,n,e)},vg=function(t,n,e){e.set(!1),gg(t,n,e),mg(t,n,e)},bg=function(t,n,e,o){(o?hg:vg)(t,n,e)},yg=function(t,n,e){bg(t,n,e,n.selected)},xg=/* */Object.freeze({__proto__:null,onLoad:yg,toggle:pg,isOn:function(t,n,e){return e.get()},on:hg,off:vg,set:bg}),wg=/* */Object.freeze({__proto__:null,exhibit:function(){return fi({})},events:function(t,n){var e,o,r,i=(e=t,o=n,r=pg,sr(function(t){r(t,e,o)})),u=Ja(t,n,yg);return Jo(rt([t.toggleOnExecute?[i]:[],[u]]))}}),Sg=function(t,n,e){Vr(t.element,"aria-expanded",e)},kg=[te("selected",!1),qn("toggleClass"),te("toggleOnExecute",!0),ne("aria",{mode:"none"},Dn("mode",{pressed:[te("syncWithExpanded",!1),ga("update",function(t,n,e){Vr(t.element,"aria-pressed",e),n.syncWithExpanded&&Sg(t,n,e)})],checked:[ga("update",function(t,n,e){Vr(t.element,"aria-checked",e)})],expanded:[ga("update",Sg)],selected:[ga("update",function(t,n,e){Vr(t.element,"aria-selected",e)})],none:[ga("update",$)]}))],Cg=oc({fields:kg,name:"toggling",active:wg,apis:xg,state:(Bd=!1,{init:function(){var n=se(Bd);return{get:function(){return n.get()},set:function(t){return n.set(t)},clear:function(){return n.set(Bd)},readState:function(){return n.get()}}}})}),Og=function(){var t=function(t,n){n.stop(),Xo(t)};return[Zo(go(),t),Zo(Oo(),t),rr(Qe()),rr(eo())]},_g=function(t){return Jo(rt([t.map(function(e){return sr(function(t,n){e(t),n.stop()})}).toArray(),Og()]))},Tg="alloy.item-hover",Eg="alloy.item-focus",Bg=function(t){(fc(t.element).isNone()||dg.isFocused(t))&&(dg.isFocused(t)||dg.focus(t),Go(t,Tg,{item:t}))},Dg=function(t){Go(t,Eg,{item:t})},Ag=at(Tg),Mg=at(Eg),Fg=[Nn("data"),Nn("components"),Nn("dom"),te("hasSubmenu",!1),qn("toggling"),Nl("itemBehaviours",[Cg,dg,ng,Vl]),te("ignoreFocus",!1),te("domModification",{}),ga("builder",function(t){return{dom:t.dom,domModification:nt(nt({},t.domModification),{attributes:nt(nt(nt({role:t.toggling.isSome()?"menuitemcheckbox":"menuitem"},t.domModification.attributes),{"aria-haspopup":t.hasSubmenu}),t.hasSubmenu?{"aria-expanded":!1}:{})}),behaviours:Ll(t.itemBehaviours,[t.toggling.fold(Cg.revoke,function(t){return Cg.config(nt({aria:{mode:"checked"}},t))}),dg.config({ignore:t.ignoreFocus,stopMousedown:t.ignoreFocus,onFocus:function(t){Dg(t)}}),ng.config({mode:"execution"}),Vl.config({store:{mode:"memory",initialValue:t.data}}),ag("item-type-events",w(Og(),[Zo(uo(),Bg),Zo(Co(),dg.focus)]))]),components:t.components,eventOrder:t.eventOrder}}),te("eventOrder",{})],Ig=[Nn("dom"),Nn("components"),ga("builder",function(t){return{dom:t.dom,components:t.components,events:Jo([(n=Co(),Zo(n,function(t,n){n.stop()}))])};var n})],Rg=function(){return"item-widget"},Vg=at([mf({name:"widget",overrides:function(n){return{behaviours:nc([Vl.config({store:{mode:"manual",getValue:function(t){return n.data},setValue:function(){}}})])}}})]),Pg=[Nn("uid"),Nn("data"),Nn("components"),Nn("dom"),te("autofocus",!1),te("ignoreFocus",!1),Nl("widgetBehaviours",[Vl,dg,ng]),te("domModification",{}),If(Vg()),ga("builder",function(e){var t=Of(Rg(),e,Vg()),n=_f(Rg(),e,t.internals()),o=function(t){return Tf(t,e,"widget").map(function(t){return ng.focusIn(t),t})},r=function(t,n){return Xf(n.event.target)||e.autofocus&&n.setSource(t.element),st.none()};return{dom:e.dom,components:n,domModification:e.domModification,events:Jo([sr(function(t,n){o(t).each(function(t){n.stop()})}),Zo(uo(),Bg),Zo(Co(),function(t,n){e.autofocus?o(t):dg.focus(t)})]),behaviours:Ll(e.widgetBehaviours,[Vl.config({store:{mode:"memory",initialValue:e.data}}),dg.config({ignore:e.ignoreFocus,onFocus:function(t){Dg(t)}}),ng.config({mode:"special",focusIn:e.autofocus?function(t){o(t)}:uc(),onLeft:r,onRight:r,onEscape:function(t,n){return dg.isFocused(t)||e.autofocus?(e.autofocus&&n.setSource(t.element),st.none()):(dg.focus(t),st.some(!0))}})])}})],Hg=Dn("type",{widget:Pg,item:Fg,separator:Ig}),zg=at([hf({factory:{sketch:function(t){var n=Tn("menu.spec item",Hg,t);return n.builder(n)}},name:"items",unit:"item",defaults:function(t,n){return n.hasOwnProperty("uid")?n:nt(nt({},n),{uid:Zr("item")})},overrides:function(t,n){return{type:n.type,ignoreFocus:t.fakeFocus,domModification:{classes:[t.markers.item]}}}})]),Ng=at([Nn("value"),Nn("items"),Nn("dom"),Nn("components"),te("eventOrder",{}),Pl("menuBehaviours",[ud,Vl,Kf,ng]),ne("movement",{mode:"menu",moveOnTab:!0},Dn("mode",{grid:[ha(),ga("config",function(t,n){return{mode:"flatgrid",selector:"."+t.markers.item,initSize:{numColumns:n.initSize.numColumns,numRows:n.initSize.numRows},focusManager:t.focusManager}})],matrix:[ga("config",function(t,n){return{mode:"matrix",selectors:{row:n.rowSelector,cell:"."+t.markers.item},focusManager:t.focusManager}}),Nn("rowSelector")],menu:[te("moveOnTab",!0),ga("config",function(t,n){return{mode:"menu",selector:"."+t.markers.item,moveOnTab:n.moveOnTab,focusManager:t.focusManager}})]})),Ln("markers",ia()),te("fakeFocus",!1),te("focusManager",Td()),la("onHighlight")]),Lg=at("alloy.menu-focus"),jg=Wf({name:"Menu",configFields:Ng(),partFields:zg(),factory:function(t,n,e,o){return{uid:t.uid,dom:t.dom,markers:t.markers,behaviours:zl(t.menuBehaviours,[ud.config({highlightClass:t.markers.selectedItem,itemClass:t.markers.item,onHighlight:t.onHighlight}),Vl.config({store:{mode:"memory",initialValue:t.value}}),Kf.config({find:st.some}),ng.config(t.movement.config(t,t.movement))]),events:Jo([Zo(Mg(),function(n,e){var t=e.event;n.getSystem().getByDom(t.target).each(function(t){ud.highlight(n,t),e.stop(),Go(n,Lg(),{menu:n,item:t})})}),Zo(Ag(),function(t,n){var e=n.event.item;ud.highlight(t,e)})]),components:n,eventOrder:t.eventOrder,domModification:{attributes:{role:"menu"}}}}}),Ug=function(e,o,r,t){return Mt(r,t).bind(function(t){return Mt(e,t).bind(function(t){var n=Ug(e,o,r,t);return st.some([t].concat(n))})}).getOr([])},Wg=function(t,n){var e={};Ot(t,function(t,n){ot(t,function(t){e[t]=n})});var o=n,r=Tt(n,function(t,n){return{k:t,v:n}}),i=_t(r,function(t,n){return[n].concat(Ug(e,o,r,n))});return _t(e,function(t){return Mt(i,t).getOr([t])})},Gg=function(t){return"prepared"===t.type?st.some(t.menu):st.none()},Xg={init:function(){var i=se({}),u=se({}),a=se({}),c=se(st.none()),s=se({}),r=function(t,o,r){return e(t).bind(function(n){return e=t,Dt(i.get(),function(t,n){return t===e}).bind(function(t){return o(t).map(function(t){return{triggeredMenu:n,triggeringItem:t,triggeringPath:r}})});var e})},e=function(t){return n(t).bind(Gg)},n=function(t){return Mt(u.get(),t)},l=function(t){return Mt(i.get(),t)};return{setMenuBuilt:function(t,n){var e;u.set(nt(nt({},u.get()),((e={})[t]={type:"prepared",menu:n},e)))},setContents:function(t,n,e,o){c.set(st.some(t)),i.set(e),u.set(n),s.set(o);var r=Wg(o,e);a.set(r)},expand:function(e){return Mt(i.get(),e).map(function(t){var n=Mt(a.get(),e).getOr([]);return[t].concat(n)})},refresh:function(t){return Mt(a.get(),t)},collapse:function(t){return Mt(a.get(),t).bind(function(t){return 1<t.length?st.some(t.slice(1)):st.none()})},lookupMenu:n,lookupItem:l,otherMenus:function(t){var n=s.get();return X(kt(n),t)},getPrimary:function(){return c.get().bind(e)},getMenus:function(){return u.get()},clear:function(){i.set({}),u.set({}),a.set({}),c.set(st.none())},isClear:function(){return c.get().isNone()},getTriggeringPath:function(t,o){var n=H(l(t).toArray(),function(t){return e(t).isSome()});return Mt(a.get(),t).bind(function(t){var e=G(n.concat(t));return function(t){for(var n=[],e=0;e<t.length;e++){var o=t[e];if(!o.isSome())return st.none();n.push(o.getOrDie())}return st.some(n)}(U(e,function(t,n){return r(t,o,e.slice(0,n+1)).fold(function(){return c.get().is(t)?[]:[st.none()]},function(t){return[st.some(t)]})}))})}}},extractPreparedMenu:Gg},Yg=at("collapse-item"),qg=Uf({name:"TieredMenu",configFields:[ma("onExecute"),ma("onEscape"),da("onOpenMenu"),da("onOpenSubmenu"),la("onRepositionMenu"),la("onCollapseMenu"),te("highlightImmediately",!0),Gn("data",[Nn("primary"),Nn("menus"),Nn("expansions")]),te("fakeFocus",!1),la("onHighlight"),la("onHover"),aa(),Nn("dom"),te("navigateOnHover",!0),te("stayInDom",!1),Pl("tmenuBehaviours",[ng,ud,Kf,ug]),te("eventOrder",{})],apis:{collapseMenu:function(t,n){t.collapseMenu(n)},highlightPrimary:function(t,n){t.highlightPrimary(n)},repositionMenus:function(t,n){t.repositionMenus(n)}},factory:function(a,t){var c,n,i=se(st.none()),s=Xg.init(),e=function(t){var o,r,n,e=(o=t,r=a.data.primary,n=a.data.menus,_t(n,function(t,n){var e=function(){return jg.sketch(nt(nt({},t),{value:n,markers:a.markers,fakeFocus:a.fakeFocus,onHighlight:a.onHighlight,focusManager:(a.fakeFocus?Ed:Td)()}))};return n===r?{type:"prepared",menu:o.getSystem().build(e())}:{type:"notbuilt",nbMenu:e}})),i=u();return s.setContents(a.data.primary,e,a.data.expansions,i),s.getPrimary()},l=function(t){return Vl.getValue(t).value},u=function(t){return _t(a.data.menus,function(t,n){return U(t.items,function(t){return"separator"===t.type?[]:[t.data.value]})})},f=function(n,t){ud.highlight(n,t),ud.getHighlighted(t).orThunk(function(){return ud.getFirst(t)}).each(function(t){Yo(n,t.element,Co())})},d=function(n,t){return Gf(V(t,function(t){return n.lookupMenu(t).bind(function(t){return"prepared"===t.type?st.some(t.menu):st.none()})}))},m=function(n,t,e){var o=d(t,t.otherMenus(e));ot(o,function(t){Di(t.element,[a.markers.backgroundMenu]),a.stayInDom||ug.remove(n,t)})},g=function(t,o){var r,n=(r=t,i.get().getOrThunk(function(){var e={},t=ss(r.element,"."+a.markers.item),n=H(t,function(t){return"true"===Pr(t,"aria-haspopup")});return ot(n,function(t){r.getSystem().getByDom(t).each(function(t){var n=l(t);e[n]=t})}),i.set(st.some(e)),e}));Ot(n,function(t,n){var e=M(o,n);Vr(t.element,"aria-expanded",e)})},p=function(o,r,i){return st.from(i[0]).bind(function(t){return r.lookupMenu(t).bind(function(t){if("notbuilt"===t.type)return st.none();var n=t.menu,e=d(r,i.slice(1));return ot(e,function(t){Oi(t.element,a.markers.backgroundMenu)}),zi(n.element)||ug.append(o,fu(n)),Di(n.element,[a.markers.backgroundMenu]),f(o,n),m(o,r,i),st.some(n)})})};(n=c=c||{})[n.HighlightSubmenu=0]="HighlightSubmenu",n[n.HighlightParent=1]="HighlightParent";var h=function(r,i,u){void 0===u&&(u=c.HighlightSubmenu);var t=l(i);return s.expand(t).bind(function(o){return g(r,o),st.from(o[0]).bind(function(e){return s.lookupMenu(e).bind(function(t){var n=function(t,n,e){if("notbuilt"!==e.type)return e.menu;var o=t.getSystem().build(e.nbMenu());return s.setMenuBuilt(n,o),o}(r,e,t);return zi(n.element)||ug.append(r,fu(n)),a.onOpenSubmenu(r,i,n,G(o)),u===c.HighlightSubmenu?(ud.highlightFirst(n),p(r,s,o)):(ud.dehighlightAll(n),st.some(i))})})})},o=function(n,e){var t=l(e);return s.collapse(t).bind(function(t){return g(n,t),p(n,s,t).map(function(t){return a.onCollapseMenu(n,e,t),t})})},r=function(e){return function(n,t){return Xu(t.getSource(),"."+a.markers.item).bind(function(t){return n.getSystem().getByDom(t).toOptional().bind(function(t){return e(n,t).map(function(){return!0})})})}},v=Jo([Zo(Lg(),function(e,o){var t=o.event.item;s.lookupItem(l(t)).each(function(){var t=o.event.menu;ud.highlight(e,t);var n=l(o.event.item);s.refresh(n).each(function(t){return m(e,s,t)})})}),sr(function(n,t){var e=t.event.target;n.getSystem().getByDom(e).each(function(t){0===l(t).indexOf("collapse-item")&&o(n,t),h(n,t,c.HighlightSubmenu).fold(function(){a.onExecute(n,t)},function(){})})}),ur(function(n,t){e(n).each(function(t){ug.append(n,fu(t)),a.onOpenMenu(n,t),a.highlightImmediately&&f(n,t)})})].concat(a.navigateOnHover?[Zo(Ag(),function(t,n){var e,o,r=n.event.item;e=t,o=l(r),s.refresh(o).bind(function(t){return g(e,t),p(e,s,t)}),h(t,r,c.HighlightParent),a.onHover(t,r)})]:[])),b=function(t){return ud.getHighlighted(t).bind(ud.getHighlighted)},y={collapseMenu:function(n){b(n).each(function(t){o(n,t)})},highlightPrimary:function(n){s.getPrimary().each(function(t){f(n,t)})},repositionMenus:function(o){s.getPrimary().bind(function(n){return b(o).bind(function(t){var n=l(t),e=At(s.getMenus()),o=Gf(V(e,Xg.extractPreparedMenu));return s.getTriggeringPath(n,function(t){return e=t,Q(o,function(t){if(!t.getSystem().isConnected())return st.none();var n=ud.getCandidates(t);return L(n,function(t){return l(t)===e})});var e})}).map(function(t){return{primary:n,triggeringPath:t}})}).fold(function(){var t;t=o,st.from(t.components()[0]).filter(function(t){return"menu"===Pr(t.element,"role")}).each(function(t){a.onRepositionMenu(o,t,[])})},function(t){var n=t.primary,e=t.triggeringPath;a.onRepositionMenu(o,n,e)})}};return{uid:a.uid,dom:a.dom,markers:a.markers,behaviours:zl(a.tmenuBehaviours,[ng.config({mode:"special",onRight:r(function(t,n){return Xf(n.element)?st.none():h(t,n,c.HighlightSubmenu)}),onLeft:r(function(t,n){return Xf(n.element)?st.none():o(t,n)}),onEscape:r(function(t,n){return o(t,n).orThunk(function(){return a.onEscape(t,n).map(function(){return t})})}),focusIn:function(n,t){s.getPrimary().each(function(t){Yo(n,t.element,Co())})}}),ud.config({highlightClass:a.markers.selectedMenu,itemClass:a.markers.menu}),Kf.config({find:function(t){return ud.getHighlighted(t)}}),ug.config({})]),eventOrder:a.eventOrder,apis:y,events:v}},extraApis:{tieredData:function(t,n,e){return{primary:t,menus:n,expansions:e}},singleData:function(t,n){return{primary:t,menus:Kt(t,n),expansions:{}}},collapseItem:function(t){return{value:Xr(Yg()),meta:{text:t}}}}}),Kg=Uf({name:"InlineView",configFields:[Nn("lazySink"),la("onShow"),la("onHide"),Qn("onEscape"),Pl("inlineBehaviours",[hl,Vl,ac]),Zn("fireDismissalEventInstead",[te("event",Vo())]),Zn("fireRepositionEventInstead",[te("event",Po())]),te("getRelated",st.none),te("isExtraPart",c),te("eventOrder",st.none)],factory:function(m,t){var o=function(t,n,e,o){r(t,n,e,function(){return o.map(Ru)})},r=function(t,n,e,o){var r=m.lazySink(t).getOrDie();hl.openWhileCloaked(t,e,function(){return qs.positionWithinBounds(r,n,t,o())}),Vl.setValue(t,st.some({mode:"position",anchor:n,getBounds:o}))},i=function(t,n,e,o){var r,i,u,a,c,s,l,f,d=(r=m,i=t,u=n,c=o,s=function(){return r.lazySink(i)},l="horizontal"===(a=e).type?{layouts:{onLtr:Xa,onRtl:Ya}}:{},f=function(t){return 2===t.length?l:{}},qg.sketch({dom:{tag:"div"},data:a.data,markers:a.menu.markers,highlightImmediately:a.menu.highlightImmediately,onEscape:function(){return hl.close(i),r.onEscape.map(function(t){return t(i)}),st.some(!0)},onExecute:function(){return st.some(!0)},onOpenMenu:function(t,n){qs.positionWithinBounds(s().getOrDie(),u,n,c())},onOpenSubmenu:function(t,n,e,o){var r=s().getOrDie();qs.position(r,nt({anchor:"submenu",item:n},f(o)),e)},onRepositionMenu:function(t,n,e){var o=s().getOrDie();qs.positionWithinBounds(o,u,n,c()),ot(e,function(t){var n=f(t.triggeringPath);qs.position(o,nt({anchor:"submenu",item:t.triggeringItem},n),t.triggeredMenu)})}}));hl.open(t,d),Vl.setValue(t,st.some({mode:"menu",menu:d}))},n=function(e){hl.isOpen(e)&&Vl.getValue(e).each(function(t){switch(t.mode){case"menu":hl.getState(e).each(function(t){qg.repositionMenus(t)});break;case"position":var n=m.lazySink(e).getOrDie();qs.positionWithinBounds(n,t.anchor,e,t.getBounds())}})},e={setContent:function(t,n){hl.setContent(t,n)},showAt:function(t,n,e){o(t,n,e,st.none())},showWithin:o,showWithinBounds:r,showMenuAt:function(t,n,e){i(t,n,e,function(){return st.none()})},showMenuWithinBounds:i,hide:function(t){hl.isOpen(t)&&(Vl.setValue(t,st.none()),hl.close(t))},getContent:function(t){return hl.getState(t)},reposition:n,isOpen:hl.isOpen};return{uid:m.uid,dom:m.dom,behaviours:zl(m.inlineBehaviours,[hl.config({isPartOf:function(t,n,e){return Ku(n,e)||(o=t,r=e,m.getRelated(o).exists(function(t){return Ku(t,r)}));var o,r},getAttachPoint:function(t){return m.lazySink(t).getOrDie()},onOpen:function(t){m.onShow(t)},onClose:function(t){m.onHide(t)}}),Vl.config({store:{mode:"memory",initialValue:st.none()}}),ac.config({channels:nt(nt({},wl(nt({isExtraPart:t.isExtraPart},m.fireDismissalEventInstead.map(function(t){return{fireEventInstead:{event:t.event}}}).getOr({})))),kl(nt(nt({},m.fireRepositionEventInstead.map(function(t){return{fireEventInstead:{event:t.event}}}).getOr({})),{doReposition:n})))})]),eventOrder:m.eventOrder,apis:e}},apis:{showAt:function(t,n,e,o){t.showAt(n,e,o)},showWithin:function(t,n,e,o,r){t.showWithin(n,e,o,r)},showWithinBounds:function(t,n,e,o,r){t.showWithinBounds(n,e,o,r)},showMenuAt:function(t,n,e,o){t.showMenuAt(n,e,o)},showMenuWithinBounds:function(t,n,e,o,r){t.showMenuWithinBounds(n,e,o,r)},hide:function(t,n){t.hide(n)},isOpen:function(t,n){return t.isOpen(n)},getContent:function(t,n){return t.getContent(n)},setContent:function(t,n,e){t.setContent(n,e)},reposition:function(t,n){t.reposition(n)}}}),Jg=function(t){return t.x},$g=function(t,n){return t.x+t.width/2-n.width/2},Qg=function(t,n){return t.x+t.width-n.width},Zg=function(t){return t.y},tp=function(t,n){return t.y+t.height-n.height},np=function(t,n,e){return va(Qg(t,n),tp(t,n),e.innerSoutheast(),Sa(),Ta(t,{right:0,bottom:3}),"layout-inner-se")},ep=function(t,n,e){return va(Jg(t),tp(t,n),e.innerSouthwest(),wa(),Ta(t,{left:1,bottom:3}),"layout-inner-sw")},op=function(t,n,e){return va(Qg(t,n),Zg(t),e.innerNortheast(),xa(),Ta(t,{right:0,top:2}),"layout-inner-ne")},rp=function(t,n,e){return va(Jg(t),Zg(t),e.innerNorthwest(),ya(),Ta(t,{left:1,top:2}),"layout-inner-nw")},ip=function(t,n,e){return va($g(t,n),Zg(t),e.innerNorth(),ka(),Ta(t,{top:2}),"layout-inner-n")},up=function(t,n,e){return va($g(t,n),tp(t,n),e.innerSouth(),Ca(),Ta(t,{bottom:3}),"layout-inner-s")},ap=tinymce.util.Tools.resolve("tinymce.util.Delay"),cp=Uf({name:"Button",factory:function(t){var n=_g(t.action),e=t.dom.tag,o=function(n){return Mt(t.dom,"attributes").bind(function(t){return Mt(t,n)})};return{uid:t.uid,dom:t.dom,components:t.components,events:n,behaviours:Ll(t.buttonBehaviours,[dg.config({}),ng.config({mode:"execution",useSpace:!0,useEnter:!0})]),domModification:{attributes:function(){if("button"!==e)return{role:o("role").getOr("button")};var t=o("type").getOr("button"),n=o("role").map(function(t){return{role:t}}).getOr({});return nt({type:t},n)}()},eventOrder:t.eventOrder}},configFields:[te("uid",undefined),Nn("dom"),te("components",[]),Nl("buttonBehaviours",[dg,ng]),qn("action"),qn("role"),te("eventOrder",{})]}),sp=function(t){var n=t.uid!==undefined&&It(t,"uid")?t.uid:Zr("memento");return{get:function(t){return t.getSystem().getByUid(n).getOrDie()},getOpt:function(t){return t.getSystem().getByUid(n).toOptional()},asSpec:function(){return nt(nt({},t),{uid:n})}}},lp=function(t){return st.from(t()["temporary-placeholder"]).getOr("!not found!")},fp=function(t,n){return st.from(n()[t.toLowerCase()]).getOrThunk(function(){return lp(n)})},dp={success:"checkmark",error:"warning",err:"error",warning:"warning",warn:"warning",info:"info"},mp=Uf({name:"Notification",factory:function(n){var t,e,o=sp({dom:{tag:"p",innerHtml:n.translationProvider(n.text)},behaviours:nc([ug.config({})])}),r=function(t){return{dom:{tag:"div",classes:["tox-bar"],attributes:{style:"width: "+t+"%"}}}},i=function(t){return{dom:{tag:"div",classes:["tox-text"],innerHtml:t+"%"}}},u=sp({dom:{tag:"div",classes:n.progress?["tox-progress-bar","tox-progress-indicator"]:["tox-progress-bar"]},components:[{dom:{tag:"div",classes:["tox-bar-container"]},components:[r(0)]},i(0)],behaviours:nc([ug.config({})])}),a={updateProgress:function(t,n){t.getSystem().isConnected()&&u.getOpt(t).each(function(t){ug.set(t,[{dom:{tag:"div",classes:["tox-bar-container"]},components:[r(n)]},i(n)])})},updateText:function(t,n){var e;t.getSystem().isConnected()&&(e=o.get(t),ug.set(e,[au(n)]))}},c=rt([n.icon.toArray(),n.level.toArray(),n.level.bind(function(t){return st.from(dp[t])}).toArray()]),s=sp(cp.sketch({dom:{tag:"button",classes:["tox-notification__dismiss","tox-button","tox-button--naked","tox-button--icon"]},components:[{dom:{tag:"div",classes:["tox-icon"],innerHtml:fp("close",n.iconProvider),attributes:{"aria-label":n.translationProvider("Close")}}}],action:function(t){n.onAction(t)}})),l=[{dom:{tag:"div",classes:["tox-notification__icon"],innerHtml:(t=c,e=n.iconProvider,Q(t,function(t){return st.from(e()[t.toLowerCase()])}).getOrThunk(function(){return lp(e)}))}},{dom:{tag:"div",classes:["tox-notification__body"]},components:[o.asSpec()],behaviours:nc([ug.config({})])}];return{uid:n.uid,dom:{tag:"div",attributes:{role:"alert"},classes:n.level.map(function(t){return["tox-notification","tox-notification--in","tox-notification--"+t]}).getOr(["tox-notification","tox-notification--in"])},behaviours:nc([dg.config({}),ag("notification-events",[Zo(ao(),function(t){s.getOpt(t).each(dg.focus)})])]),components:l.concat(n.progress?[u.asSpec()]:[]).concat(n.closeButton?[s.asSpec()]:[]),apis:a}},configFields:[qn("level"),Nn("progress"),Nn("icon"),Nn("onAction"),Nn("text"),Nn("iconProvider"),Nn("translationProvider"),ie("closeButton",!0)],apis:{updateProgress:function(t,n,e){t.updateProgress(n,e)},updateText:function(t,n,e){t.updateText(n,e)}}});function gp(t,u,a){var c=u.backstage;return{open:function(t,n){var e=!t.closeButton&&t.timeout&&(0<t.timeout||t.timeout<0),o=function(){n(),Kg.hide(i)},r=lu(mp.sketch({text:t.text,level:M(["success","error","warning","warn","info"],t.type)?t.type:undefined,progress:!0===t.progressBar,icon:st.from(t.icon),closeButton:!e,onAction:o,iconProvider:c.shared.providers.icons,translationProvider:c.shared.providers.translate})),i=lu(Kg.sketch(nt({dom:{tag:"div",classes:["tox-notifications-container"]},lazySink:u.backstage.shared.getSink,fireDismissalEventInstead:{}},c.shared.header.isPositionedAtTop()?{}:{fireRepositionEventInstead:{}})));return a.add(i),0<t.timeout&&ap.setTimeout(function(){o()},t.timeout),{close:o,moveTo:function(t,n){Kg.showAt(i,{anchor:"makeshift",x:t,y:n},fu(r))},moveRel:function(t,n){var e,o;"banner"!==n?(e=function(t){switch(t){case"bc-bc":return up;case"tc-tc":return ip;case"tc-bc":return Ha;case"bc-tc":default:return za}}(n),o={anchor:"node",root:Ni(),node:st.some(fe.fromDom(t)),layouts:{onRtl:function(){return[e]},onLtr:function(){return[e]}}},Kg.showAt(i,o,fu(r))):Kg.showAt(i,u.backstage.shared.anchors.banner(),fu(r))},text:function(t){mp.updateText(r,t)},settings:t,getEl:function(){return r.element.dom},progressBar:{value:function(t){mp.updateProgress(r,t)}}}},close:function(t){t.close()},reposition:function(t){var e;ot(t,function(t){return t.moveTo(0,0)}),0<(e=t).length&&(q(e).each(function(t){return t.moveRel(null,"banner")}),ot(e,function(t,n){0<n&&t.moveRel(e[n-1].getEl(),"bc-tc")}))},getArgs:function(t){return t.settings}}}var pp,hp,vp=function(e,o){var r=null;return{cancel:function(){null!==r&&(clearTimeout(r),r=null)},throttle:function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];null!==r&&clearTimeout(r),r=setTimeout(function(){e.apply(null,t),r=null},o)}}},bp=tinymce.util.Tools.resolve("tinymce.dom.TextSeeker"),yp=function(o,t,n,e,r){var i=bp(o,function(t){return e=t,(n=o).isBlock(e)||M(["BR","IMG","HR","INPUT"],e.nodeName)||"false"===n.getContentEditable(e);var n,e});return st.from(i.backwards(t,n,e,r))},xp=function(e,n){return wp(fe.fromDom(e.selection.getNode())).getOrThunk(function(){var t=fe.fromHtml('<span data-mce-autocompleter="1" data-mce-bogus="1"></span>',e.getDoc());return Er(t,fe.fromDom(n.extractContents())),n.insertNode(t.dom),xr(t).each(function(t){return t.dom.normalize()}),cs(t,as).map(function(t){var n;e.selection.setCursorLocation(t.dom,"img"===lr(n=t)?1:is(n).fold(function(){return kr(n).length},function(t){return t.length}))}),t})},wp=function(t){return Xu(t,"[data-mce-autocompleter]")},Sp=function(t){return t.toString().replace(/\u00A0/g," ").replace(/\uFEFF/g,"")},kp=function(t){return""!==t&&-1!==" \xa0\f\n\r\t\x0B".indexOf(t)},Cp=function(t,n){return t.substring(n.length)},Op=function(t,o,r,i){if(void 0===i&&(i=0),!(n=o).collapsed||3!==n.startContainer.nodeType)return st.none();var n,e=t.getParent(o.startContainer,t.isBlock)||t.getRoot();return yp(t,o.startContainer,o.startOffset,function(t,n,e){return function(t,n,e){for(var o=n-1;0<=o;o--){var r=t.charAt(o);if(kp(r))return st.none();if(r===e)break}return st.some(o)}(e,n,r).getOr(n)},e).bind(function(t){var n=o.cloneRange();if(n.setStart(t.container,t.offset),n.setEnd(o.endContainer,o.endOffset),n.collapsed)return st.none();var e=Sp(n);return 0!==e.lastIndexOf(r)||Cp(e,r).length<i?st.none():st.some({text:Cp(e,r),range:n,triggerChar:r})})},_p=function(o,t,r,n){return void 0===n&&(n=0),wp(fe.fromDom(t.startContainer)).fold(function(){return Op(o,t,r,n)},function(t){var n=o.createRng();n.selectNode(t.dom);var e=Sp(n);return st.some({range:n,text:Cp(e,r),triggerChar:r})})},Tp=function(e,t){t.on("keypress compositionend",e.onKeypress.throttle),t.on("remove",e.onKeypress.cancel);var o=function(t,n){Go(t,so(),{raw:n})};t.on("keydown",function(n){var t=function(){return e.getView().bind(ud.getHighlighted)};8===n.which&&e.onKeypress.throttle(n),e.isActive()&&(27===n.which&&e.cancelIfNecessary(),e.isMenuOpen()?13===n.which?(t().each(Xo),n.preventDefault()):40===n.which?(t().fold(function(){e.getView().each(ud.highlightFirst)},function(t){o(t,n)}),n.preventDefault(),n.stopImmediatePropagation()):37!==n.which&&38!==n.which&&39!==n.which||t().each(function(t){o(t,n),n.preventDefault(),n.stopImmediatePropagation()}):13!==n.which&&38!==n.which&&40!==n.which||e.cancelIfNecessary())}),t.on("NodeChange",function(t){e.isActive()&&!e.isProcessingAction()&&wp(fe.fromDom(t.element)).isNone()&&e.cancelIfNecessary()})},Ep=tinymce.util.Tools.resolve("tinymce.util.Promise"),Bp=function(t,n){return{container:t,offset:n}},Dp=function(t){if(3===t.nodeType)return Bp(t,t.data.length);var n=t.childNodes;return 0<n.length?Dp(n[n.length-1]):Bp(t,n.length)},Ap=function(t,n){var e=t.childNodes;return 0<e.length&&n<e.length?Ap(e[n],0):0<e.length&&1===t.nodeType&&e.length===n?Dp(e[e.length-1]):Bp(t,n)},Mp=function(r){return function(t){var n,e,o=Ap(t.startContainer,t.startOffset);return!yp(n=r,(e=o).container,e.offset,function(t,n){return 0===n?-1:n},n.getRoot()).filter(function(t){var n=t.container.data.charAt(t.offset-1);return!kp(n)}).isSome()}},Fp=function(n,e){var o,r,t=e(),i=n.selection.getRng();return o=n.dom,r=i,Q(t.triggerChars,function(t){return _p(o,r,t)}).bind(function(t){return Ip(n,e,t)})},Ip=function(n,t,e,o){void 0===o&&(o={});var r=t(),i=n.selection.getRng().startContainer.nodeValue,u=H(r.lookupByChar(e.triggerChar),function(t){return e.text.length>=t.minChars&&t.matches.getOrThunk(function(){return Mp(n.dom)})(e.range,i,e.text)});if(0===u.length)return st.none();var a=Ep.all(V(u,function(n){return n.fetch(e.text,n.maxResults,o).then(function(t){return{matchText:e.text,items:t,columns:n.columns,onAction:n.onAction}})}));return st.some({lookupData:a,context:e})},Rp=ln([jn("type"),$n("text")]),Vp=ln([te("type","autocompleteitem"),te("active",!1),te("disabled",!1),te("meta",{}),jn("value"),$n("text"),$n("icon")]),Pp=ln([jn("type"),jn("ch"),ee("minChars",1),te("columns",1),ee("maxResults",10),Qn("matches"),Wn("fetch"),Wn("onAction")]),Hp=[ie("disabled",!1),$n("tooltip"),$n("icon"),$n("text"),ue("onSetup",function(){return $})],zp=ln([jn("type"),Wn("onAction")].concat(Hp)),Np=function(t){return On("toolbarbutton",zp,t)},Lp=[ie("active",!1)].concat(Hp),jp=ln(Lp.concat([jn("type"),Wn("onAction")])),Up=function(t){return On("ToggleButton",jp,t)},Wp=[ue("predicate",function(){return!1}),re("scope","node",["node","editor"]),re("position","selection",["node","selection","line"])],Gp=Hp.concat([te("type","contextformbutton"),te("primary",!1),Wn("onAction"),ce("original",ct)]),Xp=Lp.concat([te("type","contextformbutton"),te("primary",!1),Wn("onAction"),ce("original",ct)]),Yp=Hp.concat([te("type","contextformbutton")]),qp=Lp.concat([te("type","contextformtogglebutton")]),Kp=Dn("type",{contextformbutton:Gp,contextformtogglebutton:Xp}),Jp=ln([te("type","contextform"),ue("initValue",function(){return""}),$n("label"),Yn("commands",Kp),Kn("launch",Dn("type",{contextformbutton:Yp,contextformtogglebutton:qp}))].concat(Wp)),$p=ln([te("type","contexttoolbar"),jn("items")].concat(Wp)),Qp=function(t){var n,e,o=t.ui.registry.getAll().popups,r=_t(o,function(t){return On("Autocompleter",Pp,t).fold(function(t){throw new Error(En(t))},function(t){return t})}),i=(n=Bt(r,function(t){return t.ch}),e={},ot(n,function(t){e[t]={}}),kt(e)),u=At(r);return{dataset:r,triggerChars:i,lookupByChar:function(n){return H(u,function(t){return t.ch===n})}}};(hp=pp=pp||{})[hp.CLOSE_ON_EXECUTE=0]="CLOSE_ON_EXECUTE",hp[hp.BUBBLE_TO_SANDBOX=1]="BUBBLE_TO_SANDBOX";var Zp,th,nh=pp,eh="tox-menu-nav__js",oh="tox-collection__item",rh="tox-swatch",ih={normal:eh,color:rh},uh="tox-collection__item--enabled",ah="tox-collection__item-label",ch="tox-collection__item-caret",sh="tox-collection__item--active",lh=function(t){return Mt(ih,t).getOr(eh)},fh=function(t){return{backgroundMenu:"tox-background-menu",selectedMenu:"tox-selected-menu",selectedItem:"tox-collection__item--active",hasIcons:"tox-menu--has-icons",menu:"color"===t?"tox-swatches":"tox-menu",tieredMenu:"tox-tiered-menu"}},dh=function(t){var n=fh(t);return{backgroundMenu:n.backgroundMenu,selectedMenu:n.selectedMenu,menu:n.menu,selectedItem:n.selectedItem,item:lh(t)}},mh=[jg.parts.items({})],gh=function(t,n,e){var o=fh(e);return{dom:{tag:"div",classes:rt([[o.tieredMenu]])},markers:dh(e)}},ph=function(e,o){return function(t){var n=R(t,o);return V(n,function(t){return{dom:e,components:t}})}},hh=function(t,e){var o=[],r=[];return ot(t,function(t,n){e(t,n)?(0<r.length&&o.push(r),r=[],Ft(t.dom,"innerHtml")&&r.push(t)):r.push(t)}),0<r.length&&o.push(r),V(o,function(t){return{dom:{tag:"div",classes:["tox-collection__group"]},components:t}})},vh=function(n,e,t){return{dom:{tag:"div",classes:["tox-menu","tox-collection"].concat(1===n?["tox-collection--list"]:["tox-collection--grid"])},components:[jg.parts.items({preprocess:function(t){return"auto"!==n&&1<n?ph({tag:"div",classes:["tox-collection__group"]},n)(t):hh(t,function(t,n){return"separator"===e[n].type})}})]}},bh=function(t){return F(t,function(t){return"icon"in t&&t.icon!==undefined})},yh=function(t){return console.error(En(t)),console.log(t),st.none()},xh=function(t,n,e,o,r){var i,u=(i=e,{dom:{tag:"div",classes:["tox-collection","tox-collection--horizontal"]},components:[jg.parts.items({preprocess:function(t){return hh(t,function(t,n){return"separator"===i[n].type})}})]});return{value:t,dom:u.dom,components:u.components,items:e}},wh=function(t,n,e,o,r){var i,u,a,c,s,l;return"color"===r?{value:t,dom:(a=(i=o,{dom:{tag:"div",classes:["tox-menu","tox-swatches-menu"]},components:[{dom:{tag:"div",classes:["tox-swatches"]},components:[jg.parts.items({preprocess:"auto"!==i?ph({tag:"div",classes:["tox-swatches__row"]},i):ct})]}]})).dom,components:a.components,items:e}:"normal"===r&&"auto"===o?{value:t,dom:(a=vh(o,e)).dom,components:a.components,items:e}:"normal"===r&&1===o?{value:t,dom:(a=vh(1,e)).dom,components:a.components,items:e}:"normal"===r?{value:t,dom:(a=vh(o,e)).dom,components:a.components,items:e}:"listpreview"!==r||"auto"===o?{value:t,dom:(c=n,s=o,l=fh(r),{tag:"div",classes:rt([[l.menu,"tox-menu-"+s+"-column"],c?[l.hasIcons]:[]])}),components:mh,items:e}:{value:t,dom:(a=(u=o,{dom:{tag:"div",classes:["tox-menu","tox-collection","tox-collection--toolbar","tox-collection--toolbar-lg"]},components:[jg.parts.items({preprocess:ph({tag:"div",classes:["tox-collection__group"]},u)})]})).dom,components:a.components,items:e}},Sh=[ie("disabled",!1),$n("text"),$n("shortcut"),vn("value","value",Gt(function(){return Xr("menuitem-value")}),An()),te("meta",{})],kh=ln([jn("type"),ie("active",!1),$n("icon")].concat(Sh)),Ch=ln([jn("type"),Un("fancytype",["inserttable","colorswatch"]),ue("onAction",$)]),Oh=ln([jn("type"),ue("onSetup",function(){return $}),ue("onAction",$),$n("icon")].concat(Sh)),_h=ln([jn("type"),Wn("getSubmenuItems"),ue("onSetup",function(){return $}),$n("icon")].concat(Sh)),Th=ln([jn("type"),$n("icon"),ie("active",!1),ue("onSetup",function(){return $}),Wn("onAction")].concat(Sh)),Eh=function(t,o,n){var r=ss(t.element,"."+n);if(0<r.length){var e=j(r,function(t){var n=t.dom.getBoundingClientRect().top,e=r[0].dom.getBoundingClientRect().top;return Math.abs(n-e)>o}).getOr(r.length);return st.some({numColumns:e,numRows:Math.ceil(r.length/e)})}return st.none()},Bh=function(t,n){return nc([ag(t,n)])},Dh=function(t){return Bh(Xr("unnamed-events"),t)},Ah=[Nn("lazySink"),Nn("tooltipDom"),te("exclusive",!0),te("tooltipComponents",[]),te("delay",300),re("mode","normal",["normal","follow-highlight"]),te("anchor",function(t){return{anchor:"hotspot",hotspot:t,layouts:{onLtr:at([za,Ha,Ia,Va,Ra,Pa]),onRtl:at([za,Ha,Ia,Va,Ra,Pa])}}}),la("onHide"),la("onShow")],Mh=/* */Object.freeze({__proto__:null,init:function(){var e=se(st.none()),n=se(st.none()),o=function(){e.get().each(function(t){clearTimeout(t)})},t=at("not-implemented");return si({getTooltip:function(){return n.get()},isShowing:function(){return n.get().isSome()},setTooltip:function(t){n.set(st.some(t))},clearTooltip:function(){n.set(st.none())},clearTimer:o,resetTimer:function(t,n){o(),e.set(st.some(setTimeout(function(){t()},n)))},readState:t})}}),Fh=Xr("tooltip.exclusive"),Ih=Xr("tooltip.show"),Rh=Xr("tooltip.hide"),Vh=function(t,n,e){t.getSystem().broadcastOn([Fh],{})},Ph=/* */Object.freeze({__proto__:null,hideAllExclusive:Vh,setComponents:function(t,n,e,o){e.getTooltip().each(function(t){t.getSystem().isConnected()&&ug.set(t,o)})}}),Hh=oc({fields:Ah,name:"tooltipping",active:/* */Object.freeze({__proto__:null,events:function(r,i){var e=function(n){i.getTooltip().each(function(t){el(t),r.onHide(n,t),i.clearTooltip()}),i.clearTimer()};return Jo(rt([[Zo(Ih,function(o){i.resetTimer(function(){var n,t,e;n=o,i.isShowing()||(Vh(n),t=r.lazySink(n).getOrDie(),e=n.getSystem().build({dom:r.tooltipDom,components:r.tooltipComponents,events:Jo("normal"===r.mode?[Zo(uo(),function(t){Wo(n,Ih)}),Zo(ro(),function(t){Wo(n,Rh)})]:[]),behaviours:nc([ug.config({})])}),i.setTooltip(e),Zs(t,e),r.onShow(n,e),qs.position(t,r.anchor(n),e))},r.delay)}),Zo(Rh,function(t){i.resetTimer(function(){e(t)},r.delay)}),Zo(So(),function(t,n){n.universal||M(n.channels,Fh)&&e(t)}),ar(function(t){e(t)})],"normal"===r.mode?[Zo(ao(),function(t){Wo(t,Ih)}),Zo(xo(),function(t){Wo(t,Rh)}),Zo(uo(),function(t){Wo(t,Ih)}),Zo(ro(),function(t){Wo(t,Rh)})]:[Zo(jo(),function(t,n){Wo(t,Ih)}),Zo(Uo(),function(t){Wo(t,Rh)})]]))}}),state:Mh,apis:Ph}),zh=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),Nh=tinymce.util.Tools.resolve("tinymce.util.I18n"),Lh=["input","button","textarea","select"],jh=function(t,n,e){(n.disabled()?qh:Kh)(t,n)},Uh=function(t,n){return!0===n.useNative&&M(Lh,lr(t.element))},Wh=function(t){Vr(t.element,"disabled","disabled")},Gh=function(t){Nr(t.element,"disabled")},Xh=function(t){Vr(t.element,"aria-disabled","true")},Yh=function(t){Vr(t.element,"aria-disabled","false")},qh=function(n,t,e){t.disableClass.each(function(t){Oi(n.element,t)}),(Uh(n,t)?Wh:Xh)(n),t.onDisabled(n)},Kh=function(n,t,e){t.disableClass.each(function(t){Ti(n.element,t)}),(Uh(n,t)?Gh:Yh)(n),t.onEnabled(n)},Jh=function(t,n){return Uh(t,n)?zr(t.element,"disabled"):"true"===Pr(t.element,"aria-disabled")},$h=/* */Object.freeze({__proto__:null,enable:Kh,disable:qh,isDisabled:Jh,onLoad:jh,set:function(t,n,e,o){(o?qh:Kh)(t,n)}}),Qh=/* */Object.freeze({__proto__:null,exhibit:function(t,n){return fi({classes:n.disabled()?n.disableClass.toArray():[]})},events:function(e,t){return Jo([$o(ko(),function(t,n){return Jh(t,e)}),Ja(e,t,jh)])}}),Zh=[ue("disabled",c),te("useNative",!0),qn("disableClass"),la("onDisabled"),la("onEnabled")],tv=oc({fields:Zh,name:"disabling",active:Qh,apis:$h}),nv=tinymce.util.Tools.resolve("tinymce.EditorManager"),ev=function(t){return t.getParam("height",Math.max(t.getElement().offsetHeight,200))},ov=function(t){return t.getParam("width",zh.DOM.getStyle(t.getElement(),"width"))},rv=function(t){return st.from(t.getParam("min_width")).filter(et)},iv=function(t){return st.from(t.getParam("min_height")).filter(et)},uv=function(t){return st.from(t.getParam("max_width")).filter(et)},av=function(t){return st.from(t.getParam("max_height")).filter(et)},cv=function(t){return!1!==t.getParam("menubar",!0,"boolean")},sv=function(t){var n=t.getParam("toolbar",!0),e=!0===n,o=S(n),r=h(n)&&0<n.length;return!fv(t)&&(r||o||e)},lv=function(n){var t=I(9,function(t){return n.getParam("toolbar"+(t+1),!1,"string")}),e=H(t,function(t){return"string"==typeof t});return 0<e.length?st.some(e):st.none()},fv=function(t){return lv(t).fold(function(){return 0<t.getParam("toolbar",[],"string[]").length},function(){return!0})};(th=Zp=Zp||{})["default"]="wrap",th.floating="floating",th.sliding="sliding",th.scrolling="scrolling";var dv,mv,gv=function(t){return t.getParam("toolbar_mode","","string")};(mv=dv=dv||{}).auto="auto",mv.top="top",mv.bottom="bottom";var pv,hv=function(t){return t.getParam("toolbar_location",dv.auto,"string")},vv=function(t){return hv(t)===dv.bottom},bv=function(t){var n=t.getParam("fixed_toolbar_container","","string");return 0<n.length&&t.inline?Gu(Ni(),n):st.none()},yv=function(t){return t.inline&&bv(t).isSome()},xv=function(t){return t.inline&&!cv(t)&&!sv(t)&&!fv(t)},wv=function(t){return(t.getParam("toolbar_sticky",!1,"boolean")||t.inline)&&!yv(t)&&!xv(t)},Sv="silver.readonly",kv=ln([Ln("readonly",Rn)]),Cv=function(t,n){var e=t.outerContainer.element;n&&(t.mothership.broadcastOn([vl()],{target:e}),t.uiMothership.broadcastOn([vl()],{target:e})),t.mothership.broadcastOn([Sv],{readonly:n}),t.uiMothership.broadcastOn([Sv],{readonly:n})},Ov=function(t,n){t.on("init",function(){t.mode.isReadOnly()&&Cv(n,!0)}),t.on("SwitchMode",function(){return Cv(n,t.mode.isReadOnly())}),t.getParam("readonly",!1,"boolean")&&t.setMode("readonly")},_v=function(){var t;return ac.config({channels:((t={})[Sv]={schema:kv,onReceive:function(t,n){tv.set(t,n.readonly)}},t)})},Tv=function(t){return tv.config({disabled:t,disableClass:"tox-collection__item--state-disabled"})},Ev=function(t){return tv.config({disabled:t})},Bv=function(t){return tv.config({disabled:t,disableClass:"tox-tbtn--disabled"})},Dv=function(t){return tv.config({disabled:t,disableClass:"tox-tbtn--disabled",useNative:!1})},Av=function(t,n){var e=t.getApi(n);return function(t){t(e)}},Mv=function(e,o){return ur(function(t){Av(e,t)(function(t){var n=e.onSetup(t);null!==n&&n!==undefined&&o.set(n)})})},Fv=function(n,e){return ar(function(t){return Av(n,t)(e.get())})},Iv=((pv={})[ko()]=["disabling","alloy.base.behaviour","toggling","item-events"],pv),Rv=function(t){return U(t,function(t){return t.toArray()})},Vv=function(t,n,e,o){var r,i,u=se($);return{type:"item",dom:n.dom,components:Rv(n.optComponents),data:t.data,eventOrder:Iv,hasSubmenu:t.triggersSubmenu,itemBehaviours:nc([ag("item-events",[(r=t,i=e,sr(function(t,n){Av(r,t)(r.onAction),r.triggersSubmenu||i!==nh.CLOSE_ON_EXECUTE||(Wo(t,To()),n.stop())})),Mv(t,u),Fv(t,u)]),Tv(function(){return t.disabled||o.isReadOnly()}),_v(),ug.config({})].concat(t.itemBehaviours))}},Pv=function(t){return{value:t.value,meta:nt({text:t.text.getOr("")},t.meta)}},Hv=tinymce.util.Tools.resolve("tinymce.Env"),zv=function(t){return{dom:{tag:"div",classes:["tox-collection__item-icon"],innerHtml:t}}},Nv=function(t){return{dom:{tag:"div",classes:[ah]},components:[au(Nh.translate(t))]}},Lv=function(t,n){return{dom:{tag:"div",classes:[ah]},components:[{dom:{tag:t.tag,styles:t.styles},components:[au(Nh.translate(n))]}]}},jv=function(t){return{dom:{tag:"div",classes:["tox-collection__item-accessory"],innerHtml:(n=t,e=Hv.mac?{alt:"&#x2325;",ctrl:"&#x2303;",shift:"&#x21E7;",meta:"&#x2318;",access:"&#x2303;&#x2325;"}:{meta:"Ctrl",access:"Shift+Alt"},o=n.split("+"),r=V(o,function(t){var n=t.toLowerCase().trim();return Ft(e,n)?e[n]:t}),Hv.mac?r.join(""):r.join("+"))}};var n,e,o,r},Uv=function(t){return{dom:{tag:"div",classes:["tox-collection__item-checkmark"],innerHtml:fp("checkmark",t)}}},Wv=function(t,n,e,o,r){var i=e?n.or(st.some("")).map(zv):st.none(),u=t.checkMark,a=t.ariaLabel.map(function(t){return{attributes:{title:Nh.translate(t)}}}).getOr({});return{dom:nt({tag:"div",classes:[eh,oh].concat(r?["tox-collection__item-icon-rtl"]:[])},a),optComponents:[i,t.htmlContent.fold(function(){return t.textContent.map(o)},function(t){return st.some({dom:{tag:"div",classes:[ah],innerHtml:t}})}),t.shortcutContent.map(jv),u,t.caret]}},Gv=["list-num-default","list-num-lower-alpha","list-num-lower-greek","list-num-lower-roman","list-num-upper-alpha","list-num-upper-roman"],Xv=["list-bull-circle","list-bull-default","list-bull-square"],Yv=function(t,r,n,i){void 0===i&&(i=st.none());var e,o,u,a,c,s=Nh.isRtl()&&t.iconContent.exists(function(t){return M(Xv,t)}),l=t.iconContent.map(function(t){return Nh.isRtl()&&M(Gv,t)?t+"-rtl":t}).map(function(t){return n=t,e=r.icons,o=i,st.from(e()[n.toLowerCase()]).or(o).getOrThunk(function(){return lp(e)});var n,e,o}),f=st.from(t.meta).fold(function(){return Nv},function(t){return Ft(t,"style")?g(Lv,t.style):Nv});return"color"===t.presets?(e=t.ariaLabel,o=t.value,u=r,{dom:(a=l.getOr(""),c={tag:"div",attributes:e.map(function(t){return{title:u.translate(t)}}).getOr({}),classes:["tox-swatch"]},nt(nt({},c),"custom"===o?{tag:"button",classes:w(c.classes,["tox-swatches__picker-btn"]),innerHtml:a}:"remove"===o?{classes:w(c.classes,["tox-swatch--remove"]),innerHtml:a}:{attributes:nt(nt({},c.attributes),{"data-mce-color":o}),styles:{"background-color":o}})),optComponents:[]}):Wv(t,l,n,f,s)},qv=function(t,n){var e,o=Nh.translate(t),r=(e=o,zh.DOM.encode(e));if(0<n.length){var i=new RegExp(n.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),"gi");return r.replace(i,function(t){return'<span class="tox-autocompleter-highlight">'+t+"</span>"})}return r},Kv=xf(Rg(),Vg()),Jv=function(t){return{value:t}},$v=/^#?([a-f\d])([a-f\d])([a-f\d])$/i,Qv=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i,Zv=function(t){return $v.test(t)||Qv.test(t)},tb=function(t){var n={value:t.value.replace($v,function(t,n,e,o){return n+n+e+e+o+o})},e=Qv.exec(n.value);return null===e?["FFFFFF","FF","FF","FF"]:e},nb=function(t){var n=t.toString(16);return 1===n.length?"0"+n:n},eb=function(t){var n=nb(t.red)+nb(t.green)+nb(t.blue);return Jv(n)},ob=Math.min,rb=Math.max,ib=Math.round,ub=/^rgb\((\d+),\s*(\d+),\s*(\d+)\)/,ab=/^rgba\((\d+),\s*(\d+),\s*(\d+),\s*(\d?(?:\.\d+)?)\)/,cb=function(t,n,e,o){return{red:t,green:n,blue:e,alpha:o}},sb=function(t){var n=parseInt(t,10);return n.toString()===t&&0<=n&&n<=255},lb=function(t){var n,e,o,r=(t.hue||0)%360,i=t.saturation/100,u=t.value/100,i=rb(0,ob(i,1)),u=rb(0,ob(u,1));if(0===i)return n=e=o=ib(255*u),cb(n,e,o,1);var a=r/60,c=u*i,s=c*(1-Math.abs(a%2-1)),l=u-c;switch(Math.floor(a)){case 0:n=c,e=s,o=0;break;case 1:n=s,e=c,o=0;break;case 2:n=0,e=c,o=s;break;case 3:n=0,e=s,o=c;break;case 4:n=s,e=0,o=c;break;case 5:n=c,e=0,o=s;break;default:n=e=o=0}return n=ib(255*(n+l)),e=ib(255*(e+l)),o=ib(255*(o+l)),cb(n,e,o,1)},fb=function(t){var n=tb(t),e=parseInt(n[1],16),o=parseInt(n[2],16),r=parseInt(n[3],16);return cb(e,o,r,1)},db=function(t,n,e,o){var r=parseInt(t,10),i=parseInt(n,10),u=parseInt(e,10),a=parseFloat(o);return cb(r,i,u,a)},mb=function(t){return"rgba("+t.red+","+t.green+","+t.blue+","+t.alpha+")"},gb=cb(255,0,0,1),pb=function(t,n){return t.fire("ResizeContent",n)},hb=tinymce.util.Tools.resolve("tinymce.util.LocalStorage"),vb="tinymce-custom-colors";var bb="choiceitem",yb=[{type:bb,text:"Light Green",value:"#BFEDD2"},{type:bb,text:"Light Yellow",value:"#FBEEB8"},{type:bb,text:"Light Red",value:"#F8CAC6"},{type:bb,text:"Light Purple",value:"#ECCAFA"},{type:bb,text:"Light Blue",value:"#C2E0F4"},{type:bb,text:"Green",value:"#2DC26B"},{type:bb,text:"Yellow",value:"#F1C40F"},{type:bb,text:"Red",value:"#E03E2D"},{type:bb,text:"Purple",value:"#B96AD9"},{type:bb,text:"Blue",value:"#3598DB"},{type:bb,text:"Dark Turquoise",value:"#169179"},{type:bb,text:"Orange",value:"#E67E23"},{type:bb,text:"Dark Red",value:"#BA372A"},{type:bb,text:"Dark Purple",value:"#843FA1"},{type:bb,text:"Dark Blue",value:"#236FA1"},{type:bb,text:"Light Gray",value:"#ECF0F1"},{type:bb,text:"Medium Gray",value:"#CED4D9"},{type:bb,text:"Gray",value:"#95A5A6"},{type:bb,text:"Dark Gray",value:"#7E8C8D"},{type:bb,text:"Navy Blue",value:"#34495E"},{type:bb,text:"Black",value:"#000000"},{type:bb,text:"White",value:"#ffffff"}],xb=function(e){void 0===e&&(e=10);var t,n=hb.getItem(vb),o=S(n)?JSON.parse(n):[],r=e-(t=o).length<0?t.slice(0,e):t,i=function(t){r.splice(t,1)};return{add:function(t){var n;(-1===(n=A(r,t))?st.none():st.some(n)).each(i),r.unshift(t),r.length>e&&r.pop(),hb.setItem(vb,JSON.stringify(r))},state:function(){return r.slice(0)}}}(10),wb=function(t){return!1!==t.getParam("custom_colors")},Sb=function(t){var n=t.getParam("color_map");return n!==undefined?function(t){var n=[],u=document.createElement("canvas");u.height=1,u.width=1;for(var a=u.getContext("2d"),c=function(t,n){var e=n/255;return("0"+Math.round(t*e+255*(1-e)).toString(16)).slice(-2).toUpperCase()},e=0;e<t.length;e+=2)n.push({text:t[e+1],value:function(t){if(/^[0-9A-Fa-f]{6}$/.test(t))return"#"+t.toUpperCase();a.clearRect(0,0,u.width,u.height),a.fillStyle="#FFFFFF",a.fillStyle=t,a.fillRect(0,0,1,1);var n=a.getImageData(0,0,1,1).data,e=n[0],o=n[1],r=n[2],i=n[3];return"#"+c(e,i)+c(o,i)+c(r,i)}(t[e]),type:"choiceitem"});return n}(n):yb},kb=function(t){xb.add(t)},Cb=function(i){i.addCommand("mceApplyTextcolor",function(t,n){var e,o,r;o=t,r=n,(e=i).undoManager.transact(function(){e.focus(),e.formatter.apply(o,{value:r}),e.nodeChanged()})}),i.addCommand("mceRemoveTextcolor",function(t){var n,e;e=t,(n=i).undoManager.transact(function(){n.focus(),n.formatter.remove(e,{value:null},null,!0),n.nodeChanged()})})},Ob=function(t){var n,e,o=Sb(t),r=(n=o.length,Math.max(5,Math.ceil(Math.sqrt(n))));return e=r,t.getParam("color_cols",e,"number")},_b=function(n,e,t,o){"custom"===t?Mb(n)(function(t){t.each(function(t){kb(t),n.execCommand("mceApplyTextcolor",e,t),o(t)})},"#000000"):"remove"===t?(o(""),n.execCommand("mceRemoveTextcolor",e)):(o(t),n.execCommand("mceApplyTextcolor",e,t))},Tb=function(t,n){return t.concat(V(xb.state(),function(t){return{type:bb,text:t,value:t}}).concat((o={type:e="choiceitem",text:"Remove color",icon:"color-swatch-remove-color",value:"remove"},n?[o,{type:e,text:"Custom color",icon:"color-picker",value:"custom"}]:[o])));var e,o},Eb=function(n,e){return function(t){t(Tb(n,e))}},Bb=function(t,n,e){var o,r;o="forecolor"===n?"tox-icon-text-color__color":"tox-icon-highlight-bg-color__color",r=e,t.setIconFill(o,r),t.setIconStroke(o,r)},Db=function(i,e,u,t,o){i.ui.registry.addSplitButton(e,{tooltip:t,presets:"color",icon:"forecolor"===e?"text-color":"highlight-bg-color",select:function(e){var t,o,r;return st.from((o=u,(t=i).dom.getParents(t.selection.getStart(),function(t){var n;(n=t.style["forecolor"===o?"color":"background-color"])&&(r=r||n)}),r)).bind(function(t){return function(t){if("transparent"===t)return st.some(cb(0,0,0,0));var n=ub.exec(t);if(null!==n)return st.some(db(n[1],n[2],n[3],"1"));var e=ab.exec(t);return null!==e?st.some(db(e[1],e[2],e[3],e[4])):st.none()}(t).map(function(t){var n=eb(t).value;return ye(e.toLowerCase(),n)})}).getOr(!1)},columns:Ob(i),fetch:Eb(Sb(i),wb(i)),onAction:function(t){null!==o.get()&&_b(i,u,o.get(),function(){})},onItemAction:function(t,n){_b(i,u,n,function(t){var n;o.set(t),n={name:e,color:t},i.fire("TextColorChange",n)})},onSetup:function(n){null!==o.get()&&Bb(n,e,o.get());var t=function(t){t.name===e&&Bb(n,t.name,t.color)};return i.on("TextColorChange",t),function(){i.off("TextColorChange",t)}}})},Ab=function(n,t,e,o){n.ui.registry.addNestedMenuItem(t,{text:o,icon:"forecolor"===t?"text-color":"highlight-bg-color",getSubmenuItems:function(){return[{type:"fancymenuitem",fancytype:"colorswatch",onAction:function(t){_b(n,e,t.value,$)}}]}})},Mb=function(i){return function(t,n){var e,o={colorpicker:n},r=(e=t,function(t){var n=t.getData();e(st.from(n.colorpicker)),t.close()});i.windowManager.open({title:"Color Picker",size:"normal",body:{type:"panel",items:[{type:"colorpicker",name:"colorpicker",label:"Color"}]},buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:o,onAction:function(t,n){"hex-valid"===n.name&&(n.value?t.enable("ok"):t.disable("ok"))},onSubmit:r,onClose:function(){},onCancel:function(){t(st.none())}})}},Fb=function(t,n,e,o,r,i,u,a){var c=bh(n),s=Ib(n,e,o,"color"!==r?"normal":"color",i,u,a);return wh(t,c,s,o,r)},Ib=function(e,o,r,i,u,a,c){return Gf(V(e,function(n){return"choiceitem"===n.type?On("choicemenuitem",kh,n).fold(yh,function(t){return st.some(function(n,t,e,o,r,i,u,a){void 0===a&&(a=!0);var c=Yv({presets:e,textContent:t?n.text:st.none(),htmlContent:st.none(),ariaLabel:n.text,iconContent:n.icon,shortcutContent:t?n.shortcut:st.none(),checkMark:t?st.some(Uv(u.icons)):st.none(),caret:st.none(),value:n.value},u,a);return Ht(Vv({data:Pv(n),disabled:n.disabled,getApi:function(n){return{setActive:function(t){Cg.set(n,t)},isActive:function(){return Cg.isOn(n)},isDisabled:function(){return tv.isDisabled(n)},setDisabled:function(t){return tv.set(n,t)}}},onAction:function(t){return o(n.value)},onSetup:function(t){return t.setActive(r),function(){}},triggersSubmenu:!1,itemBehaviours:[]},c,i,u),{toggling:{toggleClass:uh,toggleOnExecute:!1,selected:n.active}})}(t,1===r,i,o,a(n.value),u,c,bh(e)))}):st.none()}))},Rb=function(t,n){var e=dh(n);return 1===t?{mode:"menu",moveOnTab:!0}:"auto"===t?{mode:"grid",selector:"."+e.item,initSize:{numColumns:1,numRows:1}}:{mode:"matrix",rowSelector:"."+("color"===n?"tox-swatches__row":"tox-collection__group")}};var Vb,Pb,Hb=Xr("cell-over"),zb=Xr("cell-execute"),Nb=function(t,n,e){for(var o=[],r=0;r<n;r++){for(var i=[],u=0;u<e;u++)i.push(function(n,e,t){var o,r=function(t){return Go(t,zb,{row:n,col:e})},i=function(t,n){n.stop(),r(t)};return lu({dom:{tag:"div",attributes:((o={role:"button"})["aria-labelledby"]=t,o)},behaviours:nc([ag("insert-table-picker-cell",[Zo(uo(),dg.focus),Zo(ko(),r),Zo(go(),i),Zo(Oo(),i)]),Cg.config({toggleClass:"tox-insert-table-picker__selected",toggleOnExecute:!1}),dg.config({onFocus:function(t){return Go(t,Hb,{row:n,col:e})}})])})}(r,u,t));o.push(i)}return o},Lb={inserttable:function(o){var t=Xr("size-label"),i=Nb(t,10,10),u=sp({dom:{tag:"span",classes:["tox-insert-table-picker__label"],attributes:{id:t}},components:[au("0x0")],behaviours:nc([ug.config({})])});return{type:"widget",data:{value:Xr("widget-id")},dom:{tag:"div",classes:["tox-fancymenuitem"]},autofocus:!0,components:[Kv.widget({dom:{tag:"div",classes:["tox-insert-table-picker"]},components:U(i,function(t){return V(t,fu)}).concat(u.asSpec()),behaviours:nc([ag("insert-table-picker",[or(Hb,function(t,n,e){var o=e.event.row,r=e.event.col;!function(t,n,e,o,r){for(var i=0;i<o;i++)for(var u=0;u<r;u++)Cg.set(t[i][u],i<=n&&u<=e)}(i,o,r,10,10),ug.set(u.get(t),[au(r+1+"x"+(o+1))])}),or(zb,function(t,n,e){o.onAction({numRows:e.event.row+1,numColumns:e.event.col+1}),Wo(t,To())})]),ng.config({initSize:{numRows:10,numColumns:10},mode:"flatgrid",selector:'[role="button"]'})])})]}},colorswatch:function(n,t){var e=Tb(t.colorinput.getColors(),t.colorinput.hasCustomColors()),o=t.colorinput.getColorCols(),r=Fb(Xr("menu-value"),e,function(t){n.onAction({value:t})},o,"color",nh.CLOSE_ON_EXECUTE,function(){return!1},t.shared.providers),i=nt(nt({},r),{markers:dh("color"),movement:Rb(o,"color")});return{type:"widget",data:{value:Xr("widget-id")},dom:{tag:"div",classes:["tox-fancymenuitem"]},autofocus:!0,components:[Kv.widget(jg.sketch(i))]}}},jb=function(n,e,t,o,r,i,u,a){void 0===a&&(a=!0);var c,s,l=Yv({presets:o,textContent:st.none(),htmlContent:t?n.text.map(function(t){return qv(t,e)}):st.none(),ariaLabel:n.text,iconContent:n.icon,shortcutContent:st.none(),checkMark:st.none(),caret:st.none(),value:n.value},u.providers,a,n.icon);return Vv({data:Pv(n),disabled:n.disabled,getApi:function(){return{}},onAction:function(t){return r(n.value,n.meta)},onSetup:function(){return function(){}},triggersSubmenu:!1,itemBehaviours:(c=n.meta,s=u,Mt(c,"tooltipWorker").map(function(e){return[Hh.config({lazySink:s.getSink,tooltipDom:{tag:"div",classes:["tox-tooltip-worker-container"]},tooltipComponents:[],anchor:function(t){return{anchor:"submenu",item:t,overrides:{maxHeightFunction:Bc}}},mode:"follow-highlight",onShow:function(n,t){e(function(t){Hh.setComponents(n,[cu({element:fe.fromDom(t)})])})}})]}).getOr([]))},l,i,u.providers)},Ub=function(t){var n=t.text.fold(function(){return{}},function(t){return{innerHtml:t}});return{type:"separator",dom:nt({tag:"div",classes:[oh,"tox-collection__group-heading"]},n),components:[]}},Wb=function(t,n,e,o){void 0===o&&(o=!0);var r=Yv({presets:"normal",iconContent:t.icon,textContent:t.text,htmlContent:st.none(),ariaLabel:t.text,caret:st.none(),checkMark:st.none(),shortcutContent:t.shortcut},e,o);return Vv({data:Pv(t),getApi:function(n){return{isDisabled:function(){return tv.isDisabled(n)},setDisabled:function(t){return tv.set(n,t)}}},disabled:t.disabled,onAction:t.onAction,onSetup:t.onSetup,triggersSubmenu:!1,itemBehaviours:[]},r,n,e)},Gb=function(t,n,e,o,r){void 0===o&&(o=!0),void 0===r&&(r=!1);var i,u,a=r?(u=e.icons,{dom:{tag:"div",classes:[ch],innerHtml:fp("chevron-down",u)}}):(i=e.icons,{dom:{tag:"div",classes:[ch],innerHtml:fp("chevron-right",i)}}),c=Yv({presets:"normal",iconContent:t.icon,textContent:t.text,htmlContent:st.none(),ariaLabel:t.text,caret:st.some(a),checkMark:st.none(),shortcutContent:t.shortcut},e,o);return Vv({data:Pv(t),getApi:function(n){return{isDisabled:function(){return tv.isDisabled(n)},setDisabled:function(t){return tv.set(n,t)}}},disabled:t.disabled,onAction:$,onSetup:t.onSetup,triggersSubmenu:!0,itemBehaviours:[]},c,n,e)},Xb=function(t,n,e,o){void 0===o&&(o=!0);var r=Yv({iconContent:t.icon,textContent:t.text,htmlContent:st.none(),ariaLabel:t.text,checkMark:st.some(Uv(e.icons)),caret:st.none(),shortcutContent:t.shortcut,presets:"normal",meta:t.meta},e,o);return Ht(Vv({data:Pv(t),disabled:t.disabled,getApi:function(n){return{setActive:function(t){Cg.set(n,t)},isActive:function(){return Cg.isOn(n)},isDisabled:function(){return tv.isDisabled(n)},setDisabled:function(t){return tv.set(n,t)}}},onAction:t.onAction,onSetup:t.onSetup,triggersSubmenu:!1,itemBehaviours:[]},r,n,e),{toggling:{toggleClass:uh,toggleOnExecute:!1,selected:t.active}})},Yb=function(n,e){return t=Lb,o=n.fancytype,(Object.prototype.hasOwnProperty.call(t,o)?st.some(t[o]):st.none()).map(function(t){return t(n,e)});var t,o};(Pb=Vb=Vb||{})[Pb.ContentFocus=0]="ContentFocus",Pb[Pb.UiFocus=1]="UiFocus";var qb=function(t,n,e,o,r){var i=e.shared.providers,u=function(t){return r?nt(nt({},t),{shortcut:st.none(),icon:t.text.isSome()?st.none():t.icon}):t};switch(t.type){case"menuitem":return On("menuitem",Oh,t).fold(yh,function(t){return st.some(Wb(u(t),n,i,o))});case"nestedmenuitem":return On("nestedmenuitem",_h,t).fold(yh,function(t){return st.some(Gb(u(t),n,i,o,r))});case"togglemenuitem":return On("togglemenuitem",Th,t).fold(yh,function(t){return st.some(Xb(u(t),n,i,o))});case"separator":return On("separatormenuitem",Rp,t).fold(yh,function(t){return st.some(Ub(t))});case"fancymenuitem":return On("fancymenuitem",Ch,t).fold(yh,function(t){return Yb(u(t),e)});default:return console.error("Unknown item in general menu",t),st.none()}},Kb=function(t,n,e,o,r,i){var u=1===o,a=!u||bh(t);return Gf(V(t,function(t){return"separator"===t.type?On("Autocompleter.Separator",Rp,t).fold(yh,function(t){return st.some(Ub(t))}):On("Autocompleter.Item",Vp,t).fold(yh,function(t){return st.some(jb(t,n,u,"normal",e,r,i,a))})}))},Jb=function(t,n,e,o,r){var i=bh(n),u=Gf(V(n,function(t){var n=function(t){return qb(t,e,o,(n=t,r?!n.hasOwnProperty("text"):i),r);var n};return"nestedmenuitem"===t.type&&t.getSubmenuItems().length<=0?n(nt(nt({},t),{disabled:!0})):n(t)}));return(r?xh:wh)(t,i,u,1,"normal")},$b=function(t){return qg.singleData(t.value,t)},Qb=function(d,c){var e=se(st.none()),s=se(!1),m=lu(Kg.sketch({dom:{tag:"div",classes:["tox-autocompleter"]},components:[],fireDismissalEventInstead:{},inlineBehaviours:nc([ag("dismissAutocompleter",[Zo(Vo(),function(){return f()})])]),lazySink:c.getSink})),o=function(){return e.get().isSome()},l=function(){o()&&Kg.hide(m)},f=function(){var t;o()&&(t=e.get().map(function(t){return t.element}),wp(t.getOr(fe.fromDom(d.selection.getNode()))).each(Mr),l(),e.set(st.none()),s.set(!1))},r=Nt(function(){return Qp(d)}),g=function(t,n,e,o){t.matchLength=n.text.length;var r,i,u,a,c,s,l,f=Q(e,function(t){return st.from(t.columns)}).getOr(1);Kg.showAt(m,{anchor:"node",root:fe.fromDom(d.getBody()),node:st.from(t.element)},jg.sketch((r=wh("autocompleter-value",!0,o,f,"normal"),i=f,u=Vb.ContentFocus,a="normal",c=(u===Vb.ContentFocus?Ed:Td)(),s=Rb(i,a),l=dh(a),{dom:r.dom,components:r.components,items:r.items,value:r.value,markers:{selectedItem:l.selectedItem,item:l.item},movement:s,fakeFocus:u===Vb.ContentFocus,focusManager:c,menuBehaviours:Dh("auto"!==i?[]:[ur(function(o,t){Eh(o,4,l.item).each(function(t){var n=t.numColumns,e=t.numRows;ng.setGridSize(o,e,n)})})])}))),Kg.getContent(m).each(ud.highlightFirst)},p=function(t){var n;n=t,e.get().map(function(t){return _p(d.dom,d.selection.getRng(),t.triggerChar).bind(function(t){return Ip(d,r,t,n)})}).getOrThunk(function(){return Fp(d,r)}).fold(f,function(a){var t,n;t=a.context,o()||(n=xp(d,t.range),e.set(st.some({triggerChar:t.triggerChar,element:n,matchLength:t.text.length})),s.set(!1)),a.lookupData.then(function(u){e.get().map(function(t){var n,e,o,r,i=a.context;t.triggerChar===i.triggerChar&&(e=i.triggerChar,r=Q(o=u,function(t){return st.from(t.columns)}).getOr(1),0<(n=U(o,function(i){var t=i.items;return Kb(t,i.matchText,function(o,r){var t=d.selection.getRng();_p(d.dom,t,e).fold(function(){return console.error("Lost context. Cursor probably moved")},function(t){var n=t.range,e={hide:function(){f()},reload:function(t){l(),p(t)}};s.set(!0),i.onAction(e,n,o,r),s.set(!1)})},r,nh.BUBBLE_TO_SANDBOX,c)})).length?g(t,i,u,n):(10<=i.text.length-t.matchLength?f:l)())})})})},t={onKeypress:vp(function(t){27!==t.which&&p()},50),cancelIfNecessary:f,isMenuOpen:function(){return Kg.isOpen(m)},isActive:o,isProcessingAction:s.get,getView:function(){return Kg.getContent(m)}};Tp(t,d)},Zb=b,ty=function(t,n,e){return Ou(t,n,Zb,e,!1)},ny=function(t,n,e){return Ou(t,n,Zb,e,!0)},ey=Cu,oy=function(t,n,e){return Xu(t,n,e).isSome()};function ry(e,o){var r=null;return{cancel:function(){null!==r&&(clearTimeout(r),r=null)},schedule:function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];r=setTimeout(function(){e.apply(null,t),r=null},o)}}}var iy=function(t){var n=t.raw;return n.touches===undefined||1!==n.touches.length?st.none():st.some(n.touches[0])},uy=function(e){var u=se(st.none()),o=se(!1),r=ry(function(t){e.triggerEvent(_o(),t),o.set(!0)},400),i=Jt([{key:Qe(),value:function(e){return iy(e).each(function(t){r.cancel();var n={x:t.clientX,y:t.clientY,target:e.target};r.schedule(e),o.set(!1),u.set(st.some(n))}),st.none()}},{key:Ze(),value:function(t){return r.cancel(),iy(t).each(function(i){u.get().each(function(t){var n,e,o,r;n=i,e=t,o=Math.abs(n.clientX-e.x),r=Math.abs(n.clientY-e.y),(5<o||5<r)&&u.set(st.none())})}),st.none()}},{key:to(),value:function(n){r.cancel();return u.get().filter(function(t){return je(t.target,n.target)}).map(function(t){return o.get()?(n.prevent(),!1):e.triggerEvent(Oo(),n)})}}]);return{fireIfReady:function(n,t){return Mt(i,t).bind(function(t){return t(n)})}}},ay=function(){return ze().browser.isFirefox()},cy=sn([Wn("triggerEvent"),te("stopBackspace",!0)]),sy=function(n,t){var e,o,r,i,u=Tn("Getting GUI events settings",cy,t),a=uy(u),c=V(["touchstart","touchmove","touchend","touchcancel","gesturestart","mousedown","mouseup","mouseover","mousemove","mouseout","click"].concat(["selectstart","input","contextmenu","change","transitionend","drag","dragstart","dragend","dragenter","dragleave","dragover","drop","keyup"]),function(t){return ty(n,t,function(n){a.fireIfReady(n,t).each(function(t){t&&n.kill()}),u.triggerEvent(t,n)&&n.kill()})}),s=se(st.none()),l=ty(n,"paste",function(n){a.fireIfReady(n,"paste").each(function(t){t&&n.kill()}),u.triggerEvent("paste",n)&&n.kill(),s.set(st.some(setTimeout(function(){u.triggerEvent(wo(),n)},0)))}),f=ty(n,"keydown",function(t){var n;u.triggerEvent("keydown",t)?t.kill():!0!==u.stopBackspace||((n=t).raw.which!==ad[0]||M(["input","textarea"],lr(n.target))||oy(n.target,'[contenteditable="true"]'))||t.prevent()}),d=(e=n,o=function(t){u.triggerEvent("focusin",t)&&t.kill()},ay()?ny(e,"focus",o):ty(e,"focusin",o)),m=se(st.none()),g=(r=n,i=function(t){u.triggerEvent("focusout",t)&&t.kill(),m.set(st.some(setTimeout(function(){u.triggerEvent(xo(),t)},0)))},ay()?ny(r,"blur",i):ty(r,"focusout",i));return{unbind:function(){ot(c,function(t){t.unbind()}),f.unbind(),d.unbind(),g.unbind(),l.unbind(),s.get().each(clearTimeout),m.get().each(clearTimeout)}}},ly=function(t,n){var e=Mt(t,"target").getOr(n);return se(e)},fy=Rt([{stopped:[]},{resume:["element"]},{complete:[]}]),dy=function(t,o,n,e,r,i){var u,a,c,s,l=t(o,e),f=(u=n,a=r,c=se(!1),s=se(!1),{stop:function(){c.set(!0)},cut:function(){s.set(!0)},isStopped:c.get,isCut:s.get,event:u,setSource:a.set,getSource:a.get});return l.fold(function(){return i.logEventNoHandlers(o,e),fy.complete()},function(n){var e=n.descHandler;return mi(e)(f),f.isStopped()?(i.logEventStopped(o,n.element,e.purpose),fy.stopped()):f.isCut()?(i.logEventCut(o,n.element,e.purpose),fy.complete()):xr(n.element).fold(function(){return i.logNoParent(o,n.element,e.purpose),fy.complete()},function(t){return i.logEventResponse(o,n.element,e.purpose),fy.resume(t)})})},my=function(n,e,o,t,r,i){return dy(n,e,o,t,r,i).fold(function(){return!0},function(t){return my(n,e,o,t,r,i)},function(){return!1})},gy=function(t,n,e){var o,r,i=(o=n,r=se(!1),{stop:function(){r.set(!0)},cut:$,isStopped:r.get,isCut:c,event:o,setSource:u("Cannot set source of a broadcasted event"),getSource:u("Cannot get source of a broadcasted event")});return ot(t,function(t){var n=t.descHandler;mi(n)(i)}),i.isStopped()},py=function(t,n,e,o,r){var i=ly(e,o);return my(t,n,e,o,i,r)},hy=function(t,n){return{element:t,descHandler:n}},vy=function(t,n){return{id:t,descHandler:n}};function by(){var i={};return{registerId:function(o,r,t){Ot(t,function(t,n){var e=i[n]!==undefined?i[n]:{};e[r]=di(t,o),i[n]=e})},unregisterId:function(e){Ot(i,function(t,n){t.hasOwnProperty(e)&&delete t[e]})},filterByType:function(t){return Mt(i,t).map(function(t){return Bt(t,function(t,n){return vy(n,t)})}).getOr([])},find:function(t,n,e){var r=Mt(i,n);return Ye(e,function(t){return e=r,Qr(o=t).fold(function(){return st.none()},function(n){return e.bind(function(t){return Mt(t,n)}).map(function(t){return hy(o,t)})});var e,o},t)}}}function yy(){var o=by(),r={},i=function(o){var t=o.element;return Qr(t).fold(function(){return t="uid-",n=o.element,e=Xr(Kr+t),$r(n,e),e;var t,n,e},function(t){return t})},u=function(t){Qr(t.element).each(function(t){delete r[t],o.unregisterId(t)})};return{find:function(t,n,e){return o.find(t,n,e)},filter:function(t){return o.filterByType(t)},register:function(t){var n=i(t);It(r,n)&&function(t,n){var e=r[n];if(e!==t)throw new Error('The tagId "'+n+'" is already used by: '+jr(e.element)+"\nCannot use it for: "+jr(t.element)+"\nThe conflicting element is"+(zi(e.element)?" ":" not ")+"already in the DOM");u(t)}(t,n);var e=[t];o.registerId(e,n,t.events),r[n]=t},unregister:u,getById:function(t){return Mt(r,t)}}}var xy,wy,Sy,ky,Cy=Uf({name:"Container",factory:function(t){var n=t.dom,e=n.attributes,o=y(n,["attributes"]);return{uid:t.uid,dom:nt({tag:"div",attributes:nt({role:"presentation"},e)},o),components:t.components,behaviours:Hl(t.containerBehaviours),events:t.events,domModification:t.domModification,eventOrder:t.eventOrder}},configFields:[te("components",[]),Pl("containerBehaviours",[]),te("events",{}),te("domModification",{}),te("eventOrder",{})]}),Oy=function(e){var o=function(n){return xr(e.element).fold(function(){return!0},function(t){return je(n,t)})},r=yy(),s=function(t,n){return r.find(o,t,n)},t=sy(e.element,{triggerEvent:function(o,r){return na(o,r.target,function(t){return e=t,py(s,o,n=r,n.target,e);var n,e})}}),i={debugInfo:at("real"),triggerEvent:function(n,e,o){na(n,e,function(t){return py(s,n,o,e,t)})},triggerFocus:function(a,c){Qr(a).fold(function(){sc(a)},function(t){na(yo(),a,function(t){var n,e,o,r,i,u;return n=s,e=yo(),i=t,u=ly(o={originator:c,kill:$,prevent:$,target:a},r=a),dy(n,e,o,r,u,i),!1})})},triggerEscape:function(t,n){i.triggerEvent("keydown",t.element,n.event)},getByUid:function(t){return g(t)},getByDom:function(t){return p(t)},build:lu,addToGui:function(t){a(t)},removeFromGui:function(t){c(t)},addToWorld:function(t){n(t)},removeFromWorld:function(t){u(t)},broadcast:function(t){f(t)},broadcastOn:function(t,n){d(t,n)},broadcastEvent:function(t,n){m(t,n)},isConnected:b},n=function(t){t.connect(i),mr(t.element)||(r.register(t),ot(t.components(),n),i.triggerEvent(Bo(),t.element,{target:t.element}))},u=function(t){mr(t.element)||(ot(t.components(),u),r.unregister(t)),t.disconnect()},a=function(t){Zs(e,t)},c=function(t){el(t)},l=function(e){var t=r.filter(So());ot(t,function(t){var n=t.descHandler;mi(n)(e)})},f=function(t){l({universal:!0,data:t})},d=function(t,n){l({universal:!1,channels:t,data:n})},m=function(t,n){var e=r.filter(t);return gy(e,n)},g=function(t){return r.getById(t).fold(function(){return it.error(new Error('Could not find component with uid: "'+t+'" in system.'))},it.value)},p=function(t){var n=Qr(t).getOr("not found");return g(n)};return n(e),{root:e,element:e.element,destroy:function(){t.unbind(),Ar(e.element)},add:a,remove:c,getByUid:g,getByDom:p,addToWorld:n,removeFromWorld:u,broadcast:f,broadcastOn:d,broadcastEvent:m}},_y=at([te("prefix","form-field"),Pl("fieldBehaviours",[Kf,Vl])]),Ty=at([pf({schema:[Nn("dom")],name:"label"}),pf({factory:{sketch:function(t){return{uid:t.uid,dom:{tag:"span",styles:{display:"none"},attributes:{"aria-hidden":"true"},innerHtml:t.text}}}},schema:[Nn("text")],name:"aria-descriptor"}),mf({factory:{sketch:function(t){var n=qt(t,["factory"]);return t.factory.sketch(n)}},schema:[Nn("factory")],name:"field"})]),Ey=Wf({name:"FormField",configFields:_y(),partFields:Ty(),factory:function(r,t,n,e){var o=zl(r.fieldBehaviours,[Kf.config({find:function(t){return Tf(t,r,"field")}}),Vl.config({store:{mode:"manual",getValue:function(t){return Kf.getCurrent(t).bind(Vl.getValue)},setValue:function(t,n){Kf.getCurrent(t).each(function(t){Vl.setValue(t,n)})}}})]),i=Jo([ur(function(t,n){var o=Bf(t,r,["label","field","aria-descriptor"]);o.field().each(function(e){var n=Xr(r.prefix);o.label().each(function(t){Vr(t.element,"for",n),Vr(e.element,"id",n)}),o["aria-descriptor"]().each(function(t){var n=Xr(r.prefix);Vr(t.element,"id",n),Vr(e.element,"aria-describedby",n)})})})]),u={getField:function(t){return Tf(t,r,"field")},getLabel:function(t){return Tf(t,r,"label")}};return{uid:r.uid,dom:r.dom,components:t,behaviours:o,events:i,apis:u}},apis:{getField:function(t,n){return t.getField(n)},getLabel:function(t,n){return t.getLabel(n)}}}),By=/* */Object.freeze({__proto__:null,exhibit:function(t,n){return fi({attributes:Jt([{key:n.tabAttr,value:"true"}])})}}),Dy=[te("tabAttr","data-alloy-tabstop")],Ay=oc({fields:Dy,name:"tabstopping",active:By}),My=function(t,n,e,o){var r=Fy(t,n,e,o);return Ey.sketch(r)},Fy=function(t,n,e,o){return{dom:Iy(e),components:t.toArray().concat([n]),fieldBehaviours:nc(o)}},Iy=function(t){return{tag:"div",classes:["tox-form__group"].concat(t)}},Ry=function(t,n){return Ey.parts.label({dom:{tag:"label",classes:["tox-label"],innerHtml:n.translate(t)}})},Vy=Xr("form-component-change"),Py=Xr("form-close"),Hy=Xr("form-cancel"),zy=Xr("form-action"),Ny=Xr("form-submit"),Ly=Xr("form-block"),jy=Xr("form-unblock"),Uy=Xr("form-tabchange"),Wy=Xr("form-resize"),Gy=function(a,c){var t,n,e,o=a.label.map(function(t){return Ry(t,c)}),r=function(o){return function(n,e){Xu(e.event.target,"[data-collection-item-value]").each(function(t){o(n,e,t,Pr(t,"data-collection-item-value"))})}},i=function(t,n){var e=V(n,function(t){var n,e=Nh.translate(t.text),o=1===a.columns?'<div class="tox-collection__item-label">'+e+"</div>":"",r='<div class="tox-collection__item-icon">'+t.icon+"</div>",i={_:" "," - ":" ","-":" "},u=e.replace(/\_| \- |\-/g,function(t){return i[t]});return'<div class="tox-collection__item'+(c.isReadOnly()?" tox-collection__item--state-disabled":"")+'" tabindex="-1" data-collection-item-value="'+('"'===(n=t.value)?"&quot;":n)+'" title="'+u+'" aria-label="'+u+'">'+r+o+"</div>"}),o="auto"!==a.columns&&1<a.columns?R(e,a.columns):[e],r=V(o,function(t){return'<div class="tox-collection__group">'+t.join("")+"</div>"});Ir(t.element,r.join(""))},u=r(function(t,n,e,o){n.stop(),c.isReadOnly()||Go(t,zy,{name:a.name,value:o})}),s=[Zo(uo(),r(function(t,n,e){sc(e)})),Zo(go(),u),Zo(Oo(),u),Zo(ao(),r(function(t,n,e){Gu(t.element,"."+sh).each(function(t){Ti(t,sh)}),Oi(e,sh)})),Zo(co(),r(function(t){Gu(t.element,"."+sh).each(function(t){Ti(t,sh)})})),sr(r(function(t,n,e,o){Go(t,zy,{name:a.name,value:o})}))],l=function(t,n){return V(ss(t.element,".tox-collection__item"),n)},f=Ey.parts.field({dom:{tag:"div",classes:["tox-collection"].concat(1!==a.columns?["tox-collection--grid"]:["tox-collection--list"])},components:[],factory:{sketch:ct},behaviours:nc([tv.config({disabled:c.isReadOnly,onDisabled:function(t){l(t,function(t){Oi(t,"tox-collection__item--state-disabled"),Vr(t,"aria-disabled",!0)})},onEnabled:function(t){l(t,function(t){Ti(t,"tox-collection__item--state-disabled"),Nr(t,"aria-disabled")})}}),_v(),ug.config({}),Vl.config({store:{mode:"memory",initialValue:[]},onSetValue:function(o,t){i(o,t),"auto"===a.columns&&Eh(o,5,"tox-collection__item").each(function(t){var n=t.numRows,e=t.numColumns;ng.setGridSize(o,n,e)}),Wo(o,Wy)}}),Ay.config({}),ng.config((n=a.columns,e="normal",1===n?{mode:"menu",moveOnTab:!1,selector:".tox-collection__item"}:"auto"===n?{mode:"flatgrid",selector:".tox-collection__item",initSize:{numColumns:1,numRows:1}}:{mode:"matrix",selectors:{row:"color"===e?".tox-swatches__row":".tox-collection__group",cell:"color"===e?"."+rh:"."+oh}})),ag("collection-events",s)]),eventOrder:((t={})[ko()]=["disabling","alloy.base.behaviour","collection-events"],t)});return My(o,f,["tox-form__group--collection"],[])},Xy=at([qn("data"),te("inputAttributes",{}),te("inputStyles",{}),te("tag","input"),te("inputClasses",[]),la("onSetValue"),te("styles",{}),te("eventOrder",{}),Pl("inputBehaviours",[Vl,dg]),te("selectOnFocus",!0)]),Yy=function(t){return nc([dg.config({onFocus:t.selectOnFocus?function(t){var n=t.element,e=Zi(n);n.dom.setSelectionRange(0,e.length)}:$})])},qy=function(t){return{tag:t.tag,attributes:nt({type:"text"},t.inputAttributes),styles:t.inputStyles,classes:t.inputClasses}},Ky=Uf({name:"Input",configFields:Xy(),factory:function(t,n){return{uid:t.uid,dom:qy(t),components:[],behaviours:nt(nt({},Yy(e=t)),zl(e.inputBehaviours,[Vl.config({store:nt(nt({mode:"manual"},e.data.map(function(t){return{initialValue:t}}).getOr({})),{getValue:function(t){return Zi(t.element)},setValue:function(t,n){Zi(t.element)!==n&&tu(t.element,n)}}),onSetValue:e.onSetValue})])),eventOrder:t.eventOrder};var e}}),Jy={},$y={exports:Jy};xy=undefined,wy=Jy,Sy=$y,ky=undefined,function(t){"object"==typeof wy&&void 0!==Sy?Sy.exports=t():"function"==typeof xy&&xy.amd?xy([],t):("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).EphoxContactWrapper=t()}(function(){return function l(i,u,a){function c(n,t){if(!u[n]){if(!i[n]){var e="function"==typeof ky&&ky;if(!t&&e)return e(n,!0);if(s)return s(n,!0);var o=new Error("Cannot find module '"+n+"'");throw o.code="MODULE_NOT_FOUND",o}var r=u[n]={exports:{}};i[n][0].call(r.exports,function(t){return c(i[n][1][t]||t)},r,r.exports,l,i,u,a)}return u[n].exports}for(var s="function"==typeof ky&&ky,t=0;t<a.length;t++)c(a[t]);return c}({1:[function(t,n,e){var o,r,i=n.exports={};function u(){throw new Error("setTimeout has not been defined")}function a(){throw new Error("clearTimeout has not been defined")}function c(t){if(o===setTimeout)return setTimeout(t,0);if((o===u||!o)&&setTimeout)return o=setTimeout,setTimeout(t,0);try{return o(t,0)}catch(n){try{return o.call(null,t,0)}catch(n){return o.call(this,t,0)}}}!function(){try{o="function"==typeof setTimeout?setTimeout:u}catch(t){o=u}try{r="function"==typeof clearTimeout?clearTimeout:a}catch(t){r=a}}();var s,l=[],f=!1,d=-1;function m(){f&&s&&(f=!1,s.length?l=s.concat(l):d=-1,l.length&&g())}function g(){if(!f){var t=c(m);f=!0;for(var n=l.length;n;){for(s=l,l=[];++d<n;)s&&s[d].run();d=-1,n=l.length}s=null,f=!1,function(t){if(r===clearTimeout)return clearTimeout(t);if((r===a||!r)&&clearTimeout)return r=clearTimeout,clearTimeout(t);try{r(t)}catch(n){try{return r.call(null,t)}catch(n){return r.call(this,t)}}}(t)}}function p(t,n){this.fun=t,this.array=n}function h(){}i.nextTick=function(t){var n=new Array(arguments.length-1);if(1<arguments.length)for(var e=1;e<arguments.length;e++)n[e-1]=arguments[e];l.push(new p(t,n)),1!==l.length||f||c(g)},p.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=h,i.addListener=h,i.once=h,i.off=h,i.removeListener=h,i.removeAllListeners=h,i.emit=h,i.prependListener=h,i.prependOnceListener=h,i.listeners=function(t){return[]},i.binding=function(t){throw new Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(t){throw new Error("process.chdir is not supported")},i.umask=function(){return 0}},{}],2:[function(t,f,n){(function(n){function o(){}function u(t){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");if("function"!=typeof t)throw new TypeError("not a function");this._state=0,this._handled=!1,this._value=undefined,this._deferreds=[],l(t,this)}function r(o,r){for(;3===o._state;)o=o._value;0!==o._state?(o._handled=!0,u._immediateFn(function(){var t,n=1===o._state?r.onFulfilled:r.onRejected;if(null!==n){try{t=n(o._value)}catch(e){return void a(r.promise,e)}i(r.promise,t)}else(1===o._state?i:a)(r.promise,o._value)})):o._deferreds.push(r)}function i(t,n){try{if(n===t)throw new TypeError("A promise cannot be resolved with itself.");if(n&&("object"==typeof n||"function"==typeof n)){var e=n.then;if(n instanceof u)return t._state=3,t._value=n,void c(t);if("function"==typeof e)return void l((o=e,r=n,function(){o.apply(r,arguments)}),t)}t._state=1,t._value=n,c(t)}catch(i){a(t,i)}var o,r}function a(t,n){t._state=2,t._value=n,c(t)}function c(t){2===t._state&&0===t._deferreds.length&&u._immediateFn(function(){t._handled||u._unhandledRejectionFn(t._value)});for(var n=0,e=t._deferreds.length;n<e;n++)r(t,t._deferreds[n]);t._deferreds=null}function s(t,n,e){this.onFulfilled="function"==typeof t?t:null,this.onRejected="function"==typeof n?n:null,this.promise=e}function l(t,n){var e=!1;try{t(function(t){e||(e=!0,i(n,t))},function(t){e||(e=!0,a(n,t))})}catch(o){if(e)return;e=!0,a(n,o)}}var t,e;t=this,e=setTimeout,u.prototype["catch"]=function(t){return this.then(null,t)},u.prototype.then=function(t,n){var e=new this.constructor(o);return r(this,new s(t,n,e)),e},u.all=function(t){var c=Array.prototype.slice.call(t);return new u(function(r,i){if(0===c.length)return r([]);var u=c.length;for(var t=0;t<c.length;t++)!function a(n,t){try{if(t&&("object"==typeof t||"function"==typeof t)){var e=t.then;if("function"==typeof e)return void e.call(t,function(t){a(n,t)},i)}c[n]=t,0==--u&&r(c)}catch(o){i(o)}}(t,c[t])})},u.resolve=function(n){return n&&"object"==typeof n&&n.constructor===u?n:new u(function(t){t(n)})},u.reject=function(e){return new u(function(t,n){n(e)})},u.race=function(r){return new u(function(t,n){for(var e=0,o=r.length;e<o;e++)r[e].then(t,n)})},u._immediateFn="function"==typeof n?function(t){n(t)}:function(t){e(t,0)},u._unhandledRejectionFn=function(t){"undefined"!=typeof console&&console&&console.warn("Possible Unhandled Promise Rejection:",t)},u._setImmediateFn=function(t){u._immediateFn=t},u._setUnhandledRejectionFn=function(t){u._unhandledRejectionFn=t},void 0!==f&&f.exports?f.exports=u:t.Promise||(t.Promise=u)}).call(this,t("timers").setImmediate)},{timers:3}],3:[function(c,t,s){(function(t,n){var o=c("process/browser.js").nextTick,e=Function.prototype.apply,r=Array.prototype.slice,i={},u=0;function a(t,n){this._id=t,this._clearFn=n}s.setTimeout=function(){return new a(e.call(setTimeout,window,arguments),clearTimeout)},s.setInterval=function(){return new a(e.call(setInterval,window,arguments),clearInterval)},s.clearTimeout=s.clearInterval=function(t){t.close()},a.prototype.unref=a.prototype.ref=function(){},a.prototype.close=function(){this._clearFn.call(window,this._id)},s.enroll=function(t,n){clearTimeout(t._idleTimeoutId),t._idleTimeout=n},s.unenroll=function(t){clearTimeout(t._idleTimeoutId),t._idleTimeout=-1},s._unrefActive=s.active=function(t){clearTimeout(t._idleTimeoutId);var n=t._idleTimeout;0<=n&&(t._idleTimeoutId=setTimeout(function(){t._onTimeout&&t._onTimeout()},n))},s.setImmediate="function"==typeof t?t:function(t){var n=u++,e=!(arguments.length<2)&&r.call(arguments,1);return i[n]=!0,o(function(){i[n]&&(e?t.apply(null,e):t.call(null),s.clearImmediate(n))}),n},s.clearImmediate="function"==typeof n?n:function(t){delete i[t]}}).call(this,c("timers").setImmediate,c("timers").clearImmediate)},{"process/browser.js":1,timers:3}],4:[function(t,n,e){var o=t("promise-polyfill"),r="undefined"!=typeof window?window:Function("return this;")();n.exports={boltExport:r.Promise||o}},{"promise-polyfill":2}]},{},[4])(4)});var Qy,Zy,tx=$y.exports.boltExport,nx=function(t){var e=st.none(),n=[],o=function(t){r()?u(t):n.push(t)},r=function(){return e.isSome()},i=function(t){ot(t,u)},u=function(n){e.each(function(t){setTimeout(function(){n(t)},0)})};return t(function(t){r()||(e=st.some(t),i(n),n=[])}),{get:o,map:function(e){return nx(function(n){o(function(t){n(e(t))})})},isReady:r}},ex={nu:nx,pure:function(n){return nx(function(t){t(n)})}},ox=function(t){setTimeout(function(){throw t},0)},rx=function(e){var t=function(t){e().then(t,ox)};return{map:function(t){return rx(function(){return e().then(t)})},bind:function(n){return rx(function(){return e().then(function(t){return n(t).toPromise()})})},anonBind:function(t){return rx(function(){return e().then(function(){return t.toPromise()})})},toLazy:function(){return ex.nu(t)},toCached:function(){var t=null;return rx(function(){return null===t&&(t=e()),t})},toPromise:e,get:t}},ix=function(t){return rx(function(){return new tx(t)})},ux=function(t){return rx(function(){return tx.resolve(t)})},ax=["input","textarea"],cx=function(t){var n=lr(t);return M(ax,n)},sx=function(t,n){var e=n.getRoot(t).getOr(t.element);Ti(e,n.invalidClass),n.notify.each(function(n){cx(t.element)&&Vr(t.element,"aria-invalid",!1),n.getContainer(t).each(function(t){Ir(t,n.validHtml)}),n.onValid(t)})},lx=function(n,t,e,o){var r=t.getRoot(n).getOr(n.element);Oi(r,t.invalidClass),t.notify.each(function(t){cx(n.element)&&Vr(n.element,"aria-invalid",!0),t.getContainer(n).each(function(t){Ir(t,o)}),t.onInvalid(n,o)})},fx=function(n,t,e){return t.validator.fold(function(){return ux(it.value(!0))},function(t){return t.validate(n)})},dx=function(n,e,t){return e.notify.each(function(t){t.onValidate(n)}),fx(n,e).map(function(t){return n.getSystem().isConnected()?t.fold(function(t){return lx(n,e,0,t),it.error(t)},function(t){return sx(n,e),it.value(t)}):it.error("No longer in system")})},mx=/* */Object.freeze({__proto__:null,markValid:sx,markInvalid:lx,query:fx,run:dx,isInvalid:function(t,n){var e=n.getRoot(t).getOr(t.element);return Ei(e,n.invalidClass)}}),gx=/* */Object.freeze({__proto__:null,events:function(n,t){return n.validator.map(function(t){return Jo([Zo(t.onEvent,function(t){dx(t,n).get(ct)})].concat(t.validateOnLoad?[ur(function(t){dx(t,n).get($)})]:[]))}).getOr({})}}),px=[Nn("invalidClass"),te("getRoot",st.none),Zn("notify",[te("aria","alert"),te("getContainer",st.none),te("validHtml",""),la("onValid"),la("onInvalid"),la("onValidate")]),Zn("validator",[Nn("validate"),te("onEvent","input"),te("validateOnLoad",!0)])],hx=oc({fields:px,name:"invalidating",active:gx,apis:mx,extra:{validation:function(e){return function(t){var n=Vl.getValue(t);return ux(e(n))}}}}),vx=/* */Object.freeze({__proto__:null,getCoupled:function(t,n,e,o){return e.getOrCreate(t,n,o)}}),bx=[Ln("others",Cn(it.value,An()))],yx=oc({fields:bx,name:"coupling",apis:vx,state:/* */Object.freeze({__proto__:null,init:function(){var i={},t=at({});return si({readState:t,getOrCreate:function(e,o,r){var t=kt(o.others);if(t)return Mt(i,r).getOrThunk(function(){var t=Mt(o.others,r).getOrDie("No information found for coupled component: "+r)(e),n=e.getSystem().build(t);return i[r]=n});throw new Error("Cannot find coupled component: "+r+". Known coupled components: "+JSON.stringify(t,null,2))}})}})}),xx=at("sink"),wx=at(pf({name:xx(),overrides:at({dom:{tag:"div"},behaviours:nc([qs.config({useFixed:b})]),events:Jo([rr(so()),rr(eo()),rr(go())])})}));(Zy=Qy=Qy||{})[Zy.HighlightFirst=0]="HighlightFirst",Zy[Zy.HighlightNone=1]="HighlightNone";var Sx=function(t,n){var e=t.getHotspot(n).getOr(n),o=t.getAnchorOverrides();return t.layouts.fold(function(){return{anchor:"hotspot",hotspot:e,overrides:o}},function(t){return{anchor:"hotspot",hotspot:e,overrides:o,layouts:t}})},kx=function(t,n,e,o,r,i,u){var a,c,s,l,f,d,m,g,p,h,v=Sx(t,e);return(c=v,l=o,f=r,d=u,m=n,g=s=e,p=(0,(a=t).fetch)(g).map(m),h=Tx(s,a),p.map(function(t){return t.bind(function(t){return st.from(qg.sketch(nt(nt({},f.menu()),{uid:Zr(""),data:t,highlightImmediately:d===Qy.HighlightFirst,onOpenMenu:function(t,n){var e=h().getOrDie();qs.position(e,c,n),hl.decloak(l)},onOpenSubmenu:function(t,n,e){var o=h().getOrDie();qs.position(o,{anchor:"submenu",item:n},e),hl.decloak(l)},onRepositionMenu:function(t,n,e){var o=h().getOrDie();qs.position(o,c,n),ot(e,function(t){qs.position(o,{anchor:"submenu",item:t.triggeringItem},t.triggeredMenu)})},onEscape:function(){return dg.focus(s),hl.close(l),st.some(!0)}})))})})).map(function(t){return t.fold(function(){hl.isOpen(o)&&hl.close(o)},function(t){hl.cloak(o),hl.open(o,t),i(o)}),o})},Cx=function(t,n,e,o,r,i,u){return hl.close(o),ux(o)},Ox=function(t,n,e,o,r,i){var u=yx.getCoupled(e,"sandbox");return(hl.isOpen(u)?Cx:kx)(t,n,e,u,o,r,i)},_x=function(t,n,e){var o,r,i=Kf.getCurrent(n).getOr(n),u=Su(t.element);e?Wi(i.element,"min-width",u+"px"):(o=i.element,r=u,wu.set(o,r))},Tx=function(n,t){return n.getSystem().getByUid(t.uid+"-"+xx()).map(function(t){return function(){return it.value(t)}}).getOrThunk(function(){return t.lazySink.fold(function(){return function(){return it.error(new Error("No internal sink is specified, nor could an external sink be found"))}},function(t){return function(){return t(n)}})})},Ex=function(t){hl.getState(t).each(function(t){qg.repositionMenus(t)})},Bx=function(o,r,i){var u=Yu(),t=Tx(r,o);return{dom:{tag:"div",classes:o.sandboxClasses,attributes:{id:u.id,role:"listbox"}},behaviours:Ll(o.sandboxBehaviours,[Vl.config({store:{mode:"memory",initialValue:r}}),hl.config({onOpen:function(t,n){var e=Sx(o,r);u.link(r.element),o.matchWidth&&_x(e.hotspot,n,o.useMinWidth),o.onOpen(e,t,n),i!==undefined&&i.onOpen!==undefined&&i.onOpen(t,n)},onClose:function(t,n){u.unlink(r.element),i!==undefined&&i.onClose!==undefined&&i.onClose(t,n)},isPartOf:function(t,n,e){return Ku(n,e)||Ku(r,e)},getAttachPoint:function(){return t().getOrDie()}}),Kf.config({find:function(t){return hl.getState(t).bind(function(t){return Kf.getCurrent(t)})}}),ac.config({channels:nt(nt({},wl({isExtraPart:c})),kl({doReposition:Ex}))})])}},Dx=function(t){var n=yx.getCoupled(t,"sandbox");Ex(n)},Ax=function(){return[te("sandboxClasses",[]),Nl("sandboxBehaviours",[Kf,ac,hl,Vl])]},Mx=at([Nn("dom"),Nn("fetch"),la("onOpen"),fa("onExecute"),te("getHotspot",st.some),te("getAnchorOverrides",at({})),Nc(),Pl("dropdownBehaviours",[Cg,yx,ng,dg]),Nn("toggleClass"),te("eventOrder",{}),qn("lazySink"),te("matchWidth",!1),te("useMinWidth",!1),qn("role")].concat(Ax())),Fx=at([gf({schema:[aa()],name:"menu",defaults:function(t){return{onExecute:t.onExecute}}}),wx()]),Ix=Wf({name:"Dropdown",configFields:Mx(),partFields:Fx(),factory:function(n,t,e,o){var r,i,u=function(t){hl.getState(t).each(function(t){qg.highlightPrimary(t)})},a={expand:function(t){Cg.isOn(t)||Ox(n,function(t){return t},t,o,$,Qy.HighlightNone).get($)},open:function(t){Cg.isOn(t)||Ox(n,function(t){return t},t,o,$,Qy.HighlightFirst).get($)},isOpen:Cg.isOn,close:function(t){Cg.isOn(t)&&Ox(n,function(t){return t},t,o,$,Qy.HighlightFirst).get($)},repositionMenus:function(t){Cg.isOn(t)&&Dx(t)}},c=function(t,n){return Xo(t),st.some(!0)};return{uid:n.uid,dom:n.dom,components:t,behaviours:zl(n.dropdownBehaviours,[Cg.config({toggleClass:n.toggleClass,aria:{mode:"expanded"}}),yx.config({others:{sandbox:function(t){return Bx(n,t,{onOpen:function(){Cg.on(t)},onClose:function(){Cg.off(t)}})}}}),ng.config({mode:"special",onSpace:c,onEnter:c,onDown:function(t,n){var e;return Ix.isOpen(t)?(e=yx.getCoupled(t,"sandbox"),u(e)):Ix.open(t),st.some(!0)},onEscape:function(t,n){return Ix.isOpen(t)?(Ix.close(t),st.some(!0)):st.none()}}),dg.config({})]),events:_g(st.some(function(t){Ox(n,function(t){return t},t,o,u,Qy.HighlightFirst).get($)})),eventOrder:nt(nt({},n.eventOrder),((r={})[ko()]=["disabling","toggling","alloy.base.behaviour"],r)),apis:a,domModification:{attributes:nt(nt({"aria-haspopup":"true"},n.role.fold(function(){return{}},function(t){return{role:t}})),"button"===n.dom.tag?{type:(i="type",Mt(n.dom,"attributes").bind(function(t){return Mt(t,i)}).getOr("button"))}:{})}}},apis:{open:function(t,n){return t.open(n)},expand:function(t,n){return t.expand(n)},close:function(t,n){return t.close(n)},isOpen:function(t,n){return t.isOpen(n)},repositionMenus:function(t,n){return t.repositionMenus(n)}}}),Rx=oc({fields:[],name:"unselecting",active:/* */Object.freeze({__proto__:null,events:function(){return Jo([$o(ho(),b)])},exhibit:function(){return fi({styles:{"-webkit-user-select":"none","user-select":"none","-ms-user-select":"none","-moz-user-select":"-moz-none"},attributes:{unselectable:"on"}})}})}),Vx=Xr("color-input-change"),Px=Xr("color-swatch-change"),Hx=Xr("color-picker-cancel"),zx=function(e,n,o){var r,i,t=Ey.parts.field({factory:Ky,inputClasses:["tox-textfield"],onSetValue:function(t){return hx.run(t).get(function(){})},inputBehaviours:nc([tv.config({disabled:n.providers.isReadOnly}),_v(),Ay.config({}),hx.config({invalidClass:"tox-textbox-field-invalid",getRoot:function(t){return xr(t.element)},notify:{onValid:function(t){var n=Vl.getValue(t);Go(t,Vx,{color:n})}},validator:{validateOnLoad:!1,validate:function(t){var n=Vl.getValue(t);if(0===n.length)return ux(it.value(!0));var e=fe.fromTag("span");Wi(e,"background-color",n);var o=Ki(e,"background-color").fold(function(){return it.error("blah")},function(t){return it.value(n)});return ux(o)}}})]),selectOnFocus:!1}),u=e.label.map(function(t){return Ry(t,n.providers)}),a=function(t,n){Go(t,Px,{value:n})},c=sp((r={dom:{tag:"span",attributes:{"aria-label":n.providers.translate("Color swatch")}},layouts:{onRtl:function(){return[Ra,Ia,za]},onLtr:function(){return[Ia,Ra,za]}},components:[],fetch:Eb(o.getColors(),o.hasCustomColors()),columns:o.getColorCols(),presets:"color",onItemAction:function(t,e){c.getOpt(t).each(function(n){"custom"===e?o.colorPicker(function(t){t.fold(function(){return Wo(n,Hx)},function(t){a(n,t),kb(t)})},"#ffffff"):a(n,"remove"===e?"":e)})}},i=n,Ix.sketch({dom:r.dom,components:r.components,toggleClass:"mce-active",dropdownBehaviours:nc([Ev(i.providers.isReadOnly),_v(),Rx.config({}),Ay.config({})]),layouts:r.layouts,sandboxClasses:["tox-dialog__popups"],lazySink:i.getSink,fetch:function(n){return ix(function(t){return r.fetch(t)}).map(function(t){return st.from($b(Ht(Fb(Xr("menu-value"),t,function(t){r.onItemAction(n,t)},r.columns,r.presets,nh.CLOSE_ON_EXECUTE,function(){return!1},i.providers),{movement:Rb(r.columns,r.presets)})))})},parts:{menu:gh(0,0,r.presets)}})));return Ey.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:u.toArray().concat([{dom:{tag:"div",classes:["tox-color-input"]},components:[t,c.asSpec()]}]),fieldBehaviours:nc([ag("form-field-events",[Zo(Vx,function(t,n){c.getOpt(t).each(function(t){Wi(t.element,"background-color",n.event.color)}),Go(t,Vy,{name:e.name})}),Zo(Px,function(n,e){Ey.getField(n).each(function(t){Vl.setValue(t,e.event.value),Kf.getCurrent(n).each(dg.focus)})}),Zo(Hx,function(n,t){Ey.getField(n).each(function(t){Kf.getCurrent(n).each(dg.focus)})})])])})},Nx=function(t,n,e){return{hue:t,saturation:n,value:e}},Lx=Xr("rgb-hex-update"),jx=Xr("slider-update"),Ux=Xr("palette-update"),Wx=pf({schema:[Nn("dom")],name:"label"}),Gx=function(n){return pf({name:n+"-edge",overrides:function(t){return t.model.manager.edgeActions[n].fold(function(){return{}},function(o){return{events:Jo([tr(Qe(),function(t,n,e){return o(t,e)},[t]),tr(eo(),function(t,n,e){return o(t,e)},[t]),tr(oo(),function(t,n,e){e.mouseIsDown.get()&&o(t,e)},[t])])}})}})},Xx=Gx("top-left"),Yx=Gx("top"),qx=Gx("top-right"),Kx=Gx("right"),Jx=Gx("bottom-right"),$x=Gx("bottom"),Qx=Gx("bottom-left"),Zx=[Wx,Gx("left"),Kx,Yx,$x,Xx,qx,Qx,Jx,mf({name:"thumb",defaults:at({dom:{styles:{position:"absolute"}}}),overrides:function(t){return{events:Jo([er(Qe(),t,"spectrum"),er(Ze(),t,"spectrum"),er(to(),t,"spectrum"),er(eo(),t,"spectrum"),er(oo(),t,"spectrum"),er(io(),t,"spectrum")])}}}),mf({schema:[ce("mouseIsDown",function(){return se(!1)})],name:"spectrum",overrides:function(e){var o=e.model.manager,r=function(n,t){return o.getValueFromEvent(t).map(function(t){return o.setValueFrom(n,e,t)})};return{behaviours:nc([ng.config({mode:"special",onLeft:function(t){return o.onLeft(t,e)},onRight:function(t){return o.onRight(t,e)},onUp:function(t){return o.onUp(t,e)},onDown:function(t){return o.onDown(t,e)}}),dg.config({})]),events:Jo([Zo(Qe(),r),Zo(Ze(),r),Zo(eo(),r),Zo(oo(),function(t,n){e.mouseIsDown.get()&&r(t,n)})])}}})],tw=at("slider.change.value"),nw=function(t){var n=t.event.raw;return-1===n.type.indexOf("touch")?n.clientX!==undefined?st.some(n).map(function(t){return vu(t.clientX,t.clientY)}):st.none():n.touches!==undefined&&1===n.touches.length?st.some(n.touches[0]).map(function(t){return vu(t.clientX,t.clientY)}):st.none()},ew=function(t){return t.model.minX},ow=function(t){return t.model.minY},rw=function(t){return t.model.minX-1},iw=function(t){return t.model.minY-1},uw=function(t){return t.model.maxX},aw=function(t){return t.model.maxY},cw=function(t){return t.model.maxX+1},sw=function(t){return t.model.maxY+1},lw=function(t,n,e){return n(t)-e(t)},fw=function(t){return lw(t,uw,ew)},dw=function(t){return lw(t,aw,ow)},mw=function(t){return fw(t)/2},gw=function(t){return dw(t)/2},pw=function(t){return t.stepSize},hw=function(t){return t.snapToGrid},vw=function(t){return t.snapStart},bw=function(t){return t.rounded},yw=function(t,n){return t[n+"-edge"]!==undefined},xw=function(t){return yw(t,"left")},ww=function(t){return yw(t,"right")},Sw=function(t){return yw(t,"top")},kw=function(t){return yw(t,"bottom")},Cw=function(t){return t.model.value.get()},Ow=function(t){return{x:t}},_w=function(t){return{y:t}},Tw=function(t,n){return{x:t,y:n}},Ew=function(t,n){Go(t,tw(),{value:n})},Bw=function(t,n,e,o){return t<n?t:e<t?e:t===n?n-1:Math.max(n,t-o)},Dw=function(t,n,e,o){return e<t?t:t<n?n:t===e?e+1:Math.min(e,t+o)},Aw=function(t,n,e){return Math.max(n,Math.min(e,t))},Mw=function(t){var n=t.min,e=t.max,o=t.range,r=t.value,i=t.step,u=t.snap,a=t.snapStart,c=t.rounded,s=t.hasMinEdge,l=t.hasMaxEdge,f=t.minBound,d=t.maxBound,m=t.screenRange,g=s?n-1:n,p=l?e+1:e;if(r<f)return g;if(d<r)return p;var h,v,b,y,x,w,S,k=(x=r,w=f,S=d,Math.min(S,Math.max(x,w))-w),C=Aw(k/m*o+n,g,p);return u&&n<=C&&C<=e?(h=C,v=n,b=e,y=i,a.fold(function(){var t=h-v,n=Math.round(t/y)*y;return Aw(v+n,v-1,b+1)},function(t){var n=(h-t)%y,e=Math.round(n/y),o=Math.floor((h-t)/y),r=Math.floor((b-t)/y),i=t+Math.min(r,o+e)*y;return Math.max(t,i)})):c?Math.round(C):C},Fw=function(t){var n=t.min,e=t.max,o=t.range,r=t.value,i=t.hasMinEdge,u=t.hasMaxEdge,a=t.maxBound,c=t.maxOffset,s=t.centerMinEdge,l=t.centerMaxEdge;return r<n?i?0:s:e<r?u?a:l:(r-n)/o*c},Iw="left",Rw=function(t){return t.element.dom.getBoundingClientRect()},Vw=function(t,n){return t[n]},Pw=function(t){var n=Rw(t);return Vw(n,Iw)},Hw=function(t){var n=Rw(t);return Vw(n,"right")},zw=function(t){var n=Rw(t);return Vw(n,"top")},Nw=function(t){var n=Rw(t);return Vw(n,"bottom")},Lw=function(t){var n=Rw(t);return Vw(n,"width")},jw=function(t){var n=Rw(t);return Vw(n,"height")},Uw=function(t,n,e){return(t+n)/2-e},Ww=function(t,n){var e=Rw(t),o=Rw(n),r=Vw(e,Iw),i=Vw(e,"right"),u=Vw(o,Iw);return Uw(r,i,u)},Gw=function(t,n){var e=Rw(t),o=Rw(n),r=Vw(e,"top"),i=Vw(e,"bottom"),u=Vw(o,"top");return Uw(r,i,u)},Xw=function(t,n){Go(t,tw(),{value:n})},Yw=function(t){return{x:t}},qw=function(t,n,e){var o={min:ew(n),max:uw(n),range:fw(n),value:e,step:pw(n),snap:hw(n),snapStart:vw(n),rounded:bw(n),hasMinEdge:xw(n),hasMaxEdge:ww(n),minBound:Pw(t),maxBound:Hw(t),screenRange:Lw(t)};return Mw(o)},Kw=function(i){return function(t,n){return e=t,r=(0<i?Dw:Bw)(Cw(o=n).x,ew(o),uw(o),pw(o)),Xw(e,Yw(r)),st.some(r).map(function(){return!0});var e,o,r}},Jw=function(t,n,e,o,r,i){var u,a,c,s,l,f,d,m,g,p=(a=i,c=e,s=o,l=r,f=Lw(u=n),d=s.bind(function(t){return st.some(Ww(t,u))}).getOr(0),m=l.bind(function(t){return st.some(Ww(t,u))}).getOr(f),g={min:ew(a),max:uw(a),range:fw(a),value:c,hasMinEdge:xw(a),hasMaxEdge:ww(a),minBound:Pw(u),minOffset:0,maxBound:Hw(u),maxOffset:f,centerMinEdge:d,centerMaxEdge:m},Fw(g));return Pw(n)-Pw(t)+p},$w=Kw(-1),Qw=Kw(1),Zw=st.none,tS=st.none,nS={"top-left":st.none(),top:st.none(),"top-right":st.none(),right:st.some(function(t,n){Ew(t,Ow(cw(n)))}),"bottom-right":st.none(),bottom:st.none(),"bottom-left":st.none(),left:st.some(function(t,n){Ew(t,Ow(rw(n)))})},eS=/* */Object.freeze({__proto__:null,setValueFrom:function(t,n,e){var o=qw(t,n,e),r=Yw(o);return Xw(t,r),o},setToMin:function(t,n){var e=ew(n);Xw(t,Yw(e))},setToMax:function(t,n){var e=uw(n);Xw(t,Yw(e))},findValueOfOffset:qw,getValueFromEvent:function(t){return nw(t).map(function(t){return t.left})},findPositionOfValue:Jw,setPositionFromValue:function(t,n,e,o){var r=Cw(e),i=Jw(t,o.getSpectrum(t),r.x,o.getLeftEdge(t),o.getRightEdge(t),e),u=Su(n.element)/2;Wi(n.element,"left",i-u+"px")},onLeft:$w,onRight:Qw,onUp:Zw,onDown:tS,edgeActions:nS}),oS=function(t,n){Go(t,tw(),{value:n})},rS=function(t){return{y:t}},iS=function(t,n,e){var o={min:ow(n),max:aw(n),range:dw(n),value:e,step:pw(n),snap:hw(n),snapStart:vw(n),rounded:bw(n),hasMinEdge:Sw(n),hasMaxEdge:kw(n),minBound:zw(t),maxBound:Nw(t),screenRange:jw(t)};return Mw(o)},uS=function(i){return function(t,n){return e=t,r=(0<i?Dw:Bw)(Cw(o=n).y,ow(o),aw(o),pw(o)),oS(e,rS(r)),st.some(r).map(function(){return!0});var e,o,r}},aS=function(t,n,e,o,r,i){var u,a,c,s,l,f,d,m,g,p=(a=i,c=e,s=o,l=r,f=jw(u=n),d=s.bind(function(t){return st.some(Gw(t,u))}).getOr(0),m=l.bind(function(t){return st.some(Gw(t,u))}).getOr(f),g={min:ow(a),max:aw(a),range:dw(a),value:c,hasMinEdge:Sw(a),hasMaxEdge:kw(a),minBound:zw(u),minOffset:0,maxBound:Nw(u),maxOffset:f,centerMinEdge:d,centerMaxEdge:m},Fw(g));return zw(n)-zw(t)+p},cS=st.none,sS=st.none,lS=uS(-1),fS=uS(1),dS={"top-left":st.none(),top:st.some(function(t,n){Ew(t,_w(iw(n)))}),"top-right":st.none(),right:st.none(),"bottom-right":st.none(),bottom:st.some(function(t,n){Ew(t,_w(sw(n)))}),"bottom-left":st.none(),left:st.none()},mS=/* */Object.freeze({__proto__:null,setValueFrom:function(t,n,e){var o=iS(t,n,e),r=rS(o);return oS(t,r),o},setToMin:function(t,n){var e=ow(n);oS(t,rS(e))},setToMax:function(t,n){var e=aw(n);oS(t,rS(e))},findValueOfOffset:iS,getValueFromEvent:function(t){return nw(t).map(function(t){return t.top})},findPositionOfValue:aS,setPositionFromValue:function(t,n,e,o){var r=Cw(e),i=aS(t,o.getSpectrum(t),r.y,o.getTopEdge(t),o.getBottomEdge(t),e),u=gu(n.element)/2;Wi(n.element,"top",i-u+"px")},onLeft:cS,onRight:sS,onUp:lS,onDown:fS,edgeActions:dS}),gS=function(t,n){Go(t,tw(),{value:n})},pS=function(t,n){return{x:t,y:n}},hS=function(c,s){return function(t,n){return o=t,r=n,i=0<c?Dw:Bw,u=(e=s)?Cw(r).x:i(Cw(r).x,ew(r),uw(r),pw(r)),a=e?i(Cw(r).y,ow(r),aw(r),pw(r)):Cw(r).y,gS(o,pS(u,a)),st.some(u).map(function(){return!0});var e,o,r,i,u,a}},vS=nw,bS=hS(-1,!1),yS=hS(1,!1),xS=hS(-1,!0),wS=hS(1,!0),SS={"top-left":st.some(function(t,n){Ew(t,Tw(rw(n),iw(n)))}),top:st.some(function(t,n){Ew(t,Tw(mw(n),iw(n)))}),"top-right":st.some(function(t,n){Ew(t,Tw(cw(n),iw(n)))}),right:st.some(function(t,n){Ew(t,Tw(cw(n),gw(n)))}),"bottom-right":st.some(function(t,n){Ew(t,Tw(cw(n),sw(n)))}),bottom:st.some(function(t,n){Ew(t,Tw(mw(n),sw(n)))}),"bottom-left":st.some(function(t,n){Ew(t,Tw(rw(n),sw(n)))}),left:st.some(function(t,n){Ew(t,Tw(rw(n),gw(n)))})},kS=/* */Object.freeze({__proto__:null,setValueFrom:function(t,n,e){var o=qw(t,n,e.left),r=iS(t,n,e.top),i=pS(o,r);return gS(t,i),i},setToMin:function(t,n){var e=ew(n),o=ow(n);gS(t,pS(e,o))},setToMax:function(t,n){var e=uw(n),o=aw(n);gS(t,pS(e,o))},getValueFromEvent:vS,setPositionFromValue:function(t,n,e,o){var r=Cw(e),i=Jw(t,o.getSpectrum(t),r.x,o.getLeftEdge(t),o.getRightEdge(t),e),u=aS(t,o.getSpectrum(t),r.y,o.getTopEdge(t),o.getBottomEdge(t),e),a=Su(n.element)/2,c=gu(n.element)/2;Wi(n.element,"left",i-a+"px"),Wi(n.element,"top",u-c+"px")},onLeft:bS,onRight:yS,onUp:xS,onDown:wS,edgeActions:SS}),CS=Wf({name:"Slider",configFields:[te("stepSize",1),te("onChange",$),te("onChoose",$),te("onInit",$),te("onDragStart",$),te("onDragEnd",$),te("snapToGrid",!1),te("rounded",!0),qn("snapStart"),Ln("model",Dn("mode",{x:[te("minX",0),te("maxX",100),ce("value",function(t){return se(t.mode.minX)}),Nn("getInitialValue"),ga("manager",eS)],y:[te("minY",0),te("maxY",100),ce("value",function(t){return se(t.mode.minY)}),Nn("getInitialValue"),ga("manager",mS)],xy:[te("minX",0),te("maxX",100),te("minY",0),te("maxY",100),ce("value",function(t){return se({x:t.mode.minX,y:t.mode.minY})}),Nn("getInitialValue"),ga("manager",kS)]})),Pl("sliderBehaviours",[ng,Vl]),ce("mouseIsDown",function(){return se(!1)})],partFields:Zx,factory:function(i,t,n,e){var o,u=function(t){return Ef(t,i,"thumb")},a=function(t){return Ef(t,i,"spectrum")},r=function(t){return Tf(t,i,"left-edge")},c=function(t){return Tf(t,i,"right-edge")},s=function(t){return Tf(t,i,"top-edge")},l=function(t){return Tf(t,i,"bottom-edge")},f=i.model,d=f.manager,m=function(t,n){d.setPositionFromValue(t,n,i,{getLeftEdge:r,getRightEdge:c,getTopEdge:s,getBottomEdge:l,getSpectrum:a})},g=function(t,n){f.value.set(n);var e=u(t);return m(t,e),i.onChange(t,e,n),st.some(!0)},p=function(e){var t=i.mouseIsDown.get();i.mouseIsDown.set(!1),t&&Tf(e,i,"thumb").each(function(t){var n=f.value.get();i.onChoose(e,t,n)})},h=function(t,n){n.stop(),i.mouseIsDown.set(!0),i.onDragStart(t,u(t))},v=function(t,n){n.stop(),i.onDragEnd(t,u(t)),p(t)};return{uid:i.uid,dom:i.dom,components:t,behaviours:zl(i.sliderBehaviours,[ng.config({mode:"special",focusIn:function(t){return Tf(t,i,"spectrum").map(ng.focusIn).map(b)}}),Vl.config({store:{mode:"manual",getValue:function(t){return f.value.get()}}}),ac.config({channels:((o={})[yl()]={onReceive:p},o)})]),events:Jo([Zo(tw(),function(t,n){g(t,n.event.value)}),ur(function(t,n){var e=f.getInitialValue();f.value.set(e);var o=u(t);m(t,o);var r=a(t);i.onInit(t,o,r,f.value.get())}),Zo(Qe(),h),Zo(to(),v),Zo(eo(),h),Zo(io(),v)]),apis:{resetToMin:function(t){d.setToMin(t,i)},resetToMax:function(t){d.setToMax(t,i)},changeValue:g,refresh:m},domModification:{styles:{position:"relative"}}}},apis:{resetToMin:function(t,n){t.resetToMin(n)},resetToMax:function(t,n){t.resetToMax(n)},refresh:function(t,n){t.refresh(n)}}}),OS=[Pl("formBehaviours",[Vl])],_S=function(t){return"<alloy.field."+t+">"},TS=function(o,t){return{uid:o.uid,dom:o.dom,components:t,behaviours:zl(o.formBehaviours,[Vl.config({store:{mode:"manual",getValue:function(t){var n=Df(t,o);return _t(n,function(t,r){return t().bind(function(t){var n,e,o=Kf.getCurrent(t);return n=o,e=new Error("Cannot find a current component to extract the value from for form part '"+r+"': "+jr(t.element)),n.fold(function(){return it.error(e)},it.value)}).map(Vl.getValue)})},setValue:function(e,t){Ot(t,function(n,t){Tf(e,o,t).each(function(t){Kf.getCurrent(t).each(function(t){Vl.setValue(t,n)})})})}}})]),apis:{getField:function(t,n){return Tf(t,o,n).bind(Kf.getCurrent)}}}},ES={getField:ai(function(t,n,e){return t.getField(n,e)}),sketch:function(t){var e,n=(e=[],{field:function(t,n){return e.push(t),Sf("form",_S(t),n)},record:function(){return e}}),o=t(n),r=n.record(),i=V(r,function(t){return mf({name:t,pname:_S(t)})});return Pf("form",OS,i,TS,o)}},BS=Xr("valid-input"),DS=Xr("invalid-input"),AS=Xr("validating-input"),MS="colorcustom.rgb.",FS=function(d,m,g,p){var h=function(t,n,e,o,r){var i,u,a=d(MS+"range"),c=[Ey.parts.label({dom:{tag:"label",innerHtml:e,attributes:{"aria-label":o}}}),Ey.parts.field({data:r,factory:Ky,inputAttributes:nt({type:"text"},"hex"===n?{"aria-live":"polite"}:{}),inputClasses:[m("textfield")],inputBehaviours:nc([(i=n,u=t,hx.config({invalidClass:m("invalid"),notify:{onValidate:function(t){Go(t,AS,{type:i})},onValid:function(t){Go(t,BS,{type:i,value:Vl.getValue(t)})},onInvalid:function(t){Go(t,DS,{type:i,value:Vl.getValue(t)})}},validator:{validate:function(t){var n=Vl.getValue(t),e=u(n)?it.value(!0):it.error(d("aria.input.invalid"));return ux(e)},validateOnLoad:!1}})),Ay.config({})]),onSetValue:function(t){hx.isInvalid(t)&&hx.run(t).get($)}})],s="hex"!==n?[Ey.parts["aria-descriptor"]({text:a})]:[];return{dom:{tag:"div",attributes:{role:"presentation"}},components:c.concat(s)}},v=function(t,n){var e=n.red,o=n.green,r=n.blue;Vl.setValue(t,{red:e,green:o,blue:r})},b=sp({dom:{tag:"div",classes:[m("rgba-preview")],styles:{"background-color":"white"},attributes:{role:"presentation"}}}),y=function(t,n){b.getOpt(t).each(function(t){Wi(t.element,"background-color","#"+n.value)})};return Uf({factory:function(){var e={red:se(st.some(255)),green:se(st.some(255)),blue:se(st.some(255)),hex:se(st.some("ffffff"))},o=function(t){return e[t].get()},i=function(t,n){e[t].set(n)},r=function(t){var n=t.red,e=t.green,o=t.blue;i("red",st.some(n)),i("green",st.some(e)),i("blue",st.some(o))},n=function(t,n){var e=n.event;"hex"!==e.type?i(e.type,st.none()):p(t)},u=function(r,t,n){var e=parseInt(n,10);i(t,st.some(e)),o("red").bind(function(e){return o("green").bind(function(n){return o("blue").map(function(t){return cb(e,n,t,1)})})}).each(function(t){var n,e,o=(n=r,e=eb(t),ES.getField(n,"hex").each(function(t){dg.isFocused(t)||Vl.setValue(n,{hex:e.value})}),e);y(r,o)})},a=function(t,n){var e=n.event;"hex"===e.type?function(t,n){g(t);var e=Jv(n);i("hex",st.some(n));var o=fb(e);v(t,o),r(o),Go(t,Lx,{hex:e}),y(t,e)}(t,e.value):u(t,e.type,e.value)},t=function(t){return{label:d(MS+t+".label"),description:d(MS+t+".description")}},c=t("red"),s=t("green"),l=t("blue"),f=t("hex");return Ht(ES.sketch(function(t){return{dom:{tag:"form",classes:[m("rgb-form")],attributes:{"aria-label":d("aria.color.picker")}},components:[t.field("red",Ey.sketch(h(sb,"red",c.label,c.description,255))),t.field("green",Ey.sketch(h(sb,"green",s.label,s.description,255))),t.field("blue",Ey.sketch(h(sb,"blue",l.label,l.description,255))),t.field("hex",Ey.sketch(h(Zv,"hex",f.label,f.description,"ffffff"))),b.asSpec()],formBehaviours:nc([hx.config({invalidClass:m("form-invalid")}),ag("rgb-form-events",[Zo(BS,a),Zo(DS,n),Zo(AS,n)])])}}),{apis:{updateHex:function(t,n){var e,o;Vl.setValue(t,{hex:n.value}),e=t,o=fb(n),v(e,o),r(o),y(t,n)}}})},name:"RgbForm",configFields:[],apis:{updateHex:function(t,n,e){t.updateHex(n,e)}},extraApis:{}})},IS=function(t,o){var r=CS.parts.spectrum({dom:{tag:"canvas",attributes:{role:"presentation"},classes:[o("sv-palette-spectrum")]}}),i=CS.parts.thumb({dom:{tag:"div",attributes:{role:"presentation"},classes:[o("sv-palette-thumb")],innerHtml:"<div class="+o("sv-palette-inner-thumb")+' role="presentation"></div>'}}),u=function(t,n){var e,o,r=t.width,i=t.height,u=t.getContext("2d");null!==u&&(u.fillStyle=n,u.fillRect(0,0,r,i),(e=u.createLinearGradient(0,0,r,0)).addColorStop(0,"rgba(255,255,255,1)"),e.addColorStop(1,"rgba(255,255,255,0)"),u.fillStyle=e,u.fillRect(0,0,r,i),(o=u.createLinearGradient(0,0,0,i)).addColorStop(0,"rgba(0,0,0,0)"),o.addColorStop(1,"rgba(0,0,0,1)"),u.fillStyle=o,u.fillRect(0,0,r,i))};return Uf({factory:function(t){var n=at({x:0,y:0}),e=nc([Kf.config({find:st.some}),dg.config({})]);return CS.sketch({dom:{tag:"div",attributes:{role:"presentation"},classes:[o("sv-palette")]},model:{mode:"xy",getInitialValue:n},rounded:!1,components:[r,i],onChange:function(t,n,e){Go(t,Ux,{value:e})},onInit:function(t,n,e,o){u(e.element.dom,mb(gb))},sliderBehaviours:e})},name:"SaturationBrightnessPalette",configFields:[],apis:{setRgba:function(t,n,e){var o,r;o=e,r=n.components()[0].element.dom,u(r,mb(o))}},extraApis:{}})},RS=function(f,d){return Uf({name:"ColourPicker",configFields:[Nn("dom"),te("onValidHex",$),te("onInvalidHex",$)],factory:function(t){var a,v,n,e,o,r=FS(f,d,t.onValidHex,t.onInvalidHex),i=IS(0,d),b={paletteRgba:se(gb)},u=sp(i.sketch({})),c=sp(r.sketch({})),s=function(t,e){u.getOpt(t).each(function(t){var n=fb(e);b.paletteRgba.set(n),i.setRgba(t,n)})},l=function(t,n){c.getOpt(t).each(function(t){r.updateHex(t,n)})},y=function(n,e,t){ot(t,function(t){t(n,e)})};return{uid:t.uid,dom:t.dom,components:[u.asSpec(),(n=d,e=CS.parts.spectrum({dom:{tag:"div",classes:[n("hue-slider-spectrum")],attributes:{role:"presentation"}}}),o=CS.parts.thumb({dom:{tag:"div",classes:[n("hue-slider-thumb")],attributes:{role:"presentation"}}}),CS.sketch({dom:{tag:"div",classes:[n("hue-slider")],attributes:{role:"presentation"}},rounded:!1,model:{mode:"y",getInitialValue:at({y:0})},components:[e,o],sliderBehaviours:nc([dg.config({})]),onChange:function(t,n,e){Go(t,jx,{value:e})}})),c.asSpec()],behaviours:nc([ag("colour-picker-events",[Zo(Ux,(v=[l],function(t,n){var e,o,r,i,u,a,c,s,l,f=n.event.value,d=b.paletteRgba.get(),m=(i=r=0,u=(e=d).red/255,a=e.green/255,c=e.blue/255,s=Math.min(u,Math.min(a,c)),l=Math.max(u,Math.max(a,c)),s===l?Nx(0,0,100*(i=s)):(r=60*((r=u===s?3:c===s?1:5)-(u===s?a-c:c===s?u-a:c-u)/(l-s)),o=(l-s)/l,i=l,Nx(Math.round(r),Math.round(100*o),Math.round(100*i)))),g=Nx(m.hue,f.x,100-f.y),p=lb(g),h=eb(p);y(t,h,v)})),Zo(jx,(a=[s,l],function(t,n){var e,o,r,i=n.event.value,u=(e=i.y,o=Nx((100-e)/100*360,100,100),r=lb(o),eb(r));y(t,u,a)}))]),Kf.config({find:function(t){return c.getOpt(t)}}),ng.config({mode:"acyclic"})])}}})},VS=function(){return Kf.config({find:st.some})},PS=function(t){return Kf.config({find:t.getOpt})},HS=function(t){return Kf.config({find:function(n){return Cr(n.element,t).bind(function(t){return n.getSystem().getByDom(t).toOptional()})}})},zS={"colorcustom.rgb.red.label":"R","colorcustom.rgb.red.description":"Red component","colorcustom.rgb.green.label":"G","colorcustom.rgb.green.description":"Green component","colorcustom.rgb.blue.label":"B","colorcustom.rgb.blue.description":"Blue component","colorcustom.rgb.hex.label":"#","colorcustom.rgb.hex.description":"Hex color code","colorcustom.rgb.range":"Range 0 to 255","colorcustom.sb.saturation":"Saturation","colorcustom.sb.brightness":"Brightness","colorcustom.sb.picker":"Saturation and Brightness Picker","colorcustom.sb.palette":"Saturation and Brightness Palette","colorcustom.sb.instructions":"Use arrow keys to select saturation and brightness, on x and y axes","colorcustom.hue.hue":"Hue","colorcustom.hue.slider":"Hue Slider","colorcustom.hue.palette":"Hue Palette","colorcustom.hue.instructions":"Use arrow keys to select a hue","aria.color.picker":"Color Picker","aria.input.invalid":"Invalid input"},NS=function(t){return zS[t]},LS=tinymce.util.Tools.resolve("tinymce.Resource"),jS=ln([te("preprocess",ct),te("postprocess",ct)]),US=function(t,n,e){return Vl.config(Ht({store:{mode:"manual",getValue:n,setValue:e}},t.map(function(t){return{store:{initialValue:t}}}).getOr({})))},WS=function(t,n,e){return US(t,function(t){return n(t.element)},function(t,n){return e(t.element,n)})},GS=function(r,t){var i=Tn("RepresentingConfigs.memento processors",jS,t);return Vl.config({store:{mode:"manual",getValue:function(t){var n=r.get(t),e=Vl.getValue(n);return i.postprocess(e)},setValue:function(t,n){var e=i.preprocess(n),o=r.get(t);Vl.setValue(o,e)}}})},XS=US,YS=function(t){return WS(t,Fr,Ir)},qS=function(t){return Vl.config({store:{mode:"memory",initialValue:t}})},KS=function(r,n){var e=function(t,n){n.stop()},o=function(t){return function(n,e){ot(t,function(t){t(n,e)})}},i=function(t,n){var e;tv.isDisabled(t)||(e=n.event.raw,a(t,e.dataTransfer.files))},u=function(t,n){var e=n.event.raw.target;a(t,e.files)},a=function(t,n){var e,o;Vl.setValue(t,(e=n,o=new RegExp("("+".jpg,.jpeg,.png,.gif".split(/\s*,\s*/).join("|")+")$","i"),H(J(e),function(t){return o.test(t.name)}))),Go(t,Vy,{name:r.name})},c=sp({dom:{tag:"input",attributes:{type:"file",accept:"image/*"},styles:{display:"none"}},behaviours:nc([ag("input-file-events",[rr(go()),rr(Oo())])])}),t=r.label.map(function(t){return Ry(t,n)}),s=Ey.parts.field({factory:{sketch:function(t){return{uid:t.uid,dom:{tag:"div",classes:["tox-dropzone-container"]},behaviours:nc([qS([]),VS(),tv.config({}),Cg.config({toggleClass:"dragenter",toggleOnExecute:!1}),ag("dropzone-events",[Zo("dragenter",o([e,Cg.toggle])),Zo("dragleave",o([e,Cg.toggle])),Zo("dragover",e),Zo("drop",o([e,i])),Zo(mo(),u)])]),components:[{dom:{tag:"div",classes:["tox-dropzone"],styles:{}},components:[{dom:{tag:"p",innerHtml:n.translate("Drop an image here")}},cp.sketch({dom:{tag:"button",innerHtml:n.translate("Browse for an image"),styles:{position:"relative"},classes:["tox-button","tox-button--secondary"]},components:[c.asSpec()],action:function(t){c.get(t).element.dom.click()},buttonBehaviours:nc([Ay.config({}),Ev(n.isReadOnly),_v()])})]}]}}}});return My(t,s,["tox-form__group--stretched"],[])},JS=Xr("alloy-fake-before-tabstop"),$S=Xr("alloy-fake-after-tabstop"),QS=function(t){return{dom:{tag:"div",styles:{width:"1px",height:"1px",outline:"none"},attributes:{tabindex:"0"},classes:t},behaviours:nc([dg.config({ignore:!0}),Ay.config({})])}},ZS=function(t){return{dom:{tag:"div",classes:["tox-navobj"]},components:[QS([JS]),t,QS([$S])],behaviours:nc([HS(1)])}},tk=function(t,n){Go(t,so(),{raw:{which:9,shiftKey:n}})},nk=function(t,n){var e=n.element;Ei(e,JS)?tk(t,!0):Ei(e,$S)&&tk(t,!1)},ek=function(t){return oy(t,["."+JS,"."+$S].join(","),c)},ok=!(ze().browser.isIE()||ze().browser.isEdge()),rk=function(t,n){var o,r,e=ok&&t.sandboxed,i=nt(nt({},t.label.map(function(t){return{title:t}}).getOr({})),e?{sandbox:"allow-scripts allow-same-origin"}:{}),u=(o=e,r=se(""),{getValue:function(t){return r.get()},setValue:function(t,n){var e;o?Vr(t.element,"srcdoc",n):(Vr(t.element,"src","javascript:''"),(e=t.element.dom.contentWindow.document).open(),e.write(n),e.close()),r.set(n)}}),a=t.label.map(function(t){return Ry(t,n)}),c=Ey.parts.field({factory:{sketch:function(t){return ZS({uid:t.uid,dom:{tag:"iframe",attributes:i},behaviours:nc([Ay.config({}),dg.config({}),XS(st.none(),u.getValue,u.setValue)])})}}});return My(a,c,["tox-form__group--stretched"],[])};function ik(t,n){return ck(document.createElement("canvas"),t,n)}function uk(t){var n=ik(t.width,t.height);return ak(n).drawImage(t,0,0),n}function ak(t){return t.getContext("2d")}function ck(t,n,e){return t.width=n,t.height=e,t}function sk(t){return t.naturalWidth||t.width}function lk(t){return t.naturalHeight||t.height}var fk,dk,mk,gk,pk=window.Promise?window.Promise:(fk=function(t){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");if("function"!=typeof t)throw new TypeError("not a function");this._state=null,this._value=null,this._deferreds=[],Sk(t,hk(bk,this),hk(yk,this))},dk=window,mk=fk.immediateFn||"function"==typeof dk.setImmediate&&dk.setImmediate||function(t){setTimeout(t,1)},gk=Array.isArray||function(t){return"[object Array]"===Object.prototype.toString.call(t)},fk.prototype["catch"]=function(t){return this.then(null,t)},fk.prototype.then=function(e,o){var r=this;return new fk(function(t,n){vk.call(r,new wk(e,o,t,n))})},fk.all=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];var c=Array.prototype.slice.call(1===t.length&&gk(t[0])?t[0]:t);return new fk(function(r,i){if(0===c.length)return r([]);for(var u=c.length,t=0;t<c.length;t++)!function a(n,t){try{if(t&&("object"==typeof t||"function"==typeof t)){var e=t.then;if("function"==typeof e)return void e.call(t,function(t){a(n,t)},i)}c[n]=t,0==--u&&r(c)}catch(o){i(o)}}(t,c[t])})},fk.resolve=function(n){return n&&"object"==typeof n&&n.constructor===fk?n:new fk(function(t){t(n)})},fk.reject=function(e){return new fk(function(t,n){n(e)})},fk.race=function(r){return new fk(function(t,n){for(var e=0,o=r;e<o.length;e++)o[e].then(t,n)})},fk);function hk(t,n){return function(){return t.apply(n,arguments)}}function vk(o){var r=this;null!==this._state?mk(function(){var t,n=r._state?o.onFulfilled:o.onRejected;if(null!==n){try{t=n(r._value)}catch(e){return void o.reject(e)}o.resolve(t)}else(r._state?o.resolve:o.reject)(r._value)}):this._deferreds.push(o)}function bk(t){try{if(t===this)throw new TypeError("A promise cannot be resolved with itself.");if(t&&("object"==typeof t||"function"==typeof t)){var n=t.then;if("function"==typeof n)return void Sk(hk(n,t),hk(bk,this),hk(yk,this))}this._state=!0,this._value=t,xk.call(this)}catch(e){yk.call(this,e)}}function yk(t){this._state=!1,this._value=t,xk.call(this)}function xk(){for(var t=0,n=this._deferreds;t<n.length;t++){var e=n[t];vk.call(this,e)}this._deferreds=[]}function wk(t,n,e,o){this.onFulfilled="function"==typeof t?t:null,this.onRejected="function"==typeof n?n:null,this.resolve=e,this.reject=o}function Sk(t,n,e){var o=!1;try{t(function(t){o||(o=!0,n(t))},function(t){o||(o=!0,e(t))})}catch(r){if(o)return;o=!0,e(r)}}function kk(e){return new pk(function(t,n){(function(t){var n=t.split(","),e=/data:([^;]+)/.exec(n[0]);if(!e)return st.none();for(var o=e[1],r=n[1],i=atob(r),u=i.length,a=Math.ceil(u/1024),c=new Array(a),s=0;s<a;++s){for(var l=1024*s,f=Math.min(1024+l,u),d=new Array(f-l),m=l,g=0;m<f;++g,++m)d[g]=i[m].charCodeAt(0);c[s]=new Uint8Array(d)}return st.some(new Blob(c,{type:o}))})(e).fold(function(){n("uri is not base64: "+e)},t)})}function Ck(t,o,r){return o=o||"image/png",_(HTMLCanvasElement.prototype.toBlob)?new pk(function(n,e){t.toBlob(function(t){t?n(t):e()},o,r)}):kk(t.toDataURL(o,r))}function Ok(t){return a=t,new pk(function(t,n){var e=URL.createObjectURL(a),o=new Image,r=function(){o.removeEventListener("load",i),o.removeEventListener("error",u)};function i(){r(),t(o)}function u(){r(),n("Unable to load data of type "+a.type+": "+e)}o.addEventListener("load",i),o.addEventListener("error",u),o.src=e,o.complete&&i()}).then(function(t){var n;n=t,URL.revokeObjectURL(n.src);var e=ik(sk(t),lk(t));return ak(e).drawImage(t,0,0),e});var a}function _k(t,n,e){var o=n.type;function r(o,r){return t.then(function(t){return e=r,n=(n=o)||"image/png",t.toDataURL(n,e);var n,e})}return{getType:at(o),toBlob:function(){return pk.resolve(n)},toDataURL:at(e),toBase64:function(){return e.split(",")[1]},toAdjustedBlob:function(n,e){return t.then(function(t){return Ck(t,n,e)})},toAdjustedDataURL:r,toAdjustedBase64:function(t,n){return r(t,n).then(function(t){return t.split(",")[1]})},toCanvas:function(){return t.then(uk)}}}function Tk(n){return e=n,new pk(function(t){var n=new FileReader;n.onloadend=function(){t(n.result)},n.readAsDataURL(e)}).then(function(t){return _k(Ok(n),n,t)});var e}function Ek(n,t){return Ck(n,t).then(function(t){return _k(pk.resolve(n),t,n.toDataURL())})}var Bk=Tk;function Dk(t,n,e){var o="string"==typeof t?parseFloat(t):t;return e<o?o=e:o<n&&(o=n),o}function Ak(){return[1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1]}var Mk=[0,.01,.02,.04,.05,.06,.07,.08,.1,.11,.12,.14,.15,.16,.17,.18,.2,.21,.22,.24,.25,.27,.28,.3,.32,.34,.36,.38,.4,.42,.44,.46,.48,.5,.53,.56,.59,.62,.65,.68,.71,.74,.77,.8,.83,.86,.89,.92,.95,.98,1,1.06,1.12,1.18,1.24,1.3,1.36,1.42,1.48,1.54,1.6,1.66,1.72,1.78,1.84,1.9,1.96,2,2.12,2.25,2.37,2.5,2.62,2.75,2.87,3,3.2,3.4,3.6,3.8,4,4.3,4.7,4.9,5,5.5,6,6.5,6.8,7,7.3,7.5,7.8,8,8.4,8.7,9,9.4,9.6,9.8,10];function Fk(t,n){for(var e,o=[],r=new Array(25),i=0;i<5;i++){for(var u=0;u<5;u++)o[u]=n[u+5*i];for(u=0;u<5;u++){for(var a=e=0;a<5;a++)e+=t[u+5*a]*o[a];r[u+5*i]=e}}return r}function Ik(u,a){return u.toCanvas().then(function(t){return n=t,e=u.getType(),o=a,r=ak(n),i=function(t,n){for(var e,o,r,i,u=t.data,a=n[0],c=n[1],s=n[2],l=n[3],f=n[4],d=n[5],m=n[6],g=n[7],p=n[8],h=n[9],v=n[10],b=n[11],y=n[12],x=n[13],w=n[14],S=n[15],k=n[16],C=n[17],O=n[18],_=n[19],T=0;T<u.length;T+=4)e=u[T],o=u[T+1],r=u[T+2],i=u[T+3],u[T]=e*a+o*c+r*s+i*l+f,u[T+1]=e*d+o*m+r*g+i*p+h,u[T+2]=e*v+o*b+r*y+i*x+w,u[T+3]=e*S+o*k+r*C+i*O+_;return t}(r.getImageData(0,0,n.width,n.height),o),r.putImageData(i,0,0),Ek(n,e);var n,e,o,r,i})}function Rk(a,c){return a.toCanvas().then(function(t){return n=t,e=a.getType(),o=c,r=ak(n),i=r.getImageData(0,0,n.width,n.height),u=r.getImageData(0,0,n.width,n.height),u=function(t,n,e){function o(t,n,e){return e<t?t=e:t<n&&(t=n),t}for(var r=Math.round(Math.sqrt(e.length)),i=Math.floor(r/2),u=t.data,a=n.data,c=t.width,s=t.height,l=0;l<s;l++)for(var f=0;f<c;f++){for(var d=0,m=0,g=0,p=0;p<r;p++)for(var h=0;h<r;h++){var v=o(f+h-i,0,c-1),b=4*(o(l+p-i,0,s-1)*c+v),y=e[p*r+h];d+=u[b]*y,m+=u[1+b]*y,g+=u[2+b]*y}var x=4*(l*c+f);a[x]=o(d,0,255),a[1+x]=o(m,0,255),a[2+x]=o(g,0,255)}return n}(i,u,o),r.putImageData(u,0,0),Ek(n,e);var n,e,o,r,i,u})}function Vk(e){return function(t,n){return Ik(t,e(Ak(),n))}}var Pk,Hk,zk,Nk,Lk=(Pk=[-1,0,0,0,255,0,-1,0,0,255,0,0,-1,0,255,0,0,0,1,0,0,0,0,0,1],function(t){return Ik(t,Pk)}),jk=Vk(function(t,n){return Fk(t,[1,0,0,0,n=Dk(255*n,-255,255),0,1,0,0,n,0,0,1,0,n,0,0,0,1,0,0,0,0,0,1])}),Uk=Vk(function(t,n){var e;return n=Dk(n,-1,1),Fk(t,[(e=(n*=100)<0?127+n/100*127:127*(e=0===(e=n%1)?Mk[n]:Mk[Math.floor(n)]*(1-e)+Mk[Math.floor(n)+1]*e)+127)/127,0,0,0,.5*(127-e),0,e/127,0,0,.5*(127-e),0,0,e/127,0,.5*(127-e),0,0,0,1,0,0,0,0,0,1])}),Wk=(Hk=[0,-1,0,-1,5,-1,0,-1,0],function(t){return Rk(t,Hk)}),Gk=(zk=function(t,n){return 255*Math.pow(t/255,1-n)},Nk=function(t,n,e){var o=ak(t),r=new Array(256);for(var i=0;i<r.length;i++)r[i]=zk(i,e);var u=function(t,n){for(var e=t.data,o=0;o<e.length;o+=4)e[o]=n[e[o]],e[o+1]=n[e[o+1]],e[o+2]=n[e[o+2]];return t}(o.getImageData(0,0,t.width,t.height),r);return o.putImageData(u,0,0),Ek(t,n)},function(n,e){return n.toCanvas().then(function(t){return Nk(t,n.getType(),e)})});function Xk(t,n,e){var o=sk(t),r=lk(t),i=n/o,u=e/r,a=!1;(i<.5||2<i)&&(i=i<.5?.5:2,a=!0),(u<.5||2<u)&&(u=u<.5?.5:2,a=!0);var c,s,l,f=(c=t,s=i,l=u,new pk(function(t){var n=sk(c),e=lk(c),o=Math.floor(n*s),r=Math.floor(e*l),i=ik(o,r);ak(i).drawImage(c,0,0,n,e,0,0,o,r),t(i)}));return a?f.then(function(t){return Xk(t,n,e)}):f}function Yk(n,e){return n.toCanvas().then(function(t){return function(t,n,e){var o=ik(t.width,t.height),r=ak(o),i=0,u=0;90!==(e=e<0?360+e:e)&&270!==e||ck(o,o.height,o.width);90!==e&&180!==e||(i=o.width);270!==e&&180!==e||(u=o.height);return r.translate(i,u),r.rotate(e*Math.PI/180),r.drawImage(t,0,0),Ek(o,n)}(t,n.getType(),e)})}function qk(n,e){return n.toCanvas().then(function(t){return function(t,n,e){var o=ik(t.width,t.height),r=ak(o);"v"===e?(r.scale(1,-1),r.drawImage(t,0,-o.height)):(r.scale(-1,1),r.drawImage(t,-o.width,0));return Ek(o,n)}(t,n.getType(),e)})}function Kk(u,a,c,s,l){return u.toCanvas().then(function(t){return n=t,e=u.getType(),o=a,r=c,ak(i=ik(s,l)).drawImage(n,-o,-r),Ek(i,e);var n,e,o,r,i})}var Jk,$k=function(t){return Lk(t)},Qk=function(t){return Wk(t)},Zk=function(t,n){return Gk(t,n)},tC=function(t,n,e,o){return Ik(t,(r=Ak(),i=e,u=o,Fk(r,[Dk(n,0,2),0,0,0,0,0,i=Dk(i,0,2),0,0,0,0,0,u=Dk(u,0,2),0,0,0,0,0,1,0,0,0,0,0,1])));var r,i,u},nC=function(t,n){return jk(t,n)},eC=function(t,n){return Uk(t,n)},oC=qk,rC=Kk,iC=function(t,n,e){return r=n,i=e,(o=t).toCanvas().then(function(t){return Xk(t,r,i).then(function(t){return Ek(t,o.getType())})});var o,r,i},uC=Yk,aC=function(t,n){return nt({dom:{tag:"span",innerHtml:t,classes:["tox-icon","tox-tbtn__icon-wrap"]}},n)},cC=function(t,n){return aC(fp(t,n),{})},sC=function(t,n){return aC(fp(t,n),{behaviours:nc([ug.config({})])})},lC=function(t,n,e){return{dom:{tag:"span",innerHtml:e.translate(t),classes:[n+"__select-label"]},behaviours:nc([ug.config({})])}},fC=Xr("toolbar.button.execute"),dC=((Jk={})[ko()]=["disabling","alloy.base.behaviour","toggling","toolbar-button-events"],Jk),mC=Xr("update-menu-text"),gC=Xr("update-menu-icon"),pC=function(n,e,o){var t=se($),r=n.text.map(function(t){return sp(lC(t,e,o.providers))}),i=n.icon.map(function(t){return sp(sC(t,o.providers.icons))}),u=function(t,n){var e=Vl.getValue(t);return dg.focus(e),Go(e,"keydown",{raw:n.event.raw}),Ix.close(e),st.some(!0)},a=n.role.fold(function(){return{}},function(t){return{role:t}}),c=n.tooltip.fold(function(){return{}},function(t){var n=o.providers.translate(t);return{title:n,"aria-label":n}});return sp(Ix.sketch(nt(nt(nt({},n.uid?{uid:n.uid}:{}),a),{dom:{tag:"button",classes:[e,e+"--select"].concat(V(n.classes,function(t){return e+"--"+t})),attributes:nt({},c)},components:Rv([i.map(function(t){return t.asSpec()}),r.map(function(t){return t.asSpec()}),st.some({dom:{tag:"div",classes:[e+"__select-chevron"],innerHtml:fp("chevron-down",o.providers.icons)}})]),matchWidth:!0,useMinWidth:!0,dropdownBehaviours:nc(w(n.dropdownBehaviours,[Ev(function(){return n.disabled||o.providers.isReadOnly()}),_v(),Rx.config({}),ug.config({}),ag("dropdown-events",[Mv(n,t),Fv(n,t)]),ag("menubutton-update-display-text",[Zo(mC,function(n,e){r.bind(function(t){return t.getOpt(n)}).each(function(t){ug.set(t,[au(o.providers.translate(e.event.text))])})}),Zo(gC,function(n,e){i.bind(function(t){return t.getOpt(n)}).each(function(t){ug.set(t,[sC(e.event.icon,o.providers.icons)])})})])])),eventOrder:Ht(dC,{mousedown:["focusing","alloy.base.behaviour","item-type-events","normal-dropdown-events"]}),sandboxBehaviours:nc([ng.config({mode:"special",onLeft:u,onRight:u})]),lazySink:o.getSink,toggleClass:e+"--active",parts:{menu:gh(0,n.columns,n.presets)},fetch:function(t){return ix(g(n.fetch,t))}}))).asSpec()},hC=function(t){return"separator"===t.type},vC={type:"separator"},bC=function(t,e){var n=N(t,function(t,n){return S(n)?""===n?t:"|"===n?0<t.length&&!hC(t[t.length-1])?t.concat([vC]):t:Ft(e,n.toLowerCase())?t.concat([e[n.toLowerCase()]]):t:t.concat([n])},[]);return 0<n.length&&hC(n[n.length-1])&&n.pop(),n},yC=function(t,n){return Ft(t,"getSubmenuItems")?(o=n,r=(e=t).getSubmenuItems(),i=xC(r,o),{item:e,menus:Ht(i.menus,Kt(e.value,i.items)),expansions:Ht(i.expansions,Kt(e.value,e.value))}):{item:t,menus:{},expansions:{}};var e,o,r,i},xC=function(t,r){var n=bC(S(t)?t.split(" "):t,r);return z(n,function(t,n){var e=function(t){if(hC(t))return t;var n=Mt(t,"value").getOrThunk(function(){return Xr("generated-menu-item")});return Ht({value:n},t)}(n),o=yC(e,r);return{menus:Ht(t.menus,o.menus),items:[o.item].concat(t.items),expansions:Ht(t.expansions,o.expansions)}},{menus:{},expansions:{},items:[]})},wC=function(t,e,o,n){var r=Xr("primary-menu"),i=xC(t,o.shared.providers.menuItems());if(0===i.items.length)return st.none();var u=Jb(r,i.items,e,o,n),a=_t(i.menus,function(t,n){return Jb(n,t,e,o,!1)}),c=Ht(a,Kt(r,u));return st.from(qg.tieredData(r,c,i.expansions))},SC=function(e){return{isDisabled:function(){return tv.isDisabled(e)},setDisabled:function(t){return tv.set(e,t)},setActive:function(t){var n=e.element;t?(Oi(n,"tox-tbtn--enabled"),Vr(n,"aria-pressed",!0)):(Ti(n,"tox-tbtn--enabled"),Nr(n,"aria-pressed"))},isActive:function(){return Ei(e.element,"tox-tbtn--enabled")}}},kC=function(e,t,o,n){return pC({text:e.text,icon:e.icon,tooltip:e.tooltip,role:n,fetch:function(t,n){e.fetch(function(t){n(wC(t,nh.CLOSE_ON_EXECUTE,o,!1))})},onSetup:e.onSetup,getApi:SC,columns:1,presets:"normal",classes:[],dropdownBehaviours:[Ay.config({})]},t,o.shared)},CC=function(n,r,i){return function(t){t(V(n,function(t){var n,e,o=t.text.fold(function(){return{}},function(t){return{text:t}});return nt(nt({type:t.type,active:!1},o),{onAction:function(t){var n=!t.isActive();t.setActive(n),e.storage.set(n),i.shared.getSink().each(function(t){r().getOpt(t).each(function(t){sc(t.element),Go(t,zy,{name:e.name,value:e.storage.get()})})})},onSetup:(n=e=t,function(t){t.setActive(n.storage.get())})})}))}},OC=function(t,n,e,o,r,i){void 0===e&&(e=[]);var u=n.fold(function(){return{}},function(t){return{action:t}}),a=nt({buttonBehaviours:nc([Ev(function(){return t.disabled||i.isReadOnly()}),_v(),Ay.config({}),ag("button press",[Qo("click"),Qo("mousedown")])].concat(e)),eventOrder:{click:["button press","alloy.base.behaviour"],mousedown:["button press","alloy.base.behaviour"]}},u),c=Ht(a,{dom:o});return Ht(c,{components:r})},_C=function(t,n,e,o){void 0===o&&(o=[]);var r={tag:"button",classes:["tox-tbtn"],attributes:t.tooltip.map(function(t){return{"aria-label":e.translate(t),title:e.translate(t)}}).getOr({})},i=t.icon.map(function(t){return cC(t,e.icons)}),u=Rv([i]);return OC(t,n,o,r,u,e)},TC=function(t,n,e,o){void 0===o&&(o=[]);var r=_C(t,st.some(n),e,o);return cp.sketch(r)},EC=function(t,n,e,o,r){void 0===o&&(o=[]),void 0===r&&(r=[]);var i=e.translate(t.text),u=t.icon?t.icon.map(function(t){return cC(t,e.icons)}):st.none(),a=u.isSome()?Rv([u]):[],c=u.isSome()?{}:{innerHtml:i},s=w(t.primary||t.borderless?["tox-button"]:["tox-button","tox-button--secondary"],u.isSome()?["tox-button--icon"]:[],t.borderless?["tox-button--naked"]:[],r),l=nt(nt({tag:"button",classes:s},c),{attributes:{title:i}});return OC(t,n,o,l,a,e)},BC=function(t,n,e,o,r){void 0===o&&(o=[]),void 0===r&&(r=[]);var i=EC(t,st.some(n),e,o,r);return cp.sketch(i)},DC=function(n,e){return function(t){"custom"===e?Go(t,zy,{name:n,value:{}}):"submit"===e?Wo(t,Ny):"cancel"===e?Wo(t,Hy):console.error("Unknown button type: ",e)}},AC=function(n,t,e){if("menu"===t){var o=n,r=nt(nt({},n),{onSetup:function(t){return t.setDisabled(n.disabled),$},fetch:CC(o.items,function(){return i},e)}),i=sp(kC(r,"tox-tbtn",e,st.none()));return i.asSpec()}if("custom"===(c=t)||"cancel"===c||"submit"===c){var u=DC(n.name,t),a=nt(nt({},n),{borderless:!1});return BC(a,u,e.shared.providers,[])}var c;console.error("Unknown footer button type: ",t)},MC=function(t,n){var e,o,r=DC(t.name,"custom");return e=st.none(),o=Ey.parts.field(nt({factory:cp},EC(t,st.some(r),n,[qS(""),VS()]))),My(e,o,[],[])},FC=at([te("field1Name","field1"),te("field2Name","field2"),da("onLockedChange"),ca(["lockClass"]),te("locked",!1),Nl("coupledFieldBehaviours",[Kf,Vl])]),IC=function(t,n){return mf({factory:Ey,name:t,overrides:function(o){return{fieldBehaviours:nc([ag("coupled-input-behaviour",[Zo(fo(),function(e){Tf(e,o,n).bind(Kf.getCurrent).each(function(n){Tf(e,o,"lock").each(function(t){Cg.isOn(t)&&o.onLockedChange(e,n,t)})})})])])}}})},RC=at([IC("field1","field2"),IC("field2","field1"),mf({factory:cp,schema:[Nn("dom")],name:"lock",overrides:function(t){return{buttonBehaviours:nc([Cg.config({selected:t.locked,toggleClass:t.markers.lockClass,aria:{mode:"pressed"}})])}}})]),VC=Wf({name:"FormCoupledInputs",configFields:FC(),partFields:RC(),factory:function(o,t,n,e){return{uid:o.uid,dom:o.dom,components:t,behaviours:Ll(o.coupledFieldBehaviours,[Kf.config({find:st.some}),Vl.config({store:{mode:"manual",getValue:function(t){var n=Mf(t,o,["field1","field2"]),e={};return e[o.field1Name]=Vl.getValue(n.field1()),e[o.field2Name]=Vl.getValue(n.field2()),e},setValue:function(t,n){var e=Mf(t,o,["field1","field2"]);It(n,o.field1Name)&&Vl.setValue(e.field1(),n[o.field1Name]),It(n,o.field2Name)&&Vl.setValue(e.field2(),n[o.field2Name])}}})]),apis:{getField1:function(t){return Tf(t,o,"field1")},getField2:function(t){return Tf(t,o,"field2")},getLock:function(t){return Tf(t,o,"lock")}}}},apis:{getField1:function(t,n){return t.getField1(n)},getField2:function(t,n){return t.getField2(n)},getLock:function(t,n){return t.getLock(n)}}}),PC=function(t){var n=/^\s*(\d+(?:\.\d+)?)\s*(|cm|mm|in|px|pt|pc|em|ex|ch|rem|vw|vh|vmin|vmax|%)\s*$/.exec(t);if(null===n)return it.error(t);var e=parseFloat(n[1]),o=n[2];return it.value({value:e,unit:o})},HC=function(t,n){var e={"":96,px:96,pt:72,cm:2.54,pc:12,mm:25.4,"in":1},o=function(t){return Object.prototype.hasOwnProperty.call(e,t)};return t.unit===n?st.some(t.value):o(t.unit)&&o(n)?e[t.unit]===e[n]?st.some(t.value):st.some(t.value/e[t.unit]*e[n]):st.none()},zC=function(t){return st.none()},NC=function(t,n){var e,o,r,i=PC(t).toOptional(),u=PC(n).toOptional();return o=u,r=function(t,o){return HC(t,o.unit).map(function(t){return o.value/t}).map(function(t){return n=t,e=o.unit,function(t){return HC(t,e).map(function(t){return{value:t*n,unit:e}})};var n,e}).getOr(zC)},((e=i).isSome()&&o.isSome()?st.some(r(e.getOrDie(),o.getOrDie())):st.none()).getOr(zC)},LC=function(o,n){var a=zC,r=Xr("ratio-event"),t=VC.parts.lock({dom:{tag:"button",classes:["tox-lock","tox-button","tox-button--naked","tox-button--icon"],attributes:{title:n.translate(o.label.getOr("Constrain proportions"))}},components:[{dom:{tag:"span",classes:["tox-icon","tox-lock-icon__lock"],innerHtml:fp("lock",n.icons)}},{dom:{tag:"span",classes:["tox-icon","tox-lock-icon__unlock"],innerHtml:fp("unlock",n.icons)}}],buttonBehaviours:nc([tv.config({disabled:function(){return o.disabled||n.isReadOnly()}}),_v(),Ay.config({})])}),e=function(t){return{dom:{tag:"div",classes:["tox-form__group"]},components:t}},i=function(e){return Ey.parts.field({factory:Ky,inputClasses:["tox-textfield"],inputBehaviours:nc([tv.config({disabled:function(){return o.disabled||n.isReadOnly()}}),_v(),Ay.config({}),ag("size-input-events",[Zo(ao(),function(t,n){Go(t,r,{isField1:e})}),Zo(mo(),function(t,n){Go(t,Vy,{name:o.name})})])]),selectOnFocus:!1})},u=function(t){return{dom:{tag:"label",classes:["tox-label"],innerHtml:n.translate(t)}}},c=VC.parts.field1(e([Ey.parts.label(u("Width")),i(!0)])),s=VC.parts.field2(e([Ey.parts.label(u("Height")),i(!1)]));return VC.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:[{dom:{tag:"div",classes:["tox-form__controls-h-stack"]},components:[c,s,e([u("&nbsp;"),t])]}],field1Name:"width",field2Name:"height",locked:!0,markers:{lockClass:"tox-locked"},onLockedChange:function(t,i,n){PC(Vl.getValue(t)).each(function(t){a(t).each(function(t){var n,e,o,r;Vl.setValue(i,(o={"":0,px:0,pt:1,mm:1,pc:2,ex:2,em:2,ch:2,rem:2,cm:3,"in":4,"%":4},-1!==(r=(n=t).value.toFixed((e=n.unit)in o?o[e]:1)).indexOf(".")&&(r=r.replace(/\.?0*$/,"")),r+n.unit))})})},coupledFieldBehaviours:nc([tv.config({disabled:function(){return o.disabled||n.isReadOnly()},onDisabled:function(t){VC.getField1(t).bind(Ey.getField).each(tv.disable),VC.getField2(t).bind(Ey.getField).each(tv.disable),VC.getLock(t).each(tv.disable)},onEnabled:function(t){VC.getField1(t).bind(Ey.getField).each(tv.enable),VC.getField2(t).bind(Ey.getField).each(tv.enable),VC.getLock(t).each(tv.enable)}}),_v(),ag("size-input-events2",[Zo(r,function(t,n){var e=n.event.isField1,o=e?VC.getField1(t):VC.getField2(t),r=e?VC.getField2(t):VC.getField1(t),i=o.map(Vl.getValue).getOr(""),u=r.map(Vl.getValue).getOr("");a=NC(i,u)})])])})},jC={undo:at(Xr("undo")),redo:at(Xr("redo")),zoom:at(Xr("zoom")),back:at(Xr("back")),apply:at(Xr("apply")),swap:at(Xr("swap")),transform:at(Xr("transform")),tempTransform:at(Xr("temp-transform")),transformApply:at(Xr("transform-apply"))},UC=at("save-state"),WC=at("disable"),GC=at("enable"),XC={formActionEvent:zy,saveState:UC,disable:WC,enable:GC},YC=function(r,c){var t=function(t,n,e,o){return sp(BC({name:t,text:t,disabled:e,primary:o,icon:st.none(),borderless:!1},n,c))},n=function(t,n,e,o){return sp(TC({name:t,icon:st.some(t),tooltip:st.some(n),disabled:o,primary:!1,borderless:!1},e,c))},u=function(t,e){t.map(function(t){var n=t.get(e);n.hasConfigured(tv)&&tv.disable(n)})},a=function(t,e){t.map(function(t){var n=t.get(e);n.hasConfigured(tv)&&tv.enable(n)})},s={tag:"div",classes:["tox-image-tools__toolbar","tox-image-tools-edit-panel"]},e=$,i=function(t,n,e){Go(t,n,e)},l=function(t){return Wo(t,XC.disable())},f=function(t){return Wo(t,XC.enable())},d=function(t,n){l(t),i(t,jC.transform(),{transform:n}),f(t)},o=function(t){return function(){J.getOpt(t).each(function(t){ug.set(t,[q])})}},m=function(t,n){l(t),i(t,jC.transformApply(),{transform:n,swap:o(t)}),f(t)},g=function(){return t("Back",function(t){return i(t,jC.back(),{swap:o(t)})},!1,!1)},p=function(){return sp({dom:{tag:"div",classes:["tox-spacer"]},behaviours:nc([tv.config({})])})},h=function(){return t("Apply",function(t){return i(t,jC.apply(),{swap:o(t)})},!0,!0)},v=[g(),p(),t("Apply",function(t){m(t,function(t){var n=r.getRect();return rC(t,n.x,n.y,n.w,n.h)}),r.hideCrop()},!1,!0)],b=Cy.sketch({dom:s,components:v.map(function(t){return t.asSpec()}),containerBehaviours:nc([ag("image-tools-crop-buttons-events",[Zo(XC.disable(),function(t,n){u(v,t)}),Zo(XC.enable(),function(t,n){a(v,t)})])])}),y=sp(LC({name:"size",label:st.none(),constrain:!0,disabled:!1},c)),x=[g(),p(),y,p(),t("Apply",function(a){y.getOpt(a).each(function(t){var n,e,o=Vl.getValue(t),r=parseInt(o.width,10),i=parseInt(o.height,10),u=(n=r,e=i,function(t){return iC(t,n,e)});m(a,u)})},!1,!0)],w=Cy.sketch({dom:s,components:x.map(function(t){return t.asSpec()}),containerBehaviours:nc([ag("image-tools-resize-buttons-events",[Zo(XC.disable(),function(t,n){u(x,t)}),Zo(XC.enable(),function(t,n){a(x,t)})])])}),S=function(n,e){return function(t){return n(t,e)}},k=S(oC,"h"),C=S(oC,"v"),O=S(uC,-90),_=S(uC,90),T=function(t,n){var e,o;o=n,l(e=t),i(e,jC.tempTransform(),{transform:o}),f(e)},E=[g(),p(),n("flip-horizontally","Flip horizontally",function(t){T(t,k)},!1),n("flip-vertically","Flip vertically",function(t){T(t,C)},!1),n("rotate-left","Rotate counterclockwise",function(t){T(t,O)},!1),n("rotate-right","Rotate clockwise",function(t){T(t,_)},!1),p(),h()],B=Cy.sketch({dom:s,components:E.map(function(t){return t.asSpec()}),containerBehaviours:nc([ag("image-tools-fliprotate-buttons-events",[Zo(XC.disable(),function(t,n){u(E,t)}),Zo(XC.enable(),function(t,n){a(E,t)})])])}),D=function(t,n,e,o,r){var i=CS.parts.label({dom:{tag:"label",classes:["tox-label"],innerHtml:c.translate(t)}}),u=CS.parts.spectrum({dom:{tag:"div",classes:["tox-slider__rail"],attributes:{role:"presentation"}}}),a=CS.parts.thumb({dom:{tag:"div",classes:["tox-slider__handle"],attributes:{role:"presentation"}}});return sp(CS.sketch({dom:{tag:"div",classes:["tox-slider"],attributes:{role:"presentation"}},model:{mode:"x",minX:e,maxX:r,getInitialValue:at({x:o})},components:[i,u,a],sliderBehaviours:nc([dg.config({})]),onChoose:n}))},A=function(t,n,e,o,r){return[g(),(i=n,D(t,function(t,n,e){var o=S(i,e.x/100);d(t,o)},e,o,r)),h()];var i},M=function(t,n,e,o,r){var i=A(t,n,e,o,r);return Cy.sketch({dom:s,components:i.map(function(t){return t.asSpec()}),containerBehaviours:nc([ag("image-tools-filter-panel-buttons-events",[Zo(XC.disable(),function(t,n){u(i,t)}),Zo(XC.enable(),function(t,n){a(i,t)})])])})},F=[g(),p(),h()],I=Cy.sketch({dom:s,components:F.map(function(t){return t.asSpec()})}),R=M("Brightness",nC,-100,0,100),V=M("Contrast",eC,-100,0,100),P=M("Gamma",Zk,-100,0,100),H=function(t){return D(t,function(l,t,n){var e=z.getOpt(l),o=L.getOpt(l),r=N.getOpt(l);e.each(function(s){o.each(function(c){r.each(function(t){var n,e,o,r=Vl.getValue(s).x/100,i=Vl.getValue(t).x/100,u=Vl.getValue(c).x/100,a=(n=r,e=i,o=u,function(t){return tC(t,n,e,o)});d(l,a)})})})},0,100,200)},z=H("R"),N=H("G"),L=H("B"),j=[g(),z,N,L,h()],U=Cy.sketch({dom:s,components:j.map(function(t){return t.asSpec()})}),W=function(n,e,o){return function(t){i(t,jC.swap(),{transform:e,swap:function(){J.getOpt(t).each(function(t){ug.set(t,[n]),o(t)})}})}},G=st.some(Qk),X=st.some($k),Y=[n("crop","Crop",W(b,st.none(),function(t){r.showCrop()}),!1),n("resize","Resize",W(w,st.none(),function(t){y.getOpt(t).each(function(t){var n=r.getMeasurements(),e=n.width,o=n.height;Vl.setValue(t,{width:e,height:o})})}),!1),n("orientation","Orientation",W(B,st.none(),e),!1),n("brightness","Brightness",W(R,st.none(),e),!1),n("sharpen","Sharpen",W(I,G,e),!1),n("contrast","Contrast",W(V,st.none(),e),!1),n("color-levels","Color levels",W(U,st.none(),e),!1),n("gamma","Gamma",W(P,st.none(),e),!1),n("invert","Invert",W(I,X,e),!1)],q=Cy.sketch({dom:s,components:Y.map(function(t){return t.asSpec()})}),K=Cy.sketch({dom:{tag:"div"},components:[q],containerBehaviours:nc([ug.config({})])}),J=sp(K);return{memContainer:J,getApplyButton:function(t){return J.getOpt(t).map(function(t){var n=t.components()[0];return n.components()[n.components().length-1]})}}},qC=tinymce.util.Tools.resolve("tinymce.geom.Rect"),KC=tinymce.util.Tools.resolve("tinymce.dom.DomQuery"),JC=tinymce.util.Tools.resolve("tinymce.util.Observable"),$C=tinymce.util.Tools.resolve("tinymce.util.Tools"),QC=tinymce.util.Tools.resolve("tinymce.util.VK");function ZC(t){var n,e;if(t.changedTouches)for(n="screenX screenY pageX pageY clientX clientY".split(" "),e=0;e<n.length;e++)t[n[e]]=t.changedTouches[0][n[e]]}function tO(t,m){var g,p,h,v,b=m.document||document,y=b.getElementById(m.handle||t),x=function(t){if(ZC(t),t.button!==p)return w(t);t.deltaX=t.screenX-h,t.deltaY=t.screenY-v,t.preventDefault(),m.drag(t)},w=function(t){ZC(t),KC(b).off("mousemove touchmove",x).off("mouseup touchend",w),g.remove(),m.stop&&m.stop(t)};this.destroy=function(){KC(y).off()},KC(y).on("mousedown touchstart",function(t){var n,e,o,r,i,u,a,c,s,l,f=(n=b,e=Math.max,o=n.documentElement,r=n.body,i=e(o.scrollWidth,r.scrollWidth),u=e(o.clientWidth,r.clientWidth),a=e(o.offsetWidth,r.offsetWidth),c=e(o.scrollHeight,r.scrollHeight),s=e(o.clientHeight,r.clientHeight),{width:i<a?u:i,height:c<e(o.offsetHeight,r.offsetHeight)?s:c});ZC(t),t.preventDefault(),p=t.button;var d=y;h=t.screenX,v=t.screenY,l=window.getComputedStyle?window.getComputedStyle(d,null).getPropertyValue("cursor"):d.runtimeStyle.cursor,g=KC("<div></div>").css({position:"absolute",top:0,left:0,width:f.width,height:f.height,zIndex:2147483647,opacity:1e-4,cursor:l}).appendTo(b.body),KC(b).on("mousemove touchmove",x).on("mouseup touchend",w),m.start(t)})}var nO=0,eO=function(s,e,l,o,r){var t,n="tox-",u="tox-crid-"+nO++,a=[{name:"move",xMul:0,yMul:0,deltaX:1,deltaY:1,deltaW:0,deltaH:0,label:"Crop Mask"},{name:"nw",xMul:0,yMul:0,deltaX:1,deltaY:1,deltaW:-1,deltaH:-1,label:"Top Left Crop Handle"},{name:"ne",xMul:1,yMul:0,deltaX:0,deltaY:1,deltaW:1,deltaH:-1,label:"Top Right Crop Handle"},{name:"sw",xMul:0,yMul:1,deltaX:1,deltaY:0,deltaW:-1,deltaH:1,label:"Bottom Left Crop Handle"},{name:"se",xMul:1,yMul:1,deltaX:0,deltaY:0,deltaW:1,deltaH:1,label:"Bottom Right Crop Handle"}],i=["top","right","bottom","left"],c=function(t,n){return{x:n.x+t.x,y:n.y+t.y,w:n.w,h:n.h}},f=function(t,n){return{x:n.x-t.x,y:n.y-t.y,w:n.w,h:n.h}};function d(t,n,e,o){var r,i=n.x,u=n.y,a=n.w,c=n.h;i+=e*t.deltaX,u+=o*t.deltaY,(a+=e*t.deltaW)<20&&(a=20),(c+=o*t.deltaH)<20&&(c=20),r=s=qC.clamp({x:i,y:u,w:a,h:c},l,"move"===t.name),r=f(l,r),h.fire("updateRect",{rect:r}),p(r)}function m(n){function t(t,n){n.h<0&&(n.h=0),n.w<0&&(n.w=0),KC("#"+u+"-"+t,o).css({left:n.x,top:n.y,width:n.w,height:n.h})}$C.each(a,function(t){KC("#"+u+"-"+t.name,o).css({left:n.w*t.xMul+n.x,top:n.h*t.yMul+n.y})}),t("top",{x:e.x,y:e.y,w:e.w,h:n.y-e.y}),t("right",{x:n.x+n.w,y:n.y,w:e.w-n.x-n.w+e.x,h:n.h}),t("bottom",{x:e.x,y:n.y+n.h,w:e.w,h:e.h-n.y-n.h+e.y}),t("left",{x:e.x,y:n.y,w:n.x-e.x,h:n.h}),t("move",n)}function g(t){m(s=t)}function p(t){g(c(l,t))}KC('<div id="'+u+'" class="'+n+'croprect-container" role="grid" aria-dropeffect="execute">').appendTo(o),$C.each(i,function(t){KC("#"+u,o).append('<div id="'+u+"-"+t+'"class="'+n+'croprect-block" style="display: none" data-mce-bogus="all">')}),$C.each(a,function(t){KC("#"+u,o).append('<div id="'+u+"-"+t.name+'" class="'+n+"croprect-handle "+n+"croprect-handle-"+t.name+'"style="display: none" data-mce-bogus="all" role="gridcell" tabindex="-1" aria-label="'+t.label+'" aria-grabbed="false" title="'+t.label+'">')}),t=$C.map(a,function(n){var e;return new tO(u,{document:o.ownerDocument,handle:u+"-"+n.name,start:function(){e=s},drag:function(t){d(n,e,t.deltaX,t.deltaY)}})}),m(s),KC(o).on("focusin focusout",function(t){KC(t.target).attr("aria-grabbed","focus"===t.type?"true":"false")}),KC(o).on("keydown",function(n){var i;function t(t,n,e,o,r){t.stopPropagation(),t.preventDefault(),d(i,e,o,r)}switch($C.each(a,function(t){if(n.target.id===u+"-"+t.name)return i=t,!1}),n.keyCode){case QC.LEFT:t(n,0,s,-10,0);break;case QC.RIGHT:t(n,0,s,10,0);break;case QC.UP:t(n,0,s,0,-10);break;case QC.DOWN:t(n,0,s,0,10);break;case QC.ENTER:case QC.SPACEBAR:n.preventDefault(),r()}});var h=$C.extend({toggleVisibility:function(t){var n=$C.map(a,function(t){return"#"+u+"-"+t.name}).concat($C.map(i,function(t){return"#"+u+"-"+t})).join(",");t?KC(n,o).show():KC(n,o).hide()},setClampRect:function(t){l=t,m(s)},setRect:g,getInnerRect:function(){return f(l,s)},setInnerRect:p,setViewPortRect:function(t){e=t,m(s)},destroy:function(){$C.each(t,function(t){t.destroy()}),t=[]}},JC);return h},oO=function(n){var l=sp({dom:{tag:"div",classes:["tox-image-tools__image-bg"],attributes:{role:"presentation"}}}),f=se(1),d=se(st.none()),m=se({x:0,y:0,w:1,h:1}),c=se({x:0,y:0,w:1,h:1}),s=function(t,s){g.getOpt(t).each(function(t){var e=f.get(),o=Su(t.element),r=gu(t.element),i=s.dom.naturalWidth*e,u=s.dom.naturalHeight*e,a=Math.max(0,o/2-i/2),c=Math.max(0,r/2-u/2),n={left:a.toString()+"px",top:c.toString()+"px",width:i.toString()+"px",height:u.toString()+"px",position:"absolute"};Gi(s,n),l.getOpt(t).each(function(t){Gi(t.element,n)}),d.get().each(function(t){var n=m.get();t.setRect({x:n.x*e+a,y:n.y*e+c,w:n.w*e,h:n.h*e}),t.setClampRect({x:a,y:c,w:i,h:u}),t.setViewPortRect({x:0,y:0,w:o,h:r})})})},e=function(t,n){var e,a=fe.fromTag("img");return Vr(a,"src",n),e=a.dom,new Ep(function(t){var n=function(){e.removeEventListener("load",n),t(e)};e.complete?t(e):e.addEventListener("load",n)}).then(function(){return g.getOpt(t).map(function(t){var n=cu({element:a});ug.replaceAt(t,1,st.some(n));var e=c.get(),o={x:0,y:0,w:a.dom.naturalWidth,h:a.dom.naturalHeight};c.set(o);var r,u,i=qC.inflate(o,-20,-20);return m.set(i),e.w===o.w&&e.h===o.h||(r=t,u=a,g.getOpt(r).each(function(t){var n=Su(t.element),e=gu(t.element),o=u.dom.naturalWidth,r=u.dom.naturalHeight,i=Math.min(n/o,e/r);1<=i?f.set(1):f.set(i)})),s(t,a),a})})},t=Cy.sketch({dom:{tag:"div",classes:["tox-image-tools__image"]},components:[l.asSpec(),{dom:{tag:"img",attributes:{src:n}}},{dom:{tag:"div"},behaviours:nc([ag("image-panel-crop-events",[ur(function(t){g.getOpt(t).each(function(t){var n=t.element.dom,e=eO({x:10,y:10,w:100,h:100},{x:0,y:0,w:200,h:200},{x:0,y:0,w:200,h:200},n,function(){});e.toggleVisibility(!1),e.on("updateRect",function(t){var n=t.rect,e=f.get(),o={x:Math.round(n.x/e),y:Math.round(n.y/e),w:Math.round(n.w/e),h:Math.round(n.h/e)};m.set(o)}),d.set(st.some(e))})})])])}],containerBehaviours:nc([ug.config({}),ag("image-panel-events",[ur(function(t){e(t,n)})])])}),g=sp(t);return{memContainer:g,updateSrc:e,zoom:function(t,n){var e=f.get(),o=0<n?Math.min(2,e+.1):Math.max(.1,e-.1);f.set(o),g.getOpt(t).each(function(t){var n=t.components()[1].element;s(t,n)})},showCrop:function(){d.get().each(function(t){t.toggleVisibility(!0)})},hideCrop:function(){d.get().each(function(t){t.toggleVisibility(!1)})},getRect:function(){return m.get()},getMeasurements:function(){var t=c.get();return{width:t.w,height:t.h}}}},rO=function(t,n,e,o,r){return TC({name:t,icon:st.some(n),disabled:e,tooltip:st.some(t),primary:!1,borderless:!1},o,r)},iO=function(t,n){n?tv.enable(t):tv.disable(t)};function uO(){var e=[],o=-1;function t(){return 0<o}function n(){return-1!==o&&o<e.length-1}return{data:e,add:function(t){var n=e.splice(++o);return e.push(t),{state:t,removed:n}},undo:function(){if(t())return e[--o]},redo:function(){if(n())return e[++o]},canUndo:t,canRedo:n}}var aO,cO,sO,lO=function(t){var n=se(t),e=se(st.none()),r=uO();r.add(t);var i=function(t){n.set(t)},u=function(t){return{blob:t,url:URL.createObjectURL(t)}},a=function(t){URL.revokeObjectURL(t.url)},o=function(){e.get().each(a),e.set(st.none())},c=function(t){var n=u(t);i(n);var e,o=r.add(n).removed;return e=o,$C.each(e,a),n.url};return{getBlobState:function(){return n.get()},setBlobState:i,addBlobState:c,getTempState:function(){return e.get().fold(function(){return n.get()},function(t){return t})},updateTempState:function(t){var n=u(t);return o(),e.set(st.some(n)),n.url},addTempState:function(t){var n=u(t);return e.set(st.some(n)),n.url},applyTempState:function(n){return e.get().fold(function(){},function(t){c(t.blob),n()})},destroyTempState:o,undo:function(){var t=r.undo();return i(t),t.url},redo:function(){var t=r.redo();return i(t),t.url},getHistoryStates:function(){return{undoEnabled:r.canUndo(),redoEnabled:r.canRedo()}}}},fO=function(t,n){var e,o,r,u=lO(t.currentState),i=function(t){var n=u.getHistoryStates();p.updateButtonUndoStates(t,n.undoEnabled,n.redoEnabled),Go(t,XC.formActionEvent,{name:XC.saveState(),value:n.undoEnabled})},a=function(t){return t.toBlob()},c=function(t){Go(t,XC.formActionEvent,{name:XC.disable(),value:{}})},s=function(t){h.getApplyButton(t).each(function(t){tv.enable(t)}),Go(t,XC.formActionEvent,{name:XC.enable(),value:{}})},l=function(t,n){return c(t),g.updateSrc(t,n)},f=function(n,t,e,o,r){return c(n),Bk(t).then(e).then(a).then(o).then(function(t){return l(n,t).then(function(t){return i(n),r(),s(n),t})})["catch"](function(t){return console.log(t),s(n),t})},d=function(t,n,e){var o=u.getBlobState().blob;f(t,o,n,function(t){return u.updateTempState(t)},e)},m=function(t){var n=u.getBlobState().url;return u.destroyTempState(),i(t),n},g=oO(t.currentState.url),p=(o=sp(rO("Undo","undo",!0,function(t){Go(t,jC.undo(),{direction:1})},e=n)),r=sp(rO("Redo","redo",!0,function(t){Go(t,jC.redo(),{direction:1})},e)),{container:Cy.sketch({dom:{tag:"div",classes:["tox-image-tools__toolbar","tox-image-tools__sidebar"]},components:[o.asSpec(),r.asSpec(),rO("Zoom in","zoom-in",!1,function(t){Go(t,jC.zoom(),{direction:1})},e),rO("Zoom out","zoom-out",!1,function(t){Go(t,jC.zoom(),{direction:-1})},e)]}),updateButtonUndoStates:function(t,n,e){o.getOpt(t).each(function(t){iO(t,n)}),r.getOpt(t).each(function(t){iO(t,e)})}}),h=YC(g,n);return{dom:{tag:"div",attributes:{role:"presentation"}},components:[h.memContainer.asSpec(),g.memContainer.asSpec(),p.container],behaviours:nc([Vl.config({store:{mode:"manual",getValue:function(){return u.getBlobState()}}}),ag("image-tools-events",[Zo(jC.undo(),function(n,t){var e=u.undo();l(n,e).then(function(t){s(n),i(n)})}),Zo(jC.redo(),function(n,t){var e=u.redo();l(n,e).then(function(t){s(n),i(n)})}),Zo(jC.zoom(),function(t,n){var e=n.event.direction;g.zoom(t,e)}),Zo(jC.back(),function(t,n){var e,o;o=m(e=t),l(e,o).then(function(t){s(e)}),(0,n.event.swap)(),g.hideCrop()}),Zo(jC.apply(),function(t,n){u.applyTempState(function(){m(t),(0,n.event.swap)()})}),Zo(jC.transform(),function(t,n){return d(t,n.event.transform,$)}),Zo(jC.tempTransform(),function(t,n){return e=t,o=n.event.transform,r=u.getTempState().blob,void f(e,r,o,function(t){return u.addTempState(t)},$);var e,o,r}),Zo(jC.transformApply(),function(t,n){return e=t,o=n.event.transform,r=n.event.swap,i=u.getBlobState().blob,void f(e,i,o,function(t){var n=u.addBlobState(t);return m(e),n},r);var e,o,r,i}),Zo(jC.swap(),function(n,t){var e;e=n,p.updateButtonUndoStates(e,!1,!1);var o=t.event.transform,r=t.event.swap;o.fold(function(){r()},function(t){d(n,t,r)})})]),VS()])}},dO=function(t){return!Ft(t,"items")},mO="data-value",gO=function(n,e,t,o){return V(t,function(t){return dO(t)?{type:"togglemenuitem",text:t.text,value:t.value,active:t.value===o,onAction:function(){Vl.setValue(n,t.value),Go(n,Vy,{name:e}),dg.focus(n)}}:{type:"nestedmenuitem",text:t.text,getSubmenuItems:function(){return gO(n,e,t.items,o)}}})},pO=function(t,o){return Q(t,function(t){return dO(t)?(n=t.value===o,e=t,n?st.some(e):st.none()):pO(t.items,o);var n,e})},hO=Uf({name:"HtmlSelect",configFields:[Nn("options"),Pl("selectBehaviours",[dg,Vl]),te("selectClasses",[]),te("selectAttributes",{}),qn("data")],factory:function(e,t){var n=V(e.options,function(t){return{dom:{tag:"option",value:t.value,innerHtml:t.text}}}),o=e.data.map(function(t){return Kt("initialValue",t)}).getOr({});return{uid:e.uid,dom:{tag:"select",classes:e.selectClasses,attributes:e.selectAttributes},components:n,behaviours:zl(e.selectBehaviours,[dg.config({}),Vl.config({store:nt({mode:"manual",getValue:function(t){return Zi(t.element)},setValue:function(t,n){L(e.options,function(t){return t.value===n}).isSome()&&tu(t.element,n)}},o)})])}}}),vO=function(e,n){var t=e.label.map(function(t){return Ry(t,n)}),o=[tv.config({disabled:function(){return e.disabled||n.isReadOnly()}}),_v(),ng.config({mode:"execution",useEnter:!0!==e.multiline,useControlEnter:!0===e.multiline,execute:function(t){return Wo(t,Ny),st.some(!0)}}),ag("textfield-change",[Zo(fo(),function(t,n){Go(t,Vy,{name:e.name})}),Zo(wo(),function(t,n){Go(t,Vy,{name:e.name})})]),Ay.config({})],r=e.validation.map(function(o){return hx.config({getRoot:function(t){return xr(t.element)},invalidClass:"tox-invalid",validator:{validate:function(t){var n=Vl.getValue(t),e=o.validator(n);return ux(!0===e?it.value(n):it.error(e))},validateOnLoad:o.validateOnLoad}})}).toArray(),i=e.placeholder.fold(at({}),function(t){return{placeholder:n.translate(t)}}),u=e.inputMode.fold(at({}),function(t){return{inputmode:t}}),a=nt(nt({},i),u),c=Ey.parts.field({tag:!0===e.multiline?"textarea":"input",inputAttributes:a,inputClasses:[e.classname],inputBehaviours:nc(rt([o,r])),selectOnFocus:!1,factory:Ky}),s=(e.flex?["tox-form__group--stretched"]:[]).concat(e.maximized?["tox-form-group--maximize"]:[]),l=[tv.config({disabled:function(){return e.disabled||n.isReadOnly()},onDisabled:function(t){Ey.getField(t).each(tv.disable)},onEnabled:function(t){Ey.getField(t).each(tv.enable)}}),_v()];return My(t,c,s,l)},bO=/* */Object.freeze({__proto__:null,events:function(t,n){var e=t.stream.streams.setup(t,n);return Jo([Zo(t.event,e),ar(function(){return n.cancel()})].concat(t.cancelEvent.map(function(t){return[Zo(t,function(){return n.cancel()})]}).getOr([])))}}),yO=function(t){var n=se(null);return si({readState:function(){return{timer:null!==n.get()?"set":"unset"}},setTimer:function(t){n.set(t)},cancel:function(){var t=n.get();null!==t&&t.cancel()}})},xO=/* */Object.freeze({__proto__:null,throttle:yO,init:function(t){return t.stream.streams.state(t)}}),wO=[Ln("stream",Dn("mode",{throttle:[Nn("delay"),te("stopEvent",!0),ga("streams",{setup:function(t,n){var e=t.stream,o=vp(t.onStream,e.delay);return n.setTimer(o),function(t,n){o.throttle(t,n),e.stopEvent&&n.stop()}},state:yO})]})),te("event","input"),qn("cancelEvent"),da("onStream")],SO=oc({fields:wO,name:"streaming",active:bO,state:xO}),kO=function(t,n,e){var o=Vl.getValue(e);Vl.setValue(n,o),OO(n)},CO=function(t,n){var e=t.element,o=Zi(e),r=e.dom;"number"!==Pr(e,"type")&&n(r,o)},OO=function(t){CO(t,function(t,n){return t.setSelectionRange(n.length,n.length)})},_O=function(t,n,o){if(t.selectsOver){var e=Vl.getValue(n),r=t.getDisplayText(e),i=Vl.getValue(o);return 0===t.getDisplayText(i).indexOf(r)?st.some(function(){var t,e;kO(0,n,o),t=n,e=r.length,CO(t,function(t,n){return t.setSelectionRange(e,n.length)})}):st.none()}return st.none()},TO=at("alloy.typeahead.itemexecute"),EO=at([qn("lazySink"),Nn("fetch"),te("minChars",5),te("responseTime",1e3),la("onOpen"),te("getHotspot",st.some),te("getAnchorOverrides",at({})),te("layouts",st.none()),te("eventOrder",{}),ae("model",{},[te("getDisplayText",function(t){return t.meta!==undefined&&t.meta.text!==undefined?t.meta.text:t.value}),te("selectsOver",!0),te("populateFromBrowse",!0)]),la("onSetValue"),fa("onExecute"),la("onItemExecute"),te("inputClasses",[]),te("inputAttributes",{}),te("inputStyles",{}),te("matchWidth",!0),te("useMinWidth",!1),te("dismissOnBlur",!0),ca(["openClass"]),qn("initialData"),Pl("typeaheadBehaviours",[dg,Vl,SO,ng,Cg,yx]),ce("previewing",function(){return se(!0)})].concat(Xy()).concat(Ax())),BO=at([gf({schema:[aa()],name:"menu",overrides:function(o){return{fakeFocus:!0,onHighlight:function(n,e){o.previewing.get()?n.getSystem().getByUid(o.uid).each(function(t){_O(o.model,t,e).fold(function(){return ud.dehighlight(n,e)},function(t){return t()})}):n.getSystem().getByUid(o.uid).each(function(t){o.model.populateFromBrowse&&kO(o.model,t,e)}),o.previewing.set(!1)},onExecute:function(t,n){return t.getSystem().getByUid(o.uid).toOptional().map(function(t){return Go(t,TO(),{item:n}),!0})},onHover:function(t,n){o.previewing.set(!1),t.getSystem().getByUid(o.uid).each(function(t){o.model.populateFromBrowse&&kO(o.model,t,n)})}}}})]),DO=Wf({name:"Typeahead",configFields:EO(),partFields:BO(),factory:function(r,t,n,i){var e=function(t,n,e){r.previewing.set(!1);var o=yx.getCoupled(t,"sandbox");hl.isOpen(o)?Kf.getCurrent(o).each(function(t){ud.getHighlighted(t).fold(function(){e(t)},function(){Ko(o,t.element,"keydown",n)})}):kx(r,u(t),t,o,i,function(t){Kf.getCurrent(t).each(e)},Qy.HighlightFirst).get($)},o=Yy(r),u=function(o){return function(t){return t.map(function(t){var n=At(t.menus),e=U(n,function(t){return H(t.items,function(t){return"item"===t.type})});return Vl.getState(o).update(V(e,function(t){return t.data})),t})}},a=[dg.config({}),Vl.config({onSetValue:r.onSetValue,store:nt({mode:"dataset",getDataKey:function(t){return Zi(t.element)},getFallbackEntry:function(t){return{value:t,meta:{}}},setValue:function(t,n){tu(t.element,r.model.getDisplayText(n))}},r.initialData.map(function(t){return Kt("initialValue",t)}).getOr({}))}),SO.config({stream:{mode:"throttle",delay:r.responseTime,stopEvent:!1},onStream:function(t,n){var e,o=yx.getCoupled(t,"sandbox");dg.isFocused(t)&&Zi(t.element).length>=r.minChars&&(e=Kf.getCurrent(o).bind(function(t){return ud.getHighlighted(t).map(Vl.getValue)}),r.previewing.set(!0),kx(r,u(t),t,o,i,function(t){Kf.getCurrent(o).each(function(t){e.fold(function(){r.model.selectsOver&&ud.highlightFirst(t)},function(n){ud.highlightBy(t,function(t){return Vl.getValue(t).value===n.value}),ud.getHighlighted(t).orThunk(function(){return ud.highlightFirst(t),st.none()})})})},Qy.HighlightFirst).get($))},cancelEvent:Eo()}),ng.config({mode:"special",onDown:function(t,n){return e(t,n,ud.highlightFirst),st.some(!0)},onEscape:function(t){var n=yx.getCoupled(t,"sandbox");return hl.isOpen(n)?(hl.close(n),st.some(!0)):st.none()},onUp:function(t,n){return e(t,n,ud.highlightLast),st.some(!0)},onEnter:function(n){var t=yx.getCoupled(n,"sandbox"),e=hl.isOpen(t);if(e&&!r.previewing.get())return Kf.getCurrent(t).bind(function(t){return ud.getHighlighted(t)}).map(function(t){return Go(n,TO(),{item:t}),!0});var o=Vl.getValue(n);return Wo(n,Eo()),r.onExecute(t,n,o),e&&hl.close(t),st.some(!0)}}),Cg.config({toggleClass:r.markers.openClass,aria:{mode:"expanded"}}),yx.config({others:{sandbox:function(t){return Bx(r,t,{onOpen:function(){return Cg.on(t)},onClose:function(){return Cg.off(t)}})}}}),ag("typeaheadevents",[sr(function(t){var n=$;Ox(r,u(t),t,i,n,Qy.HighlightFirst).get($)}),Zo(TO(),function(t,n){var e=yx.getCoupled(t,"sandbox");kO(r.model,t,n.event.item),Wo(t,Eo()),r.onItemExecute(t,e,n.event.item,Vl.getValue(t)),hl.close(e),OO(t)})].concat(r.dismissOnBlur?[Zo(xo(),function(t){var n=yx.getCoupled(t,"sandbox");fc(n.element).isNone()&&hl.close(n)})]:[]))];return{uid:r.uid,dom:qy(Ht(r,{inputAttributes:{role:"combobox","aria-autocomplete":"list","aria-haspopup":"true"}})),behaviours:nt(nt({},o),zl(r.typeaheadBehaviours,a)),eventOrder:r.eventOrder}}}),AO=function(i){return nt(nt({},i),{toCached:function(){return AO(i.toCached())},bindFuture:function(n){return AO(i.bind(function(t){return t.fold(function(t){return ux(it.error(t))},function(t){return n(t)})}))},bindResult:function(n){return AO(i.map(function(t){return t.bind(n)}))},mapResult:function(n){return AO(i.map(function(t){return t.map(n)}))},mapError:function(n){return AO(i.map(function(t){return t.mapError(n)}))},foldResult:function(n,e){return i.map(function(t){return t.fold(n,e)})},withTimeout:function(t,r){return AO(ix(function(n){var e=!1,o=setTimeout(function(){e=!0,n(it.error(r()))},t);i.get(function(t){e||(clearTimeout(o),n(t))})}))}})},MO=function(t){return AO(ix(t))},FO=MO,IO={type:"separator"},RO=function(t){return{type:"menuitem",value:t.url,text:t.title,meta:{attach:t.attach},onAction:function(){}}},VO=function(t,n){return{type:"menuitem",value:n,text:t,meta:{attach:undefined},onAction:function(){}}},PO=function(t,n){return o=t,e=H(n,function(t){return t.type===o}),V(e,RO);var e,o},HO=function(t,n){var e=t.toLowerCase();return H(n,function(t){var n=t.meta!==undefined&&t.meta.text!==undefined?t.meta.text:t.text;return ye(n.toLowerCase(),e)||ye(t.value.toLowerCase(),e)})},zO=function(u,t,a){var n=Vl.getValue(t),c=n.meta.text!==undefined?n.meta.text:n.value;return a.getLinkInformation().fold(function(){return[]},function(t){var n,e,o,r,i=HO(c,(n=a.getHistory(u),V(n,function(t){return VO(t,t)})));return"file"===u?(e=[i,HO(c,PO("header",t.targets)),HO(c,rt([(r=t,st.from(r.anchorTop).map(function(t){return VO("<top>",t)}).toArray()),PO("anchor",t.targets),(o=t,st.from(o.anchorBottom).map(function(t){return VO("<bottom>",t)}).toArray())]))],N(e,function(t,n){return 0===t.length||0===n.length?t.concat(n):t.concat(IO,n)},[])):i})},NO=Xr("aria-invalid"),LO=function(r,o,i){var t,n,e,u,a,c=o.shared.providers,s=function(t){var n=Vl.getValue(t);i.addToHistory(n.value,r.filetype)},l=Ey.parts.field({factory:DO,dismissOnBlur:!0,inputClasses:["tox-textfield"],sandboxClasses:["tox-dialog__popups"],inputAttributes:{"aria-errormessage":NO,type:"url"},minChars:0,responseTime:0,fetch:function(t){var n=zO(r.filetype,t,i),e=wC(n,nh.BUBBLE_TO_SANDBOX,o,!1);return ux(e)},getHotspot:function(t){return h.getOpt(t)},onSetValue:function(t,n){t.hasConfigured(hx)&&hx.run(t).get($)},typeaheadBehaviours:nc(rt([i.getValidationHandler().map(function(e){return hx.config({getRoot:function(t){return xr(t.element)},invalidClass:"tox-control-wrap--status-invalid",notify:{onInvalid:function(t,n){d.getOpt(t).each(function(t){Vr(t.element,"title",c.translate(n))})}},validator:{validate:function(t){var n=Vl.getValue(t);return FO(function(o){e({type:r.filetype,url:n.value},function(t){var n,e;"invalid"===t.status?(n=it.error(t.message),o(n)):(e=it.value(t.message),o(e))})})},validateOnLoad:!1}})}).toArray(),[tv.config({disabled:function(){return r.disabled||c.isReadOnly()}}),Ay.config({}),ag("urlinput-events",rt(["file"===r.filetype?[Zo(fo(),function(t){Go(t,Vy,{name:r.name})})]:[],[Zo(mo(),function(t){Go(t,Vy,{name:r.name}),s(t)}),Zo(wo(),function(t){Go(t,Vy,{name:r.name}),s(t)})]]))]])),eventOrder:((t={})[fo()]=["streaming","urlinput-events","invalidating"],t),model:{getDisplayText:function(t){return t.value},selectsOver:!1,populateFromBrowse:!1},markers:{openClass:"tox-textfield--popup-open"},lazySink:o.shared.getSink,parts:{menu:gh(0,0,"normal")},onExecute:function(t,n,e){Go(n,Ny,{})},onItemExecute:function(t,n,e,o){s(t),Go(t,Vy,{name:r.name})}}),f=r.label.map(function(t){return Ry(t,c)}),d=sp((n="invalid",e=st.some(NO),void 0===(u="warning")&&(u=n),void 0===a&&(a=n),{dom:{tag:"div",classes:["tox-icon","tox-control-wrap__status-icon-"+n],innerHtml:fp(u,c.icons),attributes:nt({title:c.translate(a),"aria-live":"polite"},e.fold(function(){return{}},function(t){return{id:t}}))}})),m=sp({dom:{tag:"div",classes:["tox-control-wrap__status-icon-wrap"]},components:[d.asSpec()]}),g=i.getUrlPicker(r.filetype),p=Xr("browser.url.event"),h=sp({dom:{tag:"div",classes:["tox-control-wrap"]},components:[l,m.asSpec()],behaviours:nc([tv.config({disabled:function(){return r.disabled||c.isReadOnly()}})])}),v=sp(BC({name:r.name,icon:st.some("browse"),text:r.label.getOr(""),disabled:r.disabled,primary:!1,borderless:!0},function(t){return Wo(t,p)},c,[],["tox-browse-url"]));return Ey.sketch({dom:Iy([]),components:f.toArray().concat([{dom:{tag:"div",classes:["tox-form__controls-h-stack"]},components:rt([[h.asSpec()],g.map(function(){return v.asSpec()}).toArray()])}]),fieldBehaviours:nc([tv.config({disabled:function(){return r.disabled||c.isReadOnly()},onDisabled:function(t){Ey.getField(t).each(tv.disable),v.getOpt(t).each(tv.disable)},onEnabled:function(t){Ey.getField(t).each(tv.enable),v.getOpt(t).each(tv.enable)}}),_v(),ag("url-input-events",[Zo(p,function(o){Kf.getCurrent(o).each(function(n){var t=Vl.getValue(n),e=nt({fieldname:r.name},t);g.each(function(t){t(e).get(function(t){Vl.setValue(n,t),Go(o,Vy,{name:r.name})})})})})])])})},jO=function(r){return function(n,e,o){return Mt(e,"name").fold(function(){return r(e,o)},function(t){return n.field(t,r(e,o))})}},UO={bar:jO(function(t,n){return e=t,o=n.shared,{dom:{tag:"div",classes:["tox-bar","tox-form__controls-h-stack"]},components:V(e.items,o.interpreter)};var e,o}),collection:jO(function(t,n){return Gy(t,n.shared.providers)}),alertbanner:jO(function(t,n){return e=t,o=n.shared.providers,Cy.sketch({dom:{tag:"div",attributes:{role:"alert"},classes:["tox-notification","tox-notification--in","tox-notification--"+e.level]},components:[{dom:{tag:"div",classes:["tox-notification__icon"]},components:[cp.sketch({dom:{tag:"button",classes:["tox-button","tox-button--naked","tox-button--icon"],innerHtml:fp(e.icon,o.icons),attributes:{title:o.translate(e.iconTooltip)}},action:function(t){Go(t,zy,{name:"alert-banner",value:e.url})}})]},{dom:{tag:"div",classes:["tox-notification__body"],innerHtml:o.translate(e.text)}}]});var e,o}),input:jO(function(t,n){return e=t,o=n.shared.providers,vO({name:e.name,multiline:!1,label:e.label,inputMode:e.inputMode,placeholder:e.placeholder,flex:!1,disabled:e.disabled,classname:"tox-textfield",validation:st.none(),maximized:e.maximized},o);var e,o}),textarea:jO(function(t,n){return e=t,o=n.shared.providers,vO({name:e.name,multiline:!0,label:e.label,inputMode:st.none(),placeholder:e.placeholder,flex:!0,disabled:e.disabled,classname:"tox-textarea",validation:st.none(),maximized:e.maximized},o);var e,o}),label:jO(function(t,n){return e=t,o=n.shared,r={dom:{tag:"label",innerHtml:o.providers.translate(e.label),classes:["tox-label"]}},i=V(e.items,o.interpreter),{dom:{tag:"div",classes:["tox-form__group"]},components:[r].concat(i),behaviours:nc([VS(),ug.config({}),YS(st.none()),ng.config({mode:"acyclic"})])};var e,o,r,i}),iframe:(aO=function(t,n){return rk(t,n.shared.providers)},function(t,n,e){var o=Ht(n,{source:"dynamic"});return jO(aO)(t,o,e)}),button:jO(function(t,n){return MC(t,n.shared.providers)}),checkbox:jO(function(t,n){return e=t,o=n.shared.providers,r=Vl.config({store:{mode:"manual",getValue:function(t){return t.element.dom.checked},setValue:function(t,n){t.element.dom.checked=n}}}),i=function(t){return t.element.dom.click(),st.some(!0)},u=Ey.parts.field({factory:{sketch:ct},dom:{tag:"input",classes:["tox-checkbox__input"],attributes:{type:"checkbox"}},behaviours:nc([VS(),tv.config({disabled:function(){return e.disabled||o.isReadOnly()}}),Ay.config({}),dg.config({}),r,ng.config({mode:"special",onEnter:i,onSpace:i,stopSpaceKeyup:!0}),ag("checkbox-events",[Zo(mo(),function(t,n){Go(t,Vy,{name:e.name})})])])}),a=Ey.parts.label({dom:{tag:"span",classes:["tox-checkbox__label"],innerHtml:o.translate(e.label)},behaviours:nc([Rx.config({})])}),s=sp({dom:{tag:"div",classes:["tox-checkbox__icons"]},components:[(c=function(t){return{dom:{tag:"span",classes:["tox-icon","tox-checkbox-icon__"+t],innerHtml:fp("checked"===t?"selected":"unselected",o.icons)}}})("checked"),c("unchecked")]}),Ey.sketch({dom:{tag:"label",classes:["tox-checkbox"]},components:[u,s.asSpec(),a],fieldBehaviours:nc([tv.config({disabled:function(){return e.disabled||o.isReadOnly()},disableClass:"tox-checkbox--disabled",onDisabled:function(t){Ey.getField(t).each(tv.disable)},onEnabled:function(t){Ey.getField(t).each(tv.enable)}}),_v()])});var e,o,r,i,u,a,c,s}),colorinput:jO(function(t,n){return zx(t,n.shared,n.colorinput)}),colorpicker:jO(function(t){var n=function(t){return"tox-"+t},e=RS(NS,n),r=sp(e.sketch({dom:{tag:"div",classes:["tox-color-picker-container"],attributes:{role:"presentation"}},onValidHex:function(t){Go(t,zy,{name:"hex-valid",value:!0})},onInvalidHex:function(t){Go(t,zy,{name:"hex-valid",value:!1})}}));return{dom:{tag:"div"},components:[r.asSpec()],behaviours:nc([Vl.config({store:{mode:"manual",getValue:function(t){var n=r.get(t);return Kf.getCurrent(n).bind(function(t){return Vl.getValue(t).hex}).map(function(t){return"#"+t}).getOr("")},setValue:function(t,n){var e=/^#([a-fA-F0-9]{3}(?:[a-fA-F0-9]{3})?)/.exec(n),o=r.get(t);Kf.getCurrent(o).fold(function(){console.log("Can not find form")},function(t){Vl.setValue(t,{hex:st.from(e[1]).getOr("")}),ES.getField(t,"hex").each(function(t){Wo(t,fo())})})}}}),VS()])}}),dropzone:jO(function(t,n){return KS(t,n.shared.providers)}),grid:jO(function(t,n){return e=t,o=n.shared,{dom:{tag:"div",classes:["tox-form__grid","tox-form__grid--"+e.columns+"col"]},components:V(e.items,o.interpreter)};var e,o}),listbox:jO(function(t,n){return o=t,e=(r=n).shared.providers,i=q(o.items).filter(dO),u=o.label.map(function(t){return Ry(t,e)}),a={dom:{tag:"div",classes:["tox-listboxfield"]},components:[Ey.parts.field({dom:{},factory:{sketch:function(t){return pC({uid:t.uid,text:i.map(function(t){return t.text}),icon:st.none(),tooltip:o.label,role:st.none(),fetch:function(t,n){var e=gO(t,o.name,o.items,Vl.getValue(t));n(wC(e,nh.CLOSE_ON_EXECUTE,r,!1))},onSetup:at($),getApi:at({}),columns:1,presets:"normal",classes:[],dropdownBehaviours:[Ay.config({}),Vl.config({store:{mode:"manual",initialValue:i.map(function(t){return t.value}).getOr(""),getValue:function(t){return Pr(t.element,mO)},setValue:function(n,t){pO(o.items,t).each(function(t){Vr(n.element,mO,t.value),Go(n,mC,{text:t.text})})}}})]},"tox-listbox",r.shared)}}})]},Ey.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:rt([u.toArray(),[a]]),fieldBehaviours:nc([tv.config({disabled:at(o.disabled),onDisabled:function(t){Ey.getField(t).each(tv.disable)},onEnabled:function(t){Ey.getField(t).each(tv.enable)}})])});var o,r,e,i,u,a}),selectbox:jO(function(t,n){return e=t,o=n.shared.providers,r=V(e.items,function(t){return{text:o.translate(t.text),value:t.value}}),i=e.label.map(function(t){return Ry(t,o)}),u=Ey.parts.field({dom:{},selectAttributes:{size:e.size},options:r,factory:hO,selectBehaviours:nc([tv.config({disabled:function(){return e.disabled||o.isReadOnly()}}),Ay.config({}),ag("selectbox-change",[Zo(mo(),function(t,n){Go(t,Vy,{name:e.name})})])])}),a=1<e.size?st.none():st.some({dom:{tag:"div",classes:["tox-selectfield__icon-js"],innerHtml:fp("chevron-down",o.icons)}}),c={dom:{tag:"div",classes:["tox-selectfield"]},components:rt([[u],a.toArray()])},Ey.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:rt([i.toArray(),[c]]),fieldBehaviours:nc([tv.config({disabled:function(){return e.disabled||o.isReadOnly()},onDisabled:function(t){Ey.getField(t).each(tv.disable)},onEnabled:function(t){Ey.getField(t).each(tv.enable)}}),_v()])});var e,o,r,i,u,a,c}),sizeinput:jO(function(t,n){return LC(t,n.shared.providers)}),urlinput:jO(function(t,n){return LO(t,n,n.urlinput)}),customeditor:jO(function(e){var o=se(st.none()),n=sp({dom:{tag:e.tag}}),r=se(st.none());return{dom:{tag:"div",classes:["tox-custom-editor"]},behaviours:nc([ag("custom-editor-events",[ur(function(t){n.getOpt(t).each(function(n){var t;t=e,(Object.prototype.hasOwnProperty.call(t,"init")?e.init(n.element.dom):LS.load(e.scriptId,e.scriptUrl).then(function(t){return t(n.element.dom,e.settings)})).then(function(n){r.get().each(function(t){n.setValue(t)}),r.set(st.none()),o.set(st.some(n))})})})]),Vl.config({store:{mode:"manual",getValue:function(){return o.get().fold(function(){return r.get().getOr("")},function(t){return t.getValue()})},setValue:function(t,n){o.get().fold(function(){r.set(st.some(n))},function(t){return t.setValue(n)})}}}),VS()]),components:[n.asSpec()]}}),htmlpanel:jO(function(t){return"presentation"===t.presets?Cy.sketch({dom:{tag:"div",classes:["tox-form__group"],innerHtml:t.html}}):Cy.sketch({dom:{tag:"div",classes:["tox-form__group"],innerHtml:t.html,attributes:{role:"document"}},containerBehaviours:nc([Ay.config({}),dg.config({})])})}),imagetools:jO(function(t,n){return fO(t,n.shared.providers)}),table:jO(function(t,n){return e=t,o=n.shared.providers,u=function(t){return{dom:{tag:"th",innerHtml:o.translate(t)}}},a=function(t){return{dom:{tag:"td",innerHtml:o.translate(t)}}},c=function(t){return{dom:{tag:"tr"},components:V(t,a)}},{dom:{tag:"table",classes:["tox-dialog__table"]},components:[(i=e.header,{dom:{tag:"thead"},components:[{dom:{tag:"tr"},components:V(i,u)}]}),(r=e.cells,{dom:{tag:"tbody"},components:V(r,c)})],behaviours:nc([Ay.config({}),dg.config({})])};var e,o,r,i,u,a,c}),panel:jO(function(t,n){return o=n,{dom:{tag:"div",classes:(e=t).classes},components:V(e.items,o.shared.interpreter)};var e,o})},WO={field:function(t,n){return n}},GO=function(n,t,e){var o=Ht(e,{shared:{interpreter:function(t){return XO(n,t,o)}}});return XO(n,t,o)},XO=function(n,e,o){return Mt(UO,e.type).fold(function(){return console.error('Unknown factory type "'+e.type+'", defaulting to container: ',e),e},function(t){return t(n,e,o)})},YO={valignCentre:[],alignCentre:[],alignLeft:[],alignRight:[],right:[],left:[],bottom:[],top:[]},qO=function(t,n,e){var o=Ic(-12,12,YO),r={maxHeightFunction:Bc()};return function(){return e()?{anchor:"node",root:Vi(t()),node:st.from(t()),bubble:o,layouts:{onRtl:function(){return[rp]},onLtr:function(){return[op]}},overrides:r}:{anchor:"hotspot",hotspot:n(),bubble:o,layouts:{onRtl:function(){return[Ia]},onLtr:function(){return[Ra]}},overrides:r}}},KO=function(t,n,e){return function(){return e()?{anchor:"node",root:Vi(t()),node:st.from(t()),layouts:{onRtl:function(){return[ip]},onLtr:function(){return[ip]}}}:{anchor:"hotspot",hotspot:n(),layouts:{onRtl:function(){return[za]},onLtr:function(){return[za]}}}}},JO=function(t,n,e){var o,r,i,u=yv(t),a=function(){return fe.fromDom(t.getBody())},c=function(){return fe.fromDom(t.getContentAreaContainer())},s=function(){return u||!e()};return{inlineDialog:qO(c,n,s),banner:KO(c,n,s),cursor:(r=t,function(){return{anchor:"selection",root:i(),getSelection:function(){var t=r.selection.getRng();return st.some(qc.range(fe.fromDom(t.startContainer),t.startOffset,fe.fromDom(t.endContainer),t.endOffset))}}}),node:(o=i=a,function(t){return{anchor:"node",root:o(),node:t}})}},$O=function(t){return{colorPicker:function(t,n){Mb(r)(t,n)},hasCustomColors:function(){return wb(o)},getColors:function(){return Sb(e)},getColorCols:(n=e=o=r=t,function(){return Ob(n)})};var n,e,o,r},QO=[{title:"Headings",items:[{title:"Heading 1",format:"h1"},{title:"Heading 2",format:"h2"},{title:"Heading 3",format:"h3"},{title:"Heading 4",format:"h4"},{title:"Heading 5",format:"h5"},{title:"Heading 6",format:"h6"}]},{title:"Inline",items:[{title:"Bold",format:"bold"},{title:"Italic",format:"italic"},{title:"Underline",format:"underline"},{title:"Strikethrough",format:"strikethrough"},{title:"Superscript",format:"superscript"},{title:"Subscript",format:"subscript"},{title:"Code",format:"code"}]},{title:"Blocks",items:[{title:"Paragraph",format:"p"},{title:"Blockquote",format:"blockquote"},{title:"Div",format:"div"},{title:"Pre",format:"pre"}]},{title:"Align",items:[{title:"Left",format:"alignleft"},{title:"Center",format:"aligncenter"},{title:"Right",format:"alignright"},{title:"Justify",format:"alignjustify"}]}],ZO=function(t){return N(t,function(t,n){if(Ft(n,"items")){var e=ZO(n.items);return{customFormats:t.customFormats.concat(e.customFormats),formats:t.formats.concat([{title:n.title,items:e.formats}])}}if(Ft(n,"inline")||Ft(n,"block")||Ft(n,"selector")){var o="custom-"+n.title.toLowerCase();return{customFormats:t.customFormats.concat([{name:o,format:n}]),formats:t.formats.concat([{title:n.title,format:o,icon:n.icon}])}}return nt(nt({},t),{formats:t.formats.concat(n)})},{customFormats:[],formats:[]})},t_=function(i){return t=i,st.from(t.getParam("style_formats")).filter(h).map(function(t){var n,e,o,r=(n=i,e=ZO(t),o=function(t){ot(t,function(t){n.formatter.has(t.name)||n.formatter.register(t.name,t.format)})},n.formatter?o(e.customFormats):n.on("init",function(){o(e.customFormats)}),e.formats);return i.getParam("style_formats_merge",!1,"boolean")?QO.concat(r):r}).getOr(QO);var t},n_=function(t,n,e){var o={type:"formatter",isSelected:n(t.format),getStylePreview:e(t.format)};return Ht(t,o)},e_=function(a,t,c,s){var l=function(t){return V(t,function(t){var n,e,o,r,i=kt(t);if(It(t,"items")){var u=l(t.items);return Ht(Ht(t,{type:"submenu"}),{getStyleItems:function(){return u}})}return It(t,"format")?n_(t,c,s):1===i.length&&M(i,"title")?Ht(t,{type:"separator"}):(e=Xr((n=t).title),o={type:"formatter",format:e,isSelected:c(e),getStylePreview:s(e)},r=Ht(n,o),a.formatter.register(e,r),r)})};return l(t)},o_=$C.trim,r_=function(n){return function(t){if(t&&1===t.nodeType){if(t.contentEditable===n)return!0;if(t.getAttribute("data-mce-contenteditable")===n)return!0}return!1}},i_=r_("true"),u_=r_("false"),a_=function(t,n,e,o,r){return{type:t,title:n,url:e,level:o,attach:r}},c_=function(t){return t.innerText||t.textContent},s_=function(t){return(n=t)&&"A"===n.nodeName&&(n.id||n.name)!==undefined&&f_(t);var n},l_=function(t){return t&&/^(H[1-6])$/.test(t.nodeName)},f_=function(t){return function(t){for(;t=t.parentNode;){var n=t.contentEditable;if(n&&"inherit"!==n)return i_(t)}return!1}(t)&&!u_(t)},d_=function(t){return l_(t)&&f_(t)},m_=function(t){var n,e,o=(n=t).id?n.id:Xr("h");return a_("header",c_(t),"#"+o,l_(e=t)?parseInt(e.nodeName.substr(1),10):0,function(){t.id=o})},g_=function(t){var n=t.id||t.name,e=c_(t);return a_("anchor",e||"#"+n,"#"+n,0,$)},p_=function(t){var n,e;return n="h1,h2,h3,h4,h5,h6,a:not([href])",e=t,V(ss(fe.fromDom(e),n),function(t){return t.dom})},h_=function(t){return 0<o_(t.title).length},v_=function(t){var n=p_(t);return H(V(H(n,d_),m_).concat(V(H(n,s_),g_)),h_)},b_="tinymce-url-history",y_=function(t){return S(t)&&/^https?/.test(t)},x_=function(t){return k(t)&&Dt(t,function(t){return!(h(n=t)&&n.length<=5&&W(n,y_));var n}).isNone()},w_=function(){var t,n=hb.getItem(b_);if(null===n)return{};try{t=JSON.parse(n)}catch(e){if(e instanceof SyntaxError)return console.log("Local storage "+b_+" was not valid JSON",e),{};throw e}return x_(t)?t:(console.log("Local storage "+b_+" was not valid format",t),{})},S_=function(t){var n=w_();return Object.prototype.hasOwnProperty.call(n,t)?n[t]:[]},k_=function(n,t){var e,o,r;y_(n)&&(e=w_(),o=Object.prototype.hasOwnProperty.call(e,t)?e[t]:[],r=H(o,function(t){return t!==n}),e[t]=[n].concat(r).slice(0,5),function(t){if(!x_(t))throw new Error("Bad format for history:\n"+JSON.stringify(t));hb.setItem(b_,JSON.stringify(t))}(e))},C_=function(t){return!!t},O_=function(t){return _t($C.makeMap(t,/[, ]/),C_)},__=function(t){return st.from(t.getParam("file_picker_callback")).filter(_)},T_=function(t,n){var e,o,r,i,u=(e=t,o=st.some(e.getParam("file_picker_types")).filter(C_),r=st.some(e.getParam("file_browser_callback_types")).filter(C_),i=o.or(r).map(O_),__(e).fold(function(){return!1},function(t){return i.fold(function(){return!0},function(t){return 0<kt(t).length&&t})}));return C(u)?u?__(t):st.none():u[n]?__(t):st.none()},E_=function(t){return st.from(t).filter(S).getOrUndefined()},B_=function(t){return st.from((e=(n=t).getParam("file_picker_validator_handler",undefined,"function"))===undefined?n.getParam("filepicker_validator_handler",undefined,"function"):e);var n,e},D_=function(n){return{getHistory:S_,addToHistory:k_,getLinkInformation:function(){return!1===(t=n).getParam("typeahead_urls")?st.none():st.some({targets:v_(t.getBody()),anchorTop:E_(t.getParam("anchor_top","#top")),anchorBottom:E_(t.getParam("anchor_bottom","#bottom"))});var t},getValidationHandler:function(){return B_(n)},getUrlPicker:function(t){return T_(r=n,i=t).map(function(o){return function(n){return ix(function(e){var t=nt({filetype:i,fieldname:n.fieldname},st.from(n.meta).getOr({}));o.call(r,function(t,n){if(!S(t))throw new Error("Expected value to be string");if(n!==undefined&&!k(n))throw new Error("Expected meta to be a object");e({value:t,meta:n})},n.value,t)})}});var r,i}}},A_=function(t,n,e){var o,r,i=se(!1),u={isPositionedAtTop:function(){return"top"===o.get()},getDockingMode:(o=se(vv(n)?"bottom":"top")).get,setDockingMode:o.set},a={shared:{providers:{icons:function(){return n.ui.registry.getAll().icons},menuItems:function(){return n.ui.registry.getAll().menuItems},translate:Nh.translate,isReadOnly:function(){return n.mode.isReadOnly()}},interpreter:function(t){return XO(WO,t,a)},anchors:JO(n,e,u.isPositionedAtTop),header:u,getSink:function(){return it.value(t)}},urlinput:D_(n),styleselect:function(o){var r=function(t){return function(){return o.formatter.match(t)}},i=function(n){return function(){var t=o.formatter.get(n);return t!==undefined?st.some({tag:0<t.length&&(t[0].inline||t[0].block)||"div",styles:o.dom.parseStyle(o.formatter.getCssText(n))}):st.none()}},u=function(t){var n=t.items;return n!==undefined&&0<n.length?U(n,u):[t.format]},a=se([]),c=se([]),e=se([]),s=se([]),l=se(!1);o.on("PreInit",function(t){var n=t_(o),e=e_(o,n,r,i);a.set(e),c.set(U(e,u))}),o.on("addStyleModifications",function(t){var n=e_(o,t.items,r,i);e.set(n),l.set(t.replace),s.set(U(n,u))});return{getData:function(){var t=l.get()?[]:a.get(),n=e.get();return t.concat(n)},getFlattenedKeys:function(){var t=l.get()?[]:c.get(),n=s.get();return t.concat(n)}}}(n),colorinput:$O(n),dialog:{isDraggableModal:(r=n,function(){return r.getParam("draggable_modal",!1,"boolean")})},isContextMenuOpen:function(){return i.get()},setContextMenuState:function(t){return i.set(t)}};return a},M_=at(function(t,n){var e,o,r;e=t,o=Math.floor(n),r=wu.max(e,o,["margin-left","border-left-width","padding-left","padding-right","border-right-width","margin-right"]),Wi(e,"max-width",r+"px")}),F_="contexttoolbar-hide",I_=at([Nn("dom"),te("shell",!0),Pl("toolbarBehaviours",[ug])]),R_=at([pf({name:"groups",overrides:function(){return{behaviours:nc([ug.config({})])}}})]),V_=Wf({name:"Toolbar",configFields:I_(),partFields:R_(),factory:function(n,t,e,o){var r=function(t){return n.shell?st.some(t):Tf(t,n,"groups")},i=n.shell?{behaviours:[ug.config({})],components:[]}:{behaviours:[],components:t};return{uid:n.uid,dom:n.dom,components:i.components,behaviours:zl(n.toolbarBehaviours,i.behaviours),apis:{setGroups:function(t,n){r(t).fold(function(){throw console.error("Toolbar was defined to not be a shell, but no groups container was specified in components"),new Error("Toolbar was defined to not be a shell, but no groups container was specified in components")},function(t){ug.set(t,n)})}},domModification:{attributes:{role:"group"}}}},apis:{setGroups:function(t,n,e){t.setGroups(n,e)}}}),P_=function(t,n,e){return{within:t,extra:n,withinWidth:e}},H_=function(t,n,o){var e,r=(e=function(t,n){var e=o(t);return st.some({element:t,start:n,finish:n+e,width:e})},N(t,function(n,t){return e(t,n.len).fold(at(n),function(t){return{len:t.finish,list:n.list.concat([t])}})},{len:0,list:[]}).list),i=H(r,function(t){return t.finish<=n}),u=z(i,function(t,n){return t+n.width},0);return{within:i,extra:r.slice(i.length),withinWidth:u}},z_=function(t){return V(t,function(t){return t.element})},N_=function(t,n,e,o){var r,i,u,a,c,s,l,f,d,m=(0===(r=H_(n,t,e)).extra.length?st.some(r):st.none()).getOrThunk(function(){return H_(n,t-e(o),e)}),g=m.within,p=m.extra,h=m.withinWidth;return 1===p.length&&p[0].width<=e(o)?(l=p,f=h,d=z_(g.concat(l)),P_(d,[],f)):1<=p.length?(u=p,a=o,c=h,s=z_(g).concat([a]),P_(s,z_(u),c)):(i=h,P_(z_(g),[],i))},L_=function(t,n){var e=V(n,function(t){return fu(t)});V_.setGroups(t,e)},j_=function(t,n,e){var o=Ef(t,n,"primary"),r=yx.getCoupled(t,"overflowGroup");Wi(o.element,"visibility","hidden");var i=n.builtGroups.get().concat([r]),u=Q(i,function(n){return fc(n.element).bind(function(t){return n.getSystem().getByDom(t).toOptional()})});e([]),L_(o,i);var a=Su(o.element),c=N_(a,n.builtGroups.get(),function(t){return Su(t.element)},r);0===c.extra.length?(ug.remove(o,r),e([])):(L_(o,c.within),e(c.extra)),$i(o.element,"visibility"),Qi(o.element),u.each(dg.focus)},U_=at([Pl("splitToolbarBehaviours",[yx]),ce("builtGroups",function(){return se([])})]),W_=at([ca(["overflowToggledClass"]),Qn("getOverflowBounds"),Nn("lazySink"),ce("overflowGroups",function(){return se([])})].concat(U_())),G_=at([mf({factory:V_,schema:I_(),name:"primary"}),gf({schema:I_(),name:"overflow"}),gf({name:"overflow-button"}),gf({name:"overflow-group"})]),X_=at([ca(["toggledClass"]),Nn("lazySink"),Wn("fetch"),Qn("getBounds"),Zn("fireDismissalEventInstead",[te("event",Vo())]),Nc()]),Y_=at([gf({name:"button",overrides:function(t){return{dom:{attributes:{"aria-haspopup":"true"}},buttonBehaviours:nc([Cg.config({toggleClass:t.markers.toggledClass,aria:{mode:"expanded"},toggleOnExecute:!1})])}}}),gf({factory:V_,schema:I_(),name:"toolbar",overrides:function(n){return{toolbarBehaviours:nc([ng.config({mode:"cyclic",onEscape:function(t){return Tf(t,n,"button").each(dg.focus),st.none()}})])}}})]),q_=function(t,n){var e=yx.getCoupled(t,"toolbarSandbox");hl.isOpen(e)?hl.close(e):hl.open(e,n.toolbar())},K_=function(t,n,e,o){var r=e.getBounds.map(function(t){return t()}),i=e.lazySink(t).getOrDie();qs.positionWithinBounds(i,{anchor:"hotspot",hotspot:t,layouts:o,overrides:{maxWidthFunction:M_()}},n,r)},J_=function(t,n,e,o,r){V_.setGroups(n,r),K_(t,n,e,o),Cg.on(t)},$_=Wf({name:"FloatingToolbarButton",factory:function(u,t,a,n){return nt(nt({},cp.sketch(nt(nt({},n.button()),{action:function(t){q_(t,n)},buttonBehaviours:Ll({dump:n.button().buttonBehaviours},[yx.config({others:{toolbarSandbox:function(t){return o=t,e=a,r=u,{dom:{tag:"div",attributes:{id:(i=Yu()).id}},behaviours:nc([ng.config({mode:"special",onEscape:function(t){return hl.close(t),st.some(!0)}}),hl.config({onOpen:function(t,n){r.fetch().get(function(t){J_(o,n,r,e.layouts,t),i.link(o.element),ng.focusIn(n)})},onClose:function(){Cg.off(o),dg.focus(o),i.unlink(o.element)},isPartOf:function(t,n,e){return Ku(n,e)||Ku(o,e)},getAttachPoint:function(){return r.lazySink(o).getOrDie()}}),ac.config({channels:nt(nt({},wl(nt({isExtraPart:c},r.fireDismissalEventInstead.map(function(t){return{fireEventInstead:{event:t.event}}}).getOr({})))),kl({doReposition:function(){hl.getState(yx.getCoupled(o,"toolbarSandbox")).each(function(t){K_(o,t,r,e.layouts)})}}))})])};var o,e,r,i}}})])}))),{apis:{setGroups:function(n,e){hl.getState(yx.getCoupled(n,"toolbarSandbox")).each(function(t){J_(n,t,u,a.layouts,e)})},reposition:function(n){hl.getState(yx.getCoupled(n,"toolbarSandbox")).each(function(t){K_(n,t,u,a.layouts)})},toggle:function(t){q_(t,n)},getToolbar:function(t){return hl.getState(yx.getCoupled(t,"toolbarSandbox"))},isOpen:function(t){return hl.isOpen(yx.getCoupled(t,"toolbarSandbox"))}}})},configFields:X_(),partFields:Y_(),apis:{setGroups:function(t,n,e){t.setGroups(n,e)},reposition:function(t,n){t.reposition(n)},toggle:function(t,n){t.toggle(n)},getToolbar:function(t,n){return t.getToolbar(n)},isOpen:function(t,n){return t.isOpen(n)}}}),Q_=at([Nn("items"),ca(["itemSelector"]),Pl("tgroupBehaviours",[ng])]),Z_=at([hf({name:"items",unit:"item"})]),tT=Wf({name:"ToolbarGroup",configFields:Q_(),partFields:Z_(),factory:function(t,n,e,o){return{uid:t.uid,dom:t.dom,components:n,behaviours:zl(t.tgroupBehaviours,[ng.config({mode:"flow",selector:t.markers.itemSelector})]),domModification:{attributes:{role:"toolbar"}}}}}),nT=function(t){return V(t,function(t){return fu(t)})},eT=function(t,e,o){j_(t,o,function(n){o.overflowGroups.set(n),e.getOpt(t).each(function(t){$_.setGroups(t,nT(n))})})},oT=Wf({name:"SplitFloatingToolbar",configFields:W_(),partFields:G_(),factory:function(e,t,n,o){var r=sp($_.sketch({fetch:function(){return ix(function(t){t(nT(e.overflowGroups.get()))})},layouts:{onLtr:function(){return[Ra,Ia]},onRtl:function(){return[Ia,Ra]},onBottomLtr:function(){return[Pa,Va]},onBottomRtl:function(){return[Va,Pa]}},getBounds:n.getOverflowBounds,lazySink:e.lazySink,fireDismissalEventInstead:{},markers:{toggledClass:e.markers.overflowToggledClass},parts:{button:o["overflow-button"](),toolbar:o.overflow()}}));return{uid:e.uid,dom:e.dom,components:t,behaviours:zl(e.splitToolbarBehaviours,[yx.config({others:{overflowGroup:function(){return tT.sketch(nt(nt({},o["overflow-group"]()),{items:[r.asSpec()]}))}}})]),apis:{setGroups:function(t,n){e.builtGroups.set(V(n,t.getSystem().build)),eT(t,r,e)},refresh:function(t){return eT(t,r,e)},toggle:function(t){r.getOpt(t).each(function(t){$_.toggle(t)})},isOpen:function(t){return r.getOpt(t).map($_.isOpen).getOr(!1)},reposition:function(t){r.getOpt(t).each(function(t){$_.reposition(t)})},getOverflow:function(t){return r.getOpt(t).bind($_.getToolbar)}},domModification:{attributes:{role:"group"}}}},apis:{setGroups:function(t,n,e){t.setGroups(n,e)},refresh:function(t,n){t.refresh(n)},reposition:function(t,n){t.reposition(n)},toggle:function(t,n){t.toggle(n)},isOpen:function(t,n){return t.isOpen(n)},getOverflow:function(t,n){return t.getOverflow(n)}}}),rT=function(n,t){return t.getAnimationRoot.fold(function(){return n.element},function(t){return t(n)})},iT=function(t){return t.dimension.property},uT=function(t,n){return t.dimension.getDimension(n)},aT=function(t,n){var e=rT(t,n);Di(e,[n.shrinkingClass,n.growingClass])},cT=function(t,n){Ti(t.element,n.openClass),Oi(t.element,n.closedClass),Wi(t.element,iT(n),"0px"),Qi(t.element)},sT=function(t,n){Ti(t.element,n.closedClass),Oi(t.element,n.openClass),$i(t.element,iT(n))},lT=function(t,n,e,o){e.setCollapsed(),Wi(t.element,iT(n),uT(n,t.element)),Qi(t.element),aT(t,n),cT(t,n),n.onStartShrink(t),n.onShrunk(t)},fT=function(t,n,e,o){var r=o.getOrThunk(function(){return uT(n,t.element)});e.setCollapsed(),Wi(t.element,iT(n),r),Qi(t.element);var i=rT(t,n);Ti(i,n.growingClass),Oi(i,n.shrinkingClass),cT(t,n),n.onStartShrink(t)},dT=function(t,n,e){var o=uT(n,t.element);("0px"===o?lT:fT)(t,n,e,st.some(o))},mT=function(t,n,e){var o=rT(t,n),r=Ei(o,n.shrinkingClass),i=uT(n,t.element);sT(t,n);var u=uT(n,t.element);(r?function(){Wi(t.element,iT(n),i),Qi(t.element)}:function(){cT(t,n)})(),Ti(o,n.shrinkingClass),Oi(o,n.growingClass),sT(t,n),Wi(t.element,iT(n),u),e.setExpanded(),n.onStartGrow(t)},gT=function(t,n,e){var o=rT(t,n);return!0===Ei(o,n.growingClass)},pT=function(t,n,e){var o=rT(t,n);return!0===Ei(o,n.shrinkingClass)},hT=/* */Object.freeze({__proto__:null,refresh:function(t,n,e){var o;e.isExpanded()&&($i(t.element,iT(n)),o=uT(n,t.element),Wi(t.element,iT(n),o))},grow:function(t,n,e){e.isExpanded()||mT(t,n,e)},shrink:function(t,n,e){e.isExpanded()&&dT(t,n,e)},immediateShrink:function(t,n,e){e.isExpanded()&&lT(t,n,e,st.none())},hasGrown:function(t,n,e){return e.isExpanded()},hasShrunk:function(t,n,e){return e.isCollapsed()},isGrowing:gT,isShrinking:pT,isTransitioning:function(t,n,e){return!0===gT(t,n)||!0===pT(t,n)},toggleGrow:function(t,n,e){(e.isExpanded()?dT:mT)(t,n,e)},disableTransitions:aT}),vT=/* */Object.freeze({__proto__:null,exhibit:function(t,n,e){var o=n.expanded;return fi(o?{classes:[n.openClass],styles:{}}:{classes:[n.closedClass],styles:Kt(n.dimension.property,"0px")})},events:function(e,o){return Jo([ir(po(),function(t,n){n.event.raw.propertyName===e.dimension.property&&(aT(t,e),o.isExpanded()&&$i(t.element,e.dimension.property),(o.isExpanded()?e.onGrown:e.onShrunk)(t))})])}}),bT=[Nn("closedClass"),Nn("openClass"),Nn("shrinkingClass"),Nn("growingClass"),qn("getAnimationRoot"),la("onShrunk"),la("onStartShrink"),la("onGrown"),la("onStartGrow"),te("expanded",!1),Ln("dimension",Dn("property",{width:[ga("property","width"),ga("getDimension",function(t){return Su(t)+"px"})],height:[ga("property","height"),ga("getDimension",function(t){return gu(t)+"px"})]}))],yT=oc({fields:bT,name:"sliding",active:vT,apis:hT,state:/* */Object.freeze({__proto__:null,init:function(t){var n=se(t.expanded);return si({isExpanded:function(){return!0===n.get()},isCollapsed:function(){return!1===n.get()},setCollapsed:g(n.set,!1),setExpanded:g(n.set,!0),readState:function(){return"expanded: "+n.get()}})}})}),xT=at([ca(["closedClass","openClass","shrinkingClass","growingClass","overflowToggledClass"]),la("onOpened"),la("onClosed")].concat(U_())),wT=at([mf({factory:V_,schema:I_(),name:"primary"}),mf({factory:V_,schema:I_(),name:"overflow",overrides:function(n){return{toolbarBehaviours:nc([yT.config({dimension:{property:"height"},closedClass:n.markers.closedClass,openClass:n.markers.openClass,shrinkingClass:n.markers.shrinkingClass,growingClass:n.markers.growingClass,onShrunk:function(t){Tf(t,n,"overflow-button").each(function(t){Cg.off(t),dg.focus(t)}),n.onClosed(t)},onGrown:function(t){ng.focusIn(t),n.onOpened(t)},onStartGrow:function(t){Tf(t,n,"overflow-button").each(Cg.on)}}),ng.config({mode:"acyclic",onEscape:function(t){return Tf(t,n,"overflow-button").each(dg.focus),st.some(!0)}})])}}}),gf({name:"overflow-button",overrides:function(t){return{buttonBehaviours:nc([Cg.config({toggleClass:t.markers.overflowToggledClass,aria:{mode:"pressed"},toggleOnExecute:!1})])}}}),gf({name:"overflow-group"})]),ST=function(n,e){Tf(n,e,"overflow-button").bind(function(){return Tf(n,e,"overflow")}).each(function(t){kT(n,e),yT.toggleGrow(t)})},kT=function(t,n){Tf(t,n,"overflow").each(function(e){j_(t,n,function(t){var n=V(t,function(t){return fu(t)});V_.setGroups(e,n)}),Tf(t,n,"overflow-button").each(function(t){yT.hasGrown(e)&&Cg.on(t)}),yT.refresh(e)})},CT=Wf({name:"SplitSlidingToolbar",configFields:xT(),partFields:wT(),factory:function(o,t,n,e){var r="alloy.toolbar.toggle";return{uid:o.uid,dom:o.dom,components:t,behaviours:zl(o.splitToolbarBehaviours,[yx.config({others:{overflowGroup:function(n){return tT.sketch(nt(nt({},e["overflow-group"]()),{items:[cp.sketch(nt(nt({},e["overflow-button"]()),{action:function(t){Wo(n,r)}}))]}))}}}),ag("toolbar-toggle-events",[Zo(r,function(t){ST(t,o)})])]),apis:{setGroups:function(t,n){var e;e=V(n,t.getSystem().build),o.builtGroups.set(e),kT(t,o)},refresh:function(t){return kT(t,o)},toggle:function(t){return ST(t,o)},isOpen:function(t){return Tf(t,o,"overflow").map(yT.hasGrown).getOr(!1)}},domModification:{attributes:{role:"group"}}}},apis:{setGroups:function(t,n,e){t.setGroups(n,e)},refresh:function(t,n){t.refresh(n)},toggle:function(t,n){t.toggle(n)},isOpen:function(t,n){return t.isOpen(n)}}}),OT=at(Xr("toolbar-height-change")),_T=function(t){var n=t.title.fold(function(){return{}},function(t){return{attributes:{title:t}}});return{dom:nt({tag:"div",classes:["tox-toolbar__group"]},n),components:[tT.parts.items({})],items:t.items,markers:{itemSelector:"*:not(.tox-split-button) > .tox-tbtn:not([disabled]), .tox-split-button:not([disabled]), .tox-toolbar-nav-js:not([disabled])"},tgroupBehaviours:nc([Ay.config({}),dg.config({})])}},TT=function(t){return tT.sketch(_T(t))},ET=function(e,t){var n=ur(function(t){var n=V(e.initGroups,TT);V_.setGroups(t,n)});return nc([Dv(e.providers.isReadOnly),_v(),ng.config({mode:t,onEscape:e.onEscape,selector:".tox-toolbar__group"}),ag("toolbar-events",[n])])},BT=function(t){var n=t.cyclicKeying?"cyclic":"acyclic";return{uid:t.uid,dom:{tag:"div",classes:["tox-toolbar-overlord"]},parts:{"overflow-group":_T({title:st.none(),items:[]}),"overflow-button":_C({name:"more",icon:st.some("more-drawer"),disabled:!1,tooltip:st.some("More..."),primary:!1,borderless:!1},st.none(),t.providers)},splitToolbarBehaviours:ET(t,n)}},DT=function(i){var t=BT(i),n=oT.parts.primary({dom:{tag:"div",classes:["tox-toolbar__primary"]}});return oT.sketch(nt(nt({},t),{lazySink:i.getSink,getOverflowBounds:function(){var t=i.moreDrawerData.lazyHeader().element,n=Vu(t),e=br(t),o=Vu(e),r=Math.max(e.dom.scrollHeight,o.height);return Iu(n.x+4,o.y,n.width-8,r)},parts:nt(nt({},t.parts),{overflow:{dom:{tag:"div",classes:["tox-toolbar__overflow"],attributes:i.attributes}}}),components:[n],markers:{overflowToggledClass:"tox-tbtn--enabled"}}))},AT=function(t){var n=CT.parts.primary({dom:{tag:"div",classes:["tox-toolbar__primary"]}}),e=CT.parts.overflow({dom:{tag:"div",classes:["tox-toolbar__overflow"]}}),o=BT(t);return CT.sketch(nt(nt({},o),{components:[n,e],markers:{openClass:"tox-toolbar__overflow--open",closedClass:"tox-toolbar__overflow--closed",growingClass:"tox-toolbar__overflow--growing",shrinkingClass:"tox-toolbar__overflow--shrinking",overflowToggledClass:"tox-tbtn--enabled"},onOpened:function(t){t.getSystem().broadcastOn([OT()],{type:"opened"})},onClosed:function(t){t.getSystem().broadcastOn([OT()],{type:"closed"})}}))},MT=function(t){var n=t.cyclicKeying?"cyclic":"acyclic";return V_.sketch({uid:t.uid,dom:{tag:"div",classes:["tox-toolbar"].concat(t.type===Zp.scrolling?["tox-toolbar--scrolling"]:[])},components:[V_.parts.groups({})],toolbarBehaviours:ET(t,n)})},FT=ln([jn("type"),Ln("items",dn([wn([jn("name"),Yn("items",In)]),In]))].concat(Hp)),IT=[$n("text"),$n("tooltip"),$n("icon"),Wn("fetch"),ue("onSetup",function(){return $})],RT=ln(w([jn("type")],IT)),VT=function(t){return On("menubutton",RT,t)},PT=ln([jn("type"),$n("tooltip"),$n("icon"),$n("text"),Qn("select"),Wn("fetch"),ue("onSetup",function(){return $}),re("presets","normal",["normal","color","listpreview"]),te("columns",1),Wn("onAction"),Wn("onItemAction")]),HT=/* */Object.freeze({__proto__:null,events:function(i,u){var r=function(o,r){i.updateState.each(function(t){var n=t(o,r);u.set(n)}),i.renderComponents.each(function(t){var n=t(r,u.get()),e=V(n,o.getSystem().build);Qs(o,e)})};return Jo([Zo(So(),function(t,n){var e,o=n;o.universal||(e=i.channel,M(o.channels,e)&&r(t,o.data))}),ur(function(n,t){i.initialData.each(function(t){r(n,t)})})])}}),zT=/* */Object.freeze({__proto__:null,getState:function(t,n,e){return e}}),NT=[Nn("channel"),qn("renderComponents"),qn("updateState"),qn("initialData")],LT=oc({fields:NT,name:"reflecting",active:HT,apis:zT,state:/* */Object.freeze({__proto__:null,init:function(){var n=se(st.none());return{readState:function(){return n.get().fold(function(){return"none"},function(t){return t})},get:function(){return n.get()},set:function(t){return n.set(t)},clear:function(){return n.set(st.none())}}}})}),jT=at([Nn("toggleClass"),Nn("fetch"),da("onExecute"),te("getHotspot",st.some),te("getAnchorOverrides",at({})),Nc(),da("onItemExecute"),qn("lazySink"),Nn("dom"),la("onOpen"),Pl("splitDropdownBehaviours",[yx,ng,dg]),te("matchWidth",!1),te("useMinWidth",!1),te("eventOrder",{}),qn("role")].concat(Ax())),UT=mf({factory:cp,schema:[Nn("dom")],name:"arrow",defaults:function(){return{buttonBehaviours:nc([dg.revoke()])}},overrides:function(n){return{dom:{tag:"span",attributes:{role:"presentation"}},action:function(t){t.getSystem().getByUid(n.uid).each(Xo)},buttonBehaviours:nc([Cg.config({toggleOnExecute:!1,toggleClass:n.toggleClass})])}}}),WT=mf({factory:cp,schema:[Nn("dom")],name:"button",defaults:function(){return{buttonBehaviours:nc([dg.revoke()])}},overrides:function(e){return{dom:{tag:"span",attributes:{role:"presentation"}},action:function(n){n.getSystem().getByUid(e.uid).each(function(t){e.onExecute(t,n)})}}}}),GT=at([UT,WT,pf({factory:{sketch:function(t){return{uid:t.uid,dom:{tag:"span",styles:{display:"none"},attributes:{"aria-hidden":"true"},innerHtml:t.text}}}},schema:[Nn("text")],name:"aria-descriptor"}),gf({schema:[aa()],name:"menu",defaults:function(o){return{onExecute:function(n,e){n.getSystem().getByUid(o.uid).each(function(t){o.onItemExecute(t,n,e)})}}}}),wx()]),XT=Wf({name:"SplitDropdown",configFields:jT(),partFields:GT(),factory:function(o,t,n,e){var r,i=function(t){Kf.getCurrent(t).each(function(t){ud.highlightFirst(t),ng.focusIn(t)})},u=function(t){Ox(o,function(t){return t},t,e,i,Qy.HighlightFirst).get($)},a=function(t){var n=Ef(t,o,"button");return Xo(n),st.some(!0)},c=nt(nt({},Jo([ur(function(e,t){Tf(e,o,"aria-descriptor").each(function(t){var n=Xr("aria");Vr(t.element,"id",n),Vr(e.element,"aria-describedby",n)})})])),_g(st.some(u))),s={repositionMenus:function(t){Cg.isOn(t)&&Dx(t)}};return{uid:o.uid,dom:o.dom,components:t,apis:s,eventOrder:nt(nt({},o.eventOrder),((r={})[ko()]=["disabling","toggling","alloy.base.behaviour"],r)),events:c,behaviours:zl(o.splitDropdownBehaviours,[yx.config({others:{sandbox:function(t){var n=Ef(t,o,"arrow");return Bx(o,t,{onOpen:function(){Cg.on(n),Cg.on(t)},onClose:function(){Cg.off(n),Cg.off(t)}})}}}),ng.config({mode:"special",onSpace:a,onEnter:a,onDown:function(t){return u(t),st.some(!0)}}),dg.config({}),Cg.config({toggleOnExecute:!1,aria:{mode:"expanded"}})]),domModification:{attributes:{role:o.role.getOr("button"),"aria-haspopup":!0}}}},apis:{repositionMenus:function(t,n){return t.repositionMenus(n)}}}),YT=function(n){return{isDisabled:function(){return tv.isDisabled(n)},setDisabled:function(t){return tv.set(n,t)}}},qT=function(n){return{setActive:function(t){Cg.set(n,t)},isActive:function(){return Cg.isOn(n)},isDisabled:function(){return tv.isDisabled(n)},setDisabled:function(t){return tv.set(n,t)}}},KT=function(t,n){return t.map(function(t){return{"aria-label":n.translate(t),title:n.translate(t)}}).getOr({})},JT=Xr("focus-button"),$T=["checklist","ordered-list"],QT=["indent","outdent","table-insert-column-after","table-insert-column-before","unordered-list"],ZT=function(n,e,t,o,r,i){var u,a=function(t){return Nh.isRtl()&&M($T,t)?t+"-rtl":t},c=Nh.isRtl()&&n.exists(function(t){return M(QT,t)});return{dom:{tag:"button",classes:["tox-tbtn"].concat(e.isSome()?["tox-tbtn--select"]:[]).concat(c?["tox-tbtn__icon-rtl"]:[]),attributes:KT(t,i)},components:Rv([n.map(function(t){return cC(a(t),i.icons)}),e.map(function(t){return lC(t,"tox-tbtn",i)})]),eventOrder:((u={})[eo()]=["focusing","alloy.base.behaviour","common-button-display-events"],u),buttonBehaviours:nc([Dv(i.isReadOnly),_v(),ag("common-button-display-events",[Zo(eo(),function(t,n){n.event.prevent(),Wo(t,JT)})])].concat(o.map(function(t){return LT.config({channel:t,initialData:{icon:n,text:e},renderComponents:function(t,n){return Rv([t.icon.map(function(t){return cC(a(t),i.icons)}),t.text.map(function(t){return lC(t,"tox-tbtn",i)})])}})}).toArray()).concat(r.getOr([])))}},tE=function(t,n,e){var o,r=se($),i=ZT(t.icon,t.text,t.tooltip,st.none(),st.none(),e);return cp.sketch({dom:i.dom,components:i.components,eventOrder:dC,buttonBehaviours:nc([ag("toolbar-button-events",[(o={onAction:t.onAction,getApi:n.getApi},sr(function(n,t){Av(o,n)(function(t){Go(n,fC,{buttonApi:t}),o.onAction(t)})})),Mv(n,r),Fv(n,r)]),Dv(function(){return t.disabled||e.isReadOnly()}),_v()].concat(n.toolbarButtonBehaviours))})},nE=function(t,n,e){return tE(t,{toolbarButtonBehaviours:[].concat(0<e.length?[ag("toolbarButtonWith",e)]:[]),getApi:YT,onSetup:t.onSetup},n)},eE=function(t,n,e){return Ht(tE(t,{toolbarButtonBehaviours:[ug.config({}),Cg.config({toggleClass:"tox-tbtn--enabled",aria:{mode:"pressed"},toggleOnExecute:!1})].concat(0<e.length?[ag("toolbarToggleButtonWith",e)]:[]),getApi:qT,onSetup:t.onSetup},n))},oE=function(n,t){var e,o,r,i,u=Xr("channel-update-split-dropdown-display"),a=function(e){return{isDisabled:function(){return tv.isDisabled(e)},setDisabled:function(t){return tv.set(e,t)},setIconFill:function(t,n){Gu(e.element,'svg path[id="'+t+'"], rect[id="'+t+'"]').each(function(t){Vr(t,"fill",n)})},setIconStroke:function(t,n){Gu(e.element,'svg path[id="'+t+'"], rect[id="'+t+'"]').each(function(t){Vr(t,"stroke",n)})},setActive:function(n){Vr(e.element,"aria-pressed",n),Gu(e.element,"span").each(function(t){e.getSystem().getByDom(t).each(function(t){return Cg.set(t,n)})})},isActive:function(){return Gu(e.element,"span").exists(function(t){return e.getSystem().getByDom(t).exists(Cg.isOn)})}}},c=se($),s={getApi:a,onSetup:n.onSetup};return XT.sketch({dom:{tag:"div",classes:["tox-split-button"],attributes:nt({"aria-pressed":!1},KT(n.tooltip,t.providers))},onExecute:function(t){n.onAction(a(t))},onItemExecute:function(t,n,e){},splitDropdownBehaviours:nc([Bv(t.providers.isReadOnly),_v(),ag("split-dropdown-events",[Zo(JT,dg.focus),Mv(s,c),Fv(s,c)]),Rx.config({})]),eventOrder:((e={})[Io()]=["alloy.base.behaviour","split-dropdown-events"],e),toggleClass:"tox-tbtn--enabled",lazySink:t.getSink,fetch:(o=a,r=n,i=t.providers,function(n){return ix(function(t){return r.fetch(t)}).map(function(t){return st.from($b(Ht(Fb(Xr("menu-value"),t,function(t){r.onItemAction(o(n),t)},r.columns,r.presets,nh.CLOSE_ON_EXECUTE,r.select.getOr(function(){return!1}),i),{movement:Rb(r.columns,r.presets),menuBehaviours:Dh("auto"!==r.columns?[]:[ur(function(o,t){Eh(o,4,lh(r.presets)).each(function(t){var n=t.numRows,e=t.numColumns;ng.setGridSize(o,n,e)})})])})))})}),parts:{menu:gh(0,n.columns,n.presets)},components:[XT.parts.button(ZT(n.icon,n.text,st.none(),st.some(u),st.some([Cg.config({toggleClass:"tox-tbtn--enabled",toggleOnExecute:!1})]),t.providers)),XT.parts.arrow({dom:{tag:"button",classes:["tox-tbtn","tox-split-button__chevron"],innerHtml:fp("chevron-down",t.providers.icons)},buttonBehaviours:nc([Bv(t.providers.isReadOnly),_v()])}),XT.parts["aria-descriptor"]({text:t.providers.translate("To open the popup, press Shift+Enter")})]})},rE=function(i,u){return Zo(fC,function(t,n){var e,o=i.get(t),r=(e=o,{hide:function(){return Wo(e,To())},getValue:function(){return Vl.getValue(e)}});u.onAction(r,n.event.buttonApi)})},iE=function(t,n,e){var o,r,i,u,a,c,s,l,f,d,m,g,p={backstage:{shared:{providers:e}}};return"contextformtogglebutton"===n.type?(s=t,f=p,(d=(l=n).original).primary,m=y(d,["primary"]),g=_n(Up(nt(nt({},m),{type:"togglebutton",onAction:function(){}}))),eE(g,f.backstage.shared.providers,[rE(s,l)])):(o=t,i=p,(u=(r=n).original).primary,a=y(u,["primary"]),c=_n(Np(nt(nt({},a),{type:"button",onAction:function(){}}))),nE(c,i.backstage.shared.providers,[rE(o,r)]))},uE=function(t,n){var e,o,r,i,u=t.label.fold(function(){return{}},function(t){return{"aria-label":t}}),a=sp(Ky.sketch({inputClasses:["tox-toolbar-textfield","tox-toolbar-nav-js"],data:t.initValue(),inputAttributes:u,selectOnFocus:!0,inputBehaviours:nc([ng.config({mode:"special",onEnter:function(t){return c.findPrimary(t).map(function(t){return Xo(t),!0})},onLeft:function(t,n){return n.cut(),st.none()},onRight:function(t,n){return n.cut(),st.none()}})])})),c=(e=a,o=t.commands,r=n,i=V(o,function(t){return sp(iE(e,t,r))}),{asSpecs:function(){return V(i,function(t){return t.asSpec()})},findPrimary:function(e){return Q(o,function(t,n){return t.primary?st.from(i[n]).bind(function(t){return t.getOpt(e)}).filter(x(tv.isDisabled)):st.none()})}});return[{title:st.none(),items:[a.asSpec()]},{title:st.none(),items:c.asSpecs()}]},aE=uE,cE=function(t,n){var e,o,r,i,u,a=Du(window),c=Ru(fe.fromDom(t.getContentAreaContainer())),s=cv(t)||sv(t)||fv(t),l=(e=c,o=a,r=Math.max(o.x,e.x),i=e.right-r,u=o.width-(r-o.x),{x:r,width:Math.min(i,u)}),f=l.x,d=l.width;if(t.inline&&!s)return Iu(f,a.y,d,a.height);var m=function(t,n,e,o){var r=fe.fromDom(t.getContainer()),i=Gu(r,".tox-editor-header").getOr(r),u=Ru(i),a=u.y>=n.bottom,c=o&&!a;if(t.inline&&c)return{y:Math.max(u.bottom,e.y),bottom:e.bottom};if(t.inline&&!c)return{y:e.y,bottom:Math.min(u.y,e.bottom)};var s=Ru(r);return c?{y:Math.max(u.bottom,e.y),bottom:Math.min(s.bottom,e.bottom)}:{y:Math.max(s.y,e.y),bottom:Math.min(u.y,e.bottom)}}(t,c,a,n.header.isPositionedAtTop()),g=m.y,p=m.bottom;return Iu(f,g,d,p-g)},sE=function(n,t){var e=H(t,function(t){return t.predicate(n.dom)}),o=P(e,function(t){return"contexttoolbar"===t.type});return{contextToolbars:o.pass,contextForms:o.fail}},lE=function(t,n,e){var o=sE(t,n);if(0<o.contextForms.length)return st.some({elem:t,toolbars:[o.contextForms[0]]});var r=sE(t,e);if(0<r.contextForms.length)return st.some({elem:t,toolbars:[r.contextForms[0]]});if(0<o.contextToolbars.length||0<r.contextToolbars.length){var i=function(t){if(t.length<=1)return t;var n=function(n){return F(t,function(t){return t.position===n})},e=function(n){return H(t,function(t){return t.position===n})},o=n("selection"),r=n("node");if(o||r){if(r&&o){var i=e("node"),u=V(e("selection"),function(t){return nt(nt({},t),{position:"node"})});return i.concat(u)}return e(o?"selection":"node")}return e("line")}(o.contextToolbars.concat(r.contextToolbars));return st.some({elem:t,toolbars:i})}return st.none()},fE=function(t,n,i){return t(n)?st.none():Xe(n,function(t){var n=sE(t,i.inNodeScope),e=n.contextToolbars,o=n.contextForms,r=0<o.length?o:function(t){if(t.length<=1)return t;var n=function(n){return L(t,function(t){return t.position===n})};return n("selection").orThunk(function(){return n("node")}).orThunk(function(){return n("line")}).map(function(t){return t.position}).fold(function(){return[]},function(n){return H(t,function(t){return t.position===n})})}(e);return 0<r.length?st.some({elem:t,toolbars:r}):st.none()},t)},dE=function(e,r){var t={},i=[],u=[],a={},c={},o=function(n,e){var o=_n(On("ContextForm",Jp,e));(t[n]=o).launch.map(function(t){a["form:"+n]=nt(nt({},e.launch),{type:"contextformtogglebutton"===t.type?"togglebutton":"button",onAction:function(){r(o)}})}),"editor"===o.scope?u.push(o):i.push(o),c[n]=o},s=function(n,e){On("ContextToolbar",$p,e).each(function(t){"editor"===e.scope?u.push(t):i.push(t),c[n]=t})},n=kt(e);return ot(n,function(t){var n=e[t];"contextform"===n.type?o(t,n):"contexttoolbar"===n.type&&s(t,n)}),{forms:t,inNodeScope:i,inEditorScope:u,lookupTable:c,formNavigators:a}},mE=Xr("forward-slide"),gE=Xr("backward-slide"),pE=Xr("change-slide-event"),hE="tox-pop--resizing",vE=function(t,n,e){var u,a,r,c,s,o=e.dataset,i="basic"===o.type?function(){return V(o.data,function(t){return n_(t,e.isSelectedFor,e.getPreviewFor)})}:o.getData;return{items:(u=n,a=e,r=function(t,n,e,o){var r=u.shared.providers.translate(t.title);if("separator"===t.type)return st.some({type:"separator",text:r});if("submenu"!==t.type)return st.some(nt({type:"togglemenuitem",text:r,icon:t.icon,active:t.isSelected(o),disabled:e,onAction:a.onAction(t)},t.getStylePreview().fold(function(){return{}},function(t){return{meta:{style:t}}})));var i=U(t.getStyleItems(),function(t){return c(t,n,o)});return 0===n&&i.length<=0?st.none():st.some({type:"nestedmenuitem",text:r,disabled:i.length<=0,getSubmenuItems:function(){return U(t.getStyleItems(),function(t){return c(t,n,o)})}})},c=function(t,n,e){var o="formatter"===t.type&&a.isInvalid(t);return 0===n?o?[]:r(t,n,!1,e).toArray():r(t,n,o,e).toArray()},{validateItems:s=function(t){var n=a.getCurrentValue(),e=a.shouldHide?0:1;return U(t,function(t){return c(t,e,n)})},getFetch:function(r,i){return function(t,n){var e=i(),o=s(e);n(wC(o,nh.CLOSE_ON_EXECUTE,r,!1))}}}),getStyleItems:i}},bE=function(o,t,n){var e=vE(0,t,n),r=e.items,i=e.getStyleItems;return pC({text:n.icon.isSome()?st.none():st.some(""),icon:n.icon,tooltip:st.from(n.tooltip),role:st.none(),fetch:r.getFetch(t,i),onSetup:function(e){return n.setInitialValue.each(function(t){return t(e.getComponent())}),n.nodeChangeHandler.map(function(t){var n=t(e.getComponent());return o.on("NodeChange",n),function(){o.off("NodeChange",n)}}).getOr($)},getApi:function(t){return{getComponent:function(){return t}}},columns:1,presets:"normal",classes:n.icon.isSome()?[]:["bespoke"],dropdownBehaviours:[]},"tox-tbtn",t.shared)};(sO=cO=cO||{})[sO.SemiColon=0]="SemiColon",sO[sO.Space=1]="Space";var yE,xE,wE,SE,kE,CE,OE=function(t,n,e,o){var r,i,u=t.getParam(n,e,"string");return{type:"basic",data:(i=u,r=o===cO.SemiColon?i.replace(/;$/,"").split(";"):i.split(" "),V(r,function(t){var n=t,e=t,o=t.split("=");return 1<o.length&&(n=o[0],e=o[1]),{title:n,format:e}}))}},_E=[{title:"Left",icon:"align-left",format:"alignleft",command:"JustifyLeft"},{title:"Center",icon:"align-center",format:"aligncenter",command:"JustifyCenter"},{title:"Right",icon:"align-right",format:"alignright",command:"JustifyRight"},{title:"Justify",icon:"align-justify",format:"alignjustify",command:"JustifyFull"}],TE=function(e){var n=function(t){var n=L(_E,function(t){return e.formatter.match(t.format)}).fold(function(){return"left"},function(t){return t.title.toLowerCase()});Go(t,gC,{icon:"align-"+n})},t=st.some(function(t){return function(){return n(t)}}),o=st.some(n),r={type:"basic",data:_E};return{tooltip:"Align",icon:st.some("align-left"),isSelectedFor:function(t){return function(){return e.formatter.match(t)}},getCurrentValue:st.none,getPreviewFor:function(t){return function(){return st.none()}},onAction:function(n){return function(){return L(_E,function(t){return t.format===n.format}).each(function(t){return e.execCommand(t.command)})}},setInitialValue:o,nodeChangeHandler:t,dataset:r,shouldHide:!1,isInvalid:function(t){return!e.formatter.canApply(t.format)}}},EE=["-apple-system","Segoe UI","Roboto","Helvetica Neue","sans-serif"],BE=function(t){var n=t.split(/\s*,\s*/);return V(n,function(t){return t.replace(/^['"]+|['"]+$/g,"")})},DE=function(t){var n;return 0===t.indexOf("-apple-system")&&(n=BE(t.toLowerCase()),W(EE,function(t){return-1<n.indexOf(t.toLowerCase())}))},AE=function(r){var i=function(){var e=function(t){return t?BE(t)[0]:""},t=r.queryCommandValue("FontName"),n=u.data,o=t?t.toLowerCase():"";return{matchOpt:L(n,function(t){var n=t.format;return n.toLowerCase()===o||e(n).toLowerCase()===e(o).toLowerCase()}).orThunk(function(){return DE(o)?st.from({title:"System Font",format:o}):st.none()}),font:t}},n=function(t){var n=i(),e=n.matchOpt,o=n.font,r=e.fold(function(){return o},function(t){return t.title});Go(t,mC,{text:r})},t=st.some(function(t){return function(){return n(t)}}),e=st.some(n),u=OE(r,"font_formats","Andale Mono=andale mono,monospace;Arial=arial,helvetica,sans-serif;Arial Black=arial black,sans-serif;Book Antiqua=book antiqua,palatino,serif;Comic Sans MS=comic sans ms,sans-serif;Courier New=courier new,courier,monospace;Georgia=georgia,palatino,serif;Helvetica=helvetica,arial,sans-serif;Impact=impact,sans-serif;Symbol=symbol;Tahoma=tahoma,arial,helvetica,sans-serif;Terminal=terminal,monaco,monospace;Times New Roman=times new roman,times,serif;Trebuchet MS=trebuchet ms,geneva,sans-serif;Verdana=verdana,geneva,sans-serif;Webdings=webdings;Wingdings=wingdings,zapf dingbats",cO.SemiColon);return{tooltip:"Fonts",icon:st.none(),isSelectedFor:function(n){return function(t){return t.exists(function(t){return t.format===n})}},getCurrentValue:function(){return i().matchOpt},getPreviewFor:function(t){return function(){return st.some({tag:"div",styles:-1===t.indexOf("dings")?{"font-family":t}:{}})}},onAction:function(t){return function(){r.undoManager.transact(function(){r.focus(),r.execCommand("FontName",!1,t.format)})}},setInitialValue:e,nodeChangeHandler:t,dataset:u,shouldHide:!1,isInvalid:function(){return!1}}},ME={"8pt":"1","10pt":"2","12pt":"3","14pt":"4","18pt":"5","24pt":"6","36pt":"7"},FE=function(t,n){return/[0-9.]+px$/.test(t)?(e=72*parseInt(t,10)/96,o=n||0,r=Math.pow(10,o),Math.round(e*r)/r+"pt"):t;var e,o,r},IE=function(e){var i=function(){var o=st.none(),r=u.data,i=e.queryCommandValue("FontSize");if(i)for(var t=function(t){var n=FE(i,t),e=Mt(ME,n).getOr("");o=L(r,function(t){return t.format===i||t.format===n||t.format===e})},n=3;o.isNone()&&0<=n;n--)t(n);return{matchOpt:o,size:i}},t=at(st.none),n=function(t){var n=i(),e=n.matchOpt,o=n.size,r=e.fold(function(){return o},function(t){return t.title});Go(t,mC,{text:r})},o=st.some(function(t){return function(){return n(t)}}),r=st.some(n),u=OE(e,"fontsize_formats","8pt 10pt 12pt 14pt 18pt 24pt 36pt",cO.Space);return{tooltip:"Font sizes",icon:st.none(),isSelectedFor:function(n){return function(t){return t.exists(function(t){return t.format===n})}},getPreviewFor:t,getCurrentValue:function(){return i().matchOpt},onAction:function(t){return function(){e.undoManager.transact(function(){e.focus(),e.execCommand("FontSize",!1,t.format)})}},setInitialValue:r,nodeChangeHandler:o,dataset:u,shouldHide:!1,isInvalid:function(){return!1}}},RE=function(e,t,n){var o=t();return Q(n,function(n){return L(o,function(t){return e.formatter.matchNode(n,t.format)})}).orThunk(function(){return e.formatter.match("p")?st.some({title:"Paragraph",format:"p"}):st.none()})},VE=function(t){var n=t.selection.getStart(!0)||t.getBody();return t.dom.getParents(n,function(){return!0},t.getBody())},PE=function(t){var n=se(st.none()),e=function(){return n.get().each(t)};return{clear:function(){e(),n.set(st.none())},isSet:function(){return n.get().isSome()},set:function(t){e(),n.set(st.some(t))}}},HE=function(){return PE(function(t){return t.unbind()})},zE=function(o,r){return function(n){var e=HE(),t=function(){n.setActive(o.formatter.match(r));var t=o.formatter.formatChanged(r,n.setActive);e.set(t)};return o.initialized?t():o.on("init",t),e.clear}},NE=function(n){return function(t){return function(){n.undoManager.transact(function(){n.focus(),n.execCommand("mceToggleFormat",!1,t.format)})}}},LE=function(o){var e=function(t,n){var e=RE(o,function(){return r.data},t).fold(function(){return"Paragraph"},function(t){return t.title});Go(n,mC,{text:e})},t=st.some(function(n){return function(t){return e(t.parents,n)}}),n=st.some(function(t){var n=VE(o);e(n,t)}),r=OE(o,"block_formats","Paragraph=p;Heading 1=h1;Heading 2=h2;Heading 3=h3;Heading 4=h4;Heading 5=h5;Heading 6=h6;Preformatted=pre",cO.SemiColon);return{tooltip:"Blocks",icon:st.none(),isSelectedFor:function(t){return function(){return o.formatter.match(t)}},getCurrentValue:st.none,getPreviewFor:function(n){return function(){var t=o.formatter.get(n);return st.some({tag:0<t.length&&(t[0].inline||t[0].block)||"div",styles:o.dom.parseStyle(o.formatter.getCssText(n))})}},onAction:NE(o),setInitialValue:n,nodeChangeHandler:t,dataset:r,shouldHide:!1,isInvalid:function(t){return!o.formatter.canApply(t.format)}}},jE=function(i,t){var e=function(t,n){var e=function(t){var n=t.items;return n!==undefined&&0<n.length?U(n,e):[{title:t.title,format:t.format}]},o=U(t_(i),e),r=RE(i,function(){return o},t).fold(function(){return"Paragraph"},function(t){return t.title});Go(n,mC,{text:r})},n=st.some(function(n){return function(t){return e(t.parents,n)}}),o=st.some(function(t){var n=VE(i);e(n,t)});return{tooltip:"Formats",icon:st.none(),isSelectedFor:function(t){return function(){return i.formatter.match(t)}},getCurrentValue:st.none,getPreviewFor:function(n){return function(){var t=i.formatter.get(n);return t!==undefined?st.some({tag:0<t.length&&(t[0].inline||t[0].block)||"div",styles:i.dom.parseStyle(i.formatter.getCssText(n))}):st.none()}},onAction:NE(i),setInitialValue:o,nodeChangeHandler:n,shouldHide:i.getParam("style_formats_autohide",!1,"boolean"),isInvalid:function(t){return!i.formatter.canApply(t.format)},dataset:t}},UE=[{name:"history",items:["undo","redo"]},{name:"styles",items:["styleselect"]},{name:"formatting",items:["bold","italic"]},{name:"alignment",items:["alignleft","aligncenter","alignright","alignjustify"]},{name:"indentation",items:["outdent","indent"]},{name:"permanent pen",items:["permanentpen"]},{name:"comments",items:["addcomment"]}],WE=function(r,i){return function(t,n,e){var o=r(t).mapError(function(t){return En(t)}).getOrDie();return i(o,n,e)}},GE={button:WE(Np,function(t,n){return e=t,o=n.backstage.shared.providers,nE(e,o,[]);var e,o}),togglebutton:WE(Up,function(t,n){return e=t,o=n.backstage.shared.providers,eE(e,o,[]);var e,o}),menubutton:WE(VT,function(t,n){return kC(t,"tox-tbtn",n.backstage,st.none())}),splitbutton:WE(function(t){return On("SplitButton",PT,t)},function(t,n){return oE(t,n.backstage.shared)}),grouptoolbarbutton:WE(function(t){return On("GroupToolbarButton",FT,t)},function(t,n,e){var o,r,i,u,a,c,s=e.ui.registry.getAll().buttons,l=((o={})[Hc]=n.backstage.shared.header.isPositionedAtTop()?$u.TopToBottom:$u.BottomToTop,o);switch(gv(e)){case Zp.floating:return r=t,i=n.backstage,u=function(t){return KE(e,{buttons:s,toolbar:t,allowToolbarGroups:!1},n,st.none())},a=l,c=i.shared,$_.sketch({lazySink:c.getSink,fetch:function(){return ix(function(t){t(V(u(r.items),TT))})},markers:{toggledClass:"tox-tbtn--enabled"},parts:{button:ZT(r.icon,r.text,r.tooltip,st.none(),st.none(),c.providers),toolbar:{dom:{tag:"div",classes:["tox-toolbar__overflow"],attributes:a}}}});default:throw new Error("Toolbar groups are only supported when using floating toolbar mode")}}),styleSelectButton:function(t,n){return e=t,o=n.backstage,r=nt({type:"advanced"},o.styleselect),bE(e,o,jE(e,r));var e,o,r},fontsizeSelectButton:function(t,n){return e=t,o=n.backstage,bE(e,o,IE(e));var e,o},fontSelectButton:function(t,n){return e=t,o=n.backstage,bE(e,o,AE(e));var e,o},formatButton:function(t,n){return e=t,o=n.backstage,bE(e,o,LE(e));var e,o},alignMenuButton:function(t,n){return e=t,o=n.backstage,bE(e,o,TE(e));var e,o}},XE={styleselect:GE.styleSelectButton,fontsizeselect:GE.fontsizeSelectButton,fontselect:GE.fontSelectButton,formatselect:GE.formatButton,align:GE.alignMenuButton},YE=function(t){var n,e,o,r=t.toolbar,i=t.buttons;return!1===r?[]:r===undefined||!0===r?(e=i,o=V(UE,function(t){var n=H(t.items,function(t){return Ft(e,t)||Ft(XE,t)});return{name:t.name,items:n}}),H(o,function(t){return 0<t.items.length})):S(r)?(n=r.split("|"),V(n,function(t){return{items:t.trim().split(" ")}})):T(r,function(t){return Ft(t,"name")&&Ft(t,"items")})?r:(console.error("Toolbar type should be string, string[], boolean or ToolbarGroup[]"),[])},qE=function(r,n,i,u,a,t){return Mt(n,i.toLowerCase()).orThunk(function(){return t.bind(function(t){return Q(t,function(t){return Mt(n,t+i.toLowerCase())})})}).fold(function(){return Mt(XE,i.toLowerCase()).map(function(t){return t(r,a)}).orThunk(function(){return st.none()})},function(t){return"grouptoolbarbutton"!==t.type||u?(e=a,o=r,Mt(GE,(n=t).type).fold(function(){return console.error("skipping button defined by",n),st.none()},function(t){return st.some(t(n,e,o))})):(console.warn("Ignoring the '"+i+"' toolbar button. Group toolbar buttons are only supported when using floating toolbar mode and cannot be nested."),st.none());var n,e,o})},KE=function(e,o,r,i){var t=YE(o),n=V(t,function(t){var n=U(t.items,function(t){return 0===t.trim().length?[]:qE(e,o.buttons,t,o.allowToolbarGroups,r,i).toArray()});return{title:st.from(e.translate(t.name)),items:n}});return H(n,function(t){return 0<t.items.length})},JE={valignCentre:[],alignCentre:[],alignLeft:["tox-pop--align-left"],alignRight:["tox-pop--align-right"],right:["tox-pop--right"],left:["tox-pop--left"],bottom:["tox-pop--bottom"],top:["tox-pop--top"]},$E={maxHeightFunction:Bc(),maxWidthFunction:M_()},QE={onLtr:function(){return[Ha,za,Va,Ia,Pa,Ra,ip,up,op,np,rp,ep]},onRtl:function(){return[Ha,za,Pa,Ra,Va,Ia,ip,up,rp,ep,op,np]}},ZE={onLtr:function(){return[za,Ia,Ra,Va,Pa,Ha,ip,up,op,np,rp,ep]},onRtl:function(){return[za,Ra,Ia,Pa,Va,Ha,ip,up,rp,ep,op,np]}},tB=function(c,t,e,s){var o,r,l=ze().deviceType.isTouch,a=lu((o={sink:e,onEscape:function(){return c.focus(),st.some(!0)}},r=se([]),Kg.sketch({dom:{tag:"div",classes:["tox-pop"]},fireDismissalEventInstead:{event:"doNotDismissYet"},onShow:function(t){r.set([]),Kg.getContent(t).each(function(t){$i(t.element,"visibility")}),Ti(t.element,hE),$i(t.element,"width")},inlineBehaviours:nc([ag("context-toolbar-events",[ir(po(),function(t,n){Ti(t.element,hE),$i(t.element,"width")}),Zo(pE,function(n,e){$i(n.element,"width");var t=Su(n.element);Kg.setContent(n,e.event.contents),Oi(n.element,hE);var o=Su(n.element);Wi(n.element,"width",t+"px"),Kg.getContent(n).each(function(t){e.event.focus.bind(function(t){return sc(t),fc(n.element)}).orThunk(function(){return ng.focusIn(t),lc()})}),ap.setTimeout(function(){Wi(n.element,"width",o+"px")},0)}),Zo(mE,function(t,n){Kg.getContent(t).each(function(t){r.set(r.get().concat([{bar:t,focus:lc()}]))}),Go(t,pE,{contents:n.event.forwardContents,focus:st.none()})}),Zo(gE,function(n,t){K(r.get()).each(function(t){r.set(r.get().slice(0,r.get().length-1)),Go(n,pE,{contents:fu(t.bar),focus:t.focus})})})]),ng.config({mode:"special",onEscape:function(n){return K(r.get()).fold(function(){return o.onEscape()},function(t){return Wo(n,gE),st.some(!0)})}})]),lazySink:function(){return it.value(o.sink)}}))),f=function(){return cE(c,s.backstage.shared)},u=function(){if(l()&&s.backstage.isContextMenuOpen())return!0;var t,n,e,o,r,i,u=(t=m.get().filter(function(t){return zi(fe.fromDom(t))}).map(function(t){return t.getBoundingClientRect()}).getOrThunk(function(){return c.selection.getRng().getBoundingClientRect()}),n=c.inline?Tu().top:Vu(fe.fromDom(c.getBody())).y,{y:t.top+n,bottom:t.bottom+n}),a=f();return e=u.y,o=u.bottom,r=a.y,i=a.bottom,!(Math.max(e,r)<=Math.min(o,i))},n=function(){Kg.hide(a)},i=function(){d.get().each(function(t){var n=a.element;$i(n,"display"),u()?Wi(n,"display","none"):qs.positionWithinBounds(e,t,a,st.some(f()))})},d=se(st.none()),m=se(st.none()),g=se(null),p=function(t){return{dom:{tag:"div",classes:["tox-pop__dialog"]},components:[t],behaviours:nc([ng.config({mode:"acyclic"}),ag("pop-dialog-wrap-events",[ur(function(t){c.shortcuts.add("ctrl+F9","focus statusbar",function(){return ng.focusIn(t)})}),ar(function(t){c.shortcuts.remove("ctrl+F9")})])])}},h=Nt(function(){return dE(t,function(t){var n=v([t]);Go(a,mE,{forwardContents:p(n)})})}),v=function(t){var n=c.ui.registry.getAll().buttons,e=h(),o=nt(nt({},n),e.formNavigators),r=gv(c)===Zp.scrolling?Zp.scrolling:Zp["default"],i=rt(V(t,function(t){return"contexttoolbar"===t.type?KE(c,{buttons:o,toolbar:t.items,allowToolbarGroups:!1},s,st.some(["form:"])):(n=t,e=s.backstage.shared.providers,aE(n,e));var n,e}));return MT({type:r,uid:Xr("context-toolbar"),initGroups:i,onEscape:st.none,cyclicKeying:!0,providers:s.backstage.shared.providers})};c.on("contexttoolbar-show",function(n){var t=h();Mt(t.lookupTable,n.toolbarKey).each(function(t){y([t],n.target===c?st.none():st.some(n)),Kg.getContent(a).each(ng.focusIn)})});var b=function(t,n){var e,o,r="node"===t?s.backstage.shared.anchors.node(n):s.backstage.shared.anchors.cursor();return Ht(r,(e=t,o=l(),"line"===e?{bubble:Ic(12,0,JE),layouts:{onLtr:function(){return[Na]},onRtl:function(){return[La]}},overrides:$E}:{bubble:Ic(0,12,JE),layouts:o?ZE:QE,overrides:$E}))},y=function(t,n){var e,o,r,i;w(),l()&&s.backstage.isContextMenuOpen()||(e=v(t),o=n.map(fe.fromDom),r=b(t[0].position,o),d.set(st.some(r)),m.set(n),i=a.element,$i(i,"display"),Kg.showWithinBounds(a,r,p(e),function(){return st.some(f())}),u()&&Wi(i,"display","none"))},x=function(){var t,n,e,o,r,i,u;c.hasFocus()&&(t=h(),n=t,e=c,r=fe.fromDom(e.getBody()),i=function(t){return je(t,r)},u=fe.fromDom(e.selection.getNode()),(i(o=u)||We(r,o)?lE(u,n.inNodeScope,n.inEditorScope).orThunk(function(){return fE(i,u,n)}):st.none()).fold(function(){d.set(st.none()),Kg.hide(a)},function(t){y(t.toolbars,st.some(t.elem.dom))}))},w=function(){var t=g.get();null!==t&&(ap.clearTimeout(t),g.set(null))},S=function(t){w(),g.set(t)};c.on("init",function(){c.on(F_,n),c.on("ScrollContent ScrollWindow longpress",i),c.on("click keyup focus SetContent ObjectResized ResizeEditor",function(){S(ap.setEditorTimeout(c,x,0))}),c.on("focusout",function(t){ap.setEditorTimeout(c,function(){fc(e.element).isNone()&&fc(a.element).isNone()&&(d.set(st.none()),Kg.hide(a))},0)}),c.on("SwitchMode",function(){c.mode.isReadOnly()&&(d.set(st.none()),Kg.hide(a))}),c.on("NodeChange",function(t){fc(a.element).fold(function(){S(ap.setEditorTimeout(c,x,0))},function(t){})})})},nB=Rf,eB=bf,oB=at([te("shell",!1),Nn("makeItem"),te("setupItem",$),Nl("listBehaviours",[ug])]),rB=pf({name:"items",overrides:function(){return{behaviours:nc([ug.config({})])}}}),iB=at([rB]),uB=Wf({name:at("CustomList")(),configFields:oB(),partFields:iB(),factory:function(s,t,n,e){var o=s.shell?{behaviours:[ug.config({})],components:[]}:{behaviours:[],components:t},r=function(t){return s.shell?st.some(t):Tf(t,s,"items")};return{uid:s.uid,dom:s.dom,components:o.components,behaviours:zl(s.listBehaviours,o.behaviours),apis:{setItems:function(a,c){r(a).fold(function(){throw console.error("Custom List was defined to not be a shell, but no item container was specified in components"),new Error("Custom List was defined to not be a shell, but no item container was specified in components")},function(n){var t=ug.contents(n),e=c.length,o=e-t.length,r=0<o?I(o,function(){return s.makeItem()}):[],i=t.slice(e);ot(i,function(t){return ug.remove(n,t)}),ot(r,function(t){return ug.append(n,t)});var u=ug.contents(n);ot(u,function(t,n){s.setupItem(a,t,c[n],n)})})}}}},apis:{setItems:function(t,n,e){t.setItems(n,e)}}}),aB=$,cB=c,sB=at([]),lB=/* */Object.freeze({__proto__:null,setup:aB,isDocked:cB,getBehaviours:sB}),fB=function(t){return(Ki(t,"position").is("fixed")?st.none():Sr(t)).orThunk(function(){var e=fe.fromTag("span");return xr(t).bind(function(t){Er(t,e);var n=Sr(e);return Ar(e),n})})},dB=function(t){return fB(t).map(yu).getOrThunk(function(){return vu(0,0)})},mB=Rt([{"static":[]},{absolute:["positionCss"]},{fixed:["positionCss"]}]),gB=function(t,n){var e=t.element;Oi(e,n.transitionClass),Ti(e,n.fadeOutClass),Oi(e,n.fadeInClass),n.onShow(t)},pB=function(t,n){var e=t.element;Oi(e,n.transitionClass),Ti(e,n.fadeInClass),Oi(e,n.fadeOutClass),n.onHide(t)},hB=function(t,o,r){return W(t,function(t){switch(t){case"bottom":return e=r,o.bottom<=e.bottom;case"top":return n=r,o.y>=n.y}var n,e})},vB=function(n,t){return t.getInitialPosition().map(function(t){return Iu(t.bounds.x,t.bounds.y,Su(n),gu(n))})},bB=function(t,n,e){e.setInitialPosition(st.some({style:function(t){var n={},e=t.dom;if(Ai(e))for(var o=0;o<e.style.length;o++){var r=e.style.item(o);n[r]=e.style[r]}return n}(t),position:Yi(t,"position")||"static",bounds:n}))},yB=function(e,o,r){return r.getInitialPosition().bind(function(t){switch(r.setInitialPosition(st.none()),t.position){case"static":return st.some(mB["static"]());case"absolute":var n=fB(e).map(Ru).getOrThunk(function(){return Ru(Ni())});return st.some(mB.absolute(mc("absolute",Mt(t.style,"left").map(function(t){return o.x-n.x}),Mt(t.style,"top").map(function(t){return o.y-n.y}),Mt(t.style,"right").map(function(t){return n.right-o.right}),Mt(t.style,"bottom").map(function(t){return n.bottom-o.bottom}))));default:return st.none()}})},xB=function(t,n,e){var o,r,i,u=t.element;return Ki(u,"position").is("fixed")?(r=n,vB(o=u,i=e).filter(function(t){return hB(i.getModes(),t,r)}).bind(function(t){return yB(o,t,i)})):function(t,n,e){var o=Ru(t);if(hB(e.getModes(),o,n))return st.none();bB(t,o,e);var r=Pu(),i=o.x-r.x,u=n.y-r.y,a=r.bottom-n.bottom,c=o.y<=n.y;return st.some(mB.fixed(mc("fixed",st.some(i),c?st.some(u):st.none(),st.none(),c?st.none():st.some(a))))}(u,n,e)},wB=function(n,t){ot(["left","right","top","bottom","position"],function(t){return $i(n.element,t)}),t.onUndocked(n)},SB=function(t,n,e){gc(t.element,e),("fixed"===e.position?n.onDocked:n.onUndocked)(t)},kB=function(i,t,u,a,c){void 0===c&&(c=!1),t.contextual.each(function(r){r.lazyContext(i).each(function(t){var n,e,o=(e=a,(n=t).y<e.bottom&&n.bottom>e.y);o!==u.isVisible()&&(u.setVisible(o),c&&!o?(Bi(i.element,[r.fadeOutClass]),r.onHide(i)):(o?gB:pB)(i,r))})})},CB=function(n,e,t){var o,r,i=n.element;t.setDocked(!1),o=t,r=n.element,vB(r,o).bind(function(t){return yB(r,t,o)}).each(function(t){t.fold(function(){return wB(n,e)},function(t){return SB(n,e,t)},$)}),t.setVisible(!0),e.contextual.each(function(t){Di(i,[t.fadeInClass,t.fadeOutClass,t.transitionClass]),t.onShow(n)}),OB(n,e,t)},OB=function(t,n,e){var o,r,i,u,a;t.getSystem().isConnected()&&(o=t,i=e,u=(r=n).lazyViewport(o),(a=i.isDocked())&&kB(o,r,i,u),xB(o,u,i).each(function(t){i.setDocked(!a),t.fold(function(){return wB(o,r)},function(t){return SB(o,r,t)},function(t){kB(o,r,i,u,!0),SB(o,r,t)})}))},_B=function(t,n,e){e.isDocked()&&CB(t,n,e)},TB=/* */Object.freeze({__proto__:null,refresh:OB,reset:_B,isDocked:function(t,n,e){return e.isDocked()},getModes:function(t,n,e){return e.getModes()},setModes:function(t,n,e,o){return e.setModes(o)}}),EB=/* */Object.freeze({__proto__:null,events:function(o,r){return Jo([ir(po(),function(n,e){o.contextual.each(function(t){Ei(n.element,t.transitionClass)&&(Di(n.element,[t.transitionClass,t.fadeInClass]),(r.isVisible()?t.onShown:t.onHidden)(n)),e.stop()})}),Zo(Mo(),function(t,n){OB(t,o,r)}),Zo(Fo(),function(t,n){_B(t,o,r)})])}}),BB=[Zn("contextual",[jn("fadeInClass"),jn("fadeOutClass"),jn("transitionClass"),Wn("lazyContext"),la("onShow"),la("onShown"),la("onHide"),la("onHidden")]),ue("lazyViewport",Pu),ne("modes",["top","bottom"],fn(In)),la("onDocked"),la("onUndocked")],DB=oc({fields:BB,name:"docking",active:EB,apis:TB,state:/* */Object.freeze({__proto__:null,init:function(t){var n=se(!1),e=se(!0),o=se(st.none()),r=se(t.modes);return si({isDocked:n.get,setDocked:n.set,getInitialPosition:o.get,setInitialPosition:o.set,isVisible:e.get,setVisible:e.set,getModes:r.get,setModes:r.set,readState:function(){return"docked:  "+n.get()+", visible: "+e.get()+", modes: "+r.get().join(",")}})}})}),AB={fadeInClass:"tox-editor-dock-fadein",fadeOutClass:"tox-editor-dock-fadeout",transitionClass:"tox-editor-dock-transition"},MB="tox-tinymce--toolbar-sticky-on",FB="tox-tinymce--toolbar-sticky-off",IB=function(r){var i=r.element;xr(i).each(function(t){var n,e,o="padding-"+DB.getModes(r)[0];DB.isDocked(r)?(n=Su(t),Wi(i,"width",n+"px"),Wi(t,o,pu(e=i)+(parseInt(Yi(e,"margin-top"),10)||0)+(parseInt(Yi(e,"margin-bottom"),10)||0)+"px")):($i(i,"width"),$i(t,o))})},RB=function(t,n){n?(Ti(t,AB.fadeOutClass),Bi(t,[AB.transitionClass,AB.fadeInClass])):(Ti(t,AB.fadeInClass),Bi(t,[AB.fadeOutClass,AB.transitionClass]))},VB=function(t,n){var e=fe.fromDom(t.getContainer());n?(Oi(e,MB),Ti(e,FB)):(Oi(e,FB),Ti(e,MB))},PB=function(c,t){var n,i=se(st.none()),o=t.getSink,u=function(n){o().each(function(t){return n(t.element)})},e=function(t){c.inline||IB(t),VB(c,DB.isDocked(t)),t.getSystem().broadcastOn([bl()],{}),o().each(function(t){return t.getSystem().broadcastOn([bl()],{})})},r=c.inline?[]:[ac.config({channels:((n={})[OT()]={onReceive:IB},n)})];return w([dg.config({}),DB.config({contextual:nt({lazyContext:function(t){var n,e,o=pu(t.element),r=c.inline?c.getContentAreaContainer():c.getContainer(),i=Ru(fe.fromDom(r)),u=i.height-o,a=i.y+(n=t,e="top",M(DB.getModes(n),e)?0:o);return st.some(Iu(i.x,a,i.width,u))},onShow:function(){u(function(t){return RB(t,!0)})},onShown:function(r){u(function(t){return Di(t,[AB.transitionClass,AB.fadeInClass])}),i.get().each(function(t){var n,e,o;n=r.element,o=hr(e=t),lc(o).filter(function(t){return!je(e,t)}).filter(function(t){return je(t,fe.fromDom(o.dom.body))||We(n,t)}).each(function(){return sc(e)}),i.set(st.none())})},onHide:function(t){var n,e;i.set((n=t.element,e=o,fc(n).orThunk(function(){return e().toOptional().bind(function(t){return fc(t.element)})}))),u(function(t){return RB(t,!1)})},onHidden:function(){u(function(t){return Di(t,[AB.transitionClass])})}},AB),modes:[t.header.getDockingMode()],onDocked:e,onUndocked:e})],r)},HB=/* */Object.freeze({__proto__:null,setup:function(t,n,e){t.inline||(n.header.isPositionedAtTop()||t.on("ResizeEditor",function(){e().each(DB.reset)}),t.on("ResizeWindow ResizeEditor",function(){e().each(IB)}),t.on("SkinLoaded",function(){e().each(function(t){DB.isDocked(t)?DB.reset(t):DB.refresh(t)})}),t.on("FullscreenStateChanged",function(){e().each(DB.reset)})),t.on("AfterScrollIntoView",function(y){e().each(function(t){DB.refresh(t);var n,e,o,r,i,u,a,c,s,l,f,d,m,g,p,h,v,b=t.element;Kd(b)&&(n=y,r=hr(e=b),i=r.dom.defaultView.innerHeight,u=Tu(r),a=fe.fromDom(n.elm),c=Vu(a),s=gu(a),l=c.y,f=l+s,d=yu(e),m=gu(e),g=d.top,p=g+m,h=Math.abs(g-u.top)<2,v=Math.abs(p-(u.top+i))<2,h&&l<p?Eu(u.left,l-m,r):v&&g<f&&(o=l-i+s+m,Eu(u.left,o,r)))})}),t.on("PostRender",function(){VB(t,!1)})},isDocked:function(t){return t().map(DB.isDocked).getOr(!1)},getBehaviours:PB}),zB=Uf({factory:function(n,o){var t={focus:ng.focusIn,setMenus:function(t,n){var e=V(n,function(n){var t={type:"menubutton",text:n.text,fetch:function(t){t(n.getItems())}},e=VT(t).mapError(function(t){return En(t)}).getOrDie();return kC(e,"tox-mbtn",o.backstage,st.some("menuitem"))});ug.set(t,e)}};return{uid:n.uid,dom:n.dom,components:[],behaviours:nc([ug.config({}),ag("menubar-events",[ur(function(t){n.onSetup(t)}),Zo(uo(),function(e,t){Gu(e.element,".tox-mbtn--active").each(function(n){Xu(t.event.target,".tox-mbtn").each(function(t){je(n,t)||e.getSystem().getByDom(n).each(function(n){e.getSystem().getByDom(t).each(function(t){Ix.expand(t),Ix.close(n),dg.focus(t)})})})})}),Zo(Ho(),function(e,t){t.event.prevFocus.bind(function(t){return e.getSystem().getByDom(t).toOptional()}).each(function(n){t.event.newFocus.bind(function(t){return e.getSystem().getByDom(t).toOptional()}).each(function(t){Ix.isOpen(n)&&(Ix.expand(t),Ix.close(n))})})})]),ng.config({mode:"flow",selector:".tox-mbtn",onEscape:function(t){return n.onEscape(t),st.some(!0)}}),Ay.config({})]),apis:t,domModification:{attributes:{role:"menubar"}}}},name:"silver.Menubar",configFields:[Nn("dom"),Nn("uid"),Nn("onEscape"),Nn("backstage"),te("onSetup",$)],apis:{focus:function(t,n){t.focus(n)},setMenus:function(t,n,e){t.setMenus(n,e)}}}),NB="container",LB=[Pl("slotBehaviours",[])],jB=function(t){return"<alloy.field."+t+">"},UB=function(r,t){var e,n=function(t){return Af(r)},o=function(e,o){return function(t,n){return Tf(t,r,n).map(function(t){return e(t,n)}).getOr(o)}},i=function(t,n){return"true"!==Pr(t.element,"aria-hidden")},u=o(i,!1),a=o(function(t,n){var e;i(t)&&(e=t.element,Wi(e,"display","none"),Vr(e,"aria-hidden","true"),Go(t,zo(),{name:n,visible:!1}))}),c=function(n,t){ot(t,function(t){return e(n,t)})},s=o(function(t,n){var e;i(t)||(e=t.element,$i(e,"display"),Nr(e,"aria-hidden"),Go(t,zo(),{name:n,visible:!0}))}),l={getSlotNames:n,getSlot:function(t,n){return Tf(t,r,n)},isShowing:u,hideSlot:e=a,hideAllSlots:function(t){return c(t,n())},showSlot:s};return{uid:r.uid,dom:r.dom,components:t,behaviours:Hl(r.slotBehaviours),apis:l}},WB=_t({getSlotNames:function(t,n){return t.getSlotNames(n)},getSlot:function(t,n,e){return t.getSlot(n,e)},isShowing:function(t,n,e){return t.isShowing(n,e)},hideSlot:function(t,n,e){return t.hideSlot(n,e)},hideAllSlots:function(t,n){return t.hideAllSlots(n)},showSlot:function(t,n,e){return t.showSlot(n,e)}},ai),GB=nt(nt({},WB),{sketch:function(t){var e,n=(e=[],{slot:function(t,n){return e.push(t),Sf(NB,jB(t),n)},record:function(){return e}}),o=t(n),r=n.record(),i=V(r,function(t){return mf({name:t,pname:jB(t)})});return Pf(NB,LB,i,UB,o)}}),XB=ln([$n("icon"),$n("tooltip"),ue("onShow",$),ue("onHide",$),ue("onSetup",function(){return $})]),YB=function(t){return{element:function(){return t.element.dom}}},qB=function(e,o){var r=V(kt(o),function(t){var n=o[t],e=_n(On("sidebar",XB,n));return{name:t,getApi:YB,onSetup:e.onSetup,onShow:e.onShow,onHide:e.onHide}});return V(r,function(t){var n=se($);return e.slot(t.name,{dom:{tag:"div",classes:["tox-sidebar__pane"]},behaviours:Dh([Mv(t,n),Fv(t,n),Zo(zo(),function(n,t){var e=t.event;L(r,function(t){return t.name===e.name}).each(function(t){(e.visible?t.onShow:t.onHide)(t.getApi(n))})})])})})},KB=function(t,e){Kf.getCurrent(t).each(function(t){return ug.set(t,[(n=e,GB.sketch(function(t){return{dom:{tag:"div",classes:["tox-sidebar__pane-container"]},components:qB(t,n),slotBehaviours:Dh([ur(function(t){return GB.hideAllSlots(t)})])}}))]);var n})},JB=function(t){return Kf.getCurrent(t).bind(function(t){return yT.isGrowing(t)||yT.hasGrown(t)?Kf.getCurrent(t).bind(function(n){return L(GB.getSlotNames(n),function(t){return GB.isShowing(n,t)})}):st.none()})},$B=Xr("FixSizeEvent"),QB=Xr("AutoSizeEvent"),ZB=function(t){var n,e,o,r=fe.fromHtml(t),i=kr(r),u=(e=(n=r).dom.attributes!==undefined?n.dom.attributes:[],N(e,function(t,n){var e;return"class"===n.name?t:nt(nt({},t),((e={})[n.name]=n.value,e))},{})),a=(o=r,Array.prototype.slice.call(o.dom.classList,0)),c=0===i.length?{}:{innerHtml:Fr(r)};return nt({tag:lr(r),classes:a,attributes:u},c)},tD=function(t,n,e){var o=t.element;!0===n?(ug.set(t,[{dom:{tag:"div",attributes:{"aria-label":e.translate("Loading...")},classes:["tox-throbber__busy-spinner"]},components:[{dom:ZB('<div class="tox-spinner"><div></div><div></div><div></div></div>')}],behaviours:nc([ng.config({mode:"special",onTab:function(){return st.some(!0)},onShiftTab:function(){return st.some(!0)}}),dg.config({})])}]),$i(o,"display"),Nr(o,"aria-hidden")):(ug.set(t,[]),Wi(o,"display","none"),Vr(o,"aria-hidden","true"))},nD=eB.optional({factory:zB,name:"menubar",schema:[Nn("backstage")]}),eD=eB.optional({factory:{sketch:function(t){return uB.sketch({uid:t.uid,dom:t.dom,listBehaviours:nc([ng.config({mode:"acyclic",selector:".tox-toolbar"})]),makeItem:function(){return MT({type:t.type,uid:Xr("multiple-toolbar-item"),cyclicKeying:!1,initGroups:[],providers:t.providers,onEscape:function(){return t.onEscape(),st.some(!0)}})},setupItem:function(t,n,e,o){V_.setGroups(n,e)},shell:!0})}},name:"multiple-toolbar",schema:[Nn("dom"),Nn("onEscape")]}),oD=eB.optional({factory:{sketch:function(t){var n;return((n=t).type===Zp.sliding?AT:n.type===Zp.floating?DT:MT)({type:t.type,uid:t.uid,onEscape:function(){return t.onEscape(),st.some(!0)},cyclicKeying:!1,initGroups:[],getSink:t.getSink,providers:t.providers,moreDrawerData:{lazyToolbar:t.lazyToolbar,lazyMoreButton:t.lazyMoreButton,lazyHeader:t.lazyHeader},attributes:t.attributes})}},name:"toolbar",schema:[Nn("dom"),Nn("onEscape"),Nn("getSink")]}),rD=eB.optional({factory:{sketch:function(t){var n=t.editor,e=t.sticky?PB:sB;return{uid:t.uid,dom:t.dom,components:t.components,behaviours:nc(e(n,t.sharedBackstage))}}},name:"header",schema:[Nn("dom")]}),iD=eB.optional({name:"socket",schema:[Nn("dom")]}),uD=eB.optional({factory:{sketch:function(t){return{uid:t.uid,dom:{tag:"div",classes:["tox-sidebar"],attributes:{role:"complementary"}},components:[{dom:{tag:"div",classes:["tox-sidebar__slider"]},components:[],behaviours:nc([Ay.config({}),dg.config({}),yT.config({dimension:{property:"width"},closedClass:"tox-sidebar--sliding-closed",openClass:"tox-sidebar--sliding-open",shrinkingClass:"tox-sidebar--sliding-shrinking",growingClass:"tox-sidebar--sliding-growing",onShrunk:function(t){Kf.getCurrent(t).each(GB.hideAllSlots),Wo(t,QB)},onGrown:function(t){Wo(t,QB)},onStartGrow:function(t){Go(t,$B,{width:Ki(t.element,"width").getOr("")})},onStartShrink:function(t){Go(t,$B,{width:Su(t.element)+"px"})}}),ug.config({}),Kf.config({find:function(t){var n=ug.contents(t);return q(n)}})])}],behaviours:nc([HS(0),ag("sidebar-sliding-events",[Zo($B,function(t,n){Wi(t.element,"width",n.event.width)}),Zo(QB,function(t,n){$i(t.element,"width")})])])}}},name:"sidebar",schema:[Nn("dom")]}),aD=eB.optional({factory:{sketch:function(t){return{uid:t.uid,dom:{tag:"div",attributes:{"aria-hidden":"true"},classes:["tox-throbber"],styles:{display:"none"}},behaviours:nc([ug.config({})]),components:[]}}},name:"throbber",schema:[Nn("dom")]}),cD=Wf({name:"OuterContainer",factory:function(e,t,n){var o={getSocket:function(t){return nB.getPart(t,e,"socket")},setSidebar:function(t,n){nB.getPart(t,e,"sidebar").each(function(t){return KB(t,n)})},toggleSidebar:function(t,o){nB.getPart(t,e,"sidebar").each(function(t){return n=t,e=o,void Kf.getCurrent(n).each(function(n){Kf.getCurrent(n).each(function(t){yT.hasGrown(n)?GB.isShowing(t,e)?yT.shrink(n):(GB.hideAllSlots(t),GB.showSlot(t,e)):(GB.hideAllSlots(t),GB.showSlot(t,e),yT.grow(n))})});var n,e})},whichSidebar:function(t){return nB.getPart(t,e,"sidebar").bind(JB).getOrNull()},getHeader:function(t){return nB.getPart(t,e,"header")},getToolbar:function(t){return nB.getPart(t,e,"toolbar")},setToolbar:function(t,n){nB.getPart(t,e,"toolbar").each(function(t){t.getApis().setGroups(t,n)})},setToolbars:function(t,n){nB.getPart(t,e,"multiple-toolbar").each(function(t){uB.setItems(t,n)})},refreshToolbar:function(t){nB.getPart(t,e,"toolbar").each(function(t){return t.getApis().refresh(t)})},toggleToolbarDrawer:function(t){nB.getPart(t,e,"toolbar").each(function(n){var t,e;t=n.getApis().toggle,e=function(t){return t(n)},t!==undefined&&null!==t?st.some(e(t)):st.none()})},isToolbarDrawerToggled:function(t){return nB.getPart(t,e,"toolbar").bind(function(n){return st.from(n.getApis().isOpen).map(function(t){return t(n)})}).getOr(!1)},getThrobber:function(t){return nB.getPart(t,e,"throbber")},focusToolbar:function(t){nB.getPart(t,e,"toolbar").orThunk(function(){return nB.getPart(t,e,"multiple-toolbar")}).each(function(t){ng.focusIn(t)})},setMenubar:function(t,n){nB.getPart(t,e,"menubar").each(function(t){zB.setMenus(t,n)})},focusMenubar:function(t){nB.getPart(t,e,"menubar").each(function(t){zB.focus(t)})}};return{uid:e.uid,dom:e.dom,components:t,apis:o,behaviours:e.behaviours}},configFields:[Nn("dom"),Nn("behaviours")],partFields:[rD,nD,oD,eD,iD,uD,aD],apis:{getSocket:function(t,n){return t.getSocket(n)},setSidebar:function(t,n,e){t.setSidebar(n,e)},toggleSidebar:function(t,n,e){t.toggleSidebar(n,e)},whichSidebar:function(t,n){return t.whichSidebar(n)},getHeader:function(t,n){return t.getHeader(n)},getToolbar:function(t,n){return t.getToolbar(n)},setToolbar:function(t,n,e){var o=V(e,TT);t.setToolbar(n,o)},setToolbars:function(t,n,e){var o=V(e,function(t){return V(t,TT)});t.setToolbars(n,o)},refreshToolbar:function(t,n){return t.refreshToolbar(n)},toggleToolbarDrawer:function(t,n){t.toggleToolbarDrawer(n)},isToolbarDrawerToggled:function(t,n){return t.isToolbarDrawerToggled(n)},getThrobber:function(t,n){return t.getThrobber(n)},setMenubar:function(t,n,e){t.setMenubar(n,e)},focusMenubar:function(t,n){t.focusMenubar(n)},focusToolbar:function(t,n){t.focusToolbar(n)}}}),sD={file:{title:"File",items:"newdocument restoredraft | preview | export print | deleteallconversations"},edit:{title:"Edit",items:"undo redo | cut copy paste pastetext | selectall | searchreplace"},view:{title:"View",items:"code | visualaid visualchars visualblocks | spellchecker | preview fullscreen | showcomments"},insert:{title:"Insert",items:"image link media addcomment pageembed template codesample inserttable | charmap emoticons hr | pagebreak nonbreaking anchor toc | insertdatetime"},format:{title:"Format",items:"bold italic underline strikethrough superscript subscript codeformat | formats blockformats fontformats fontsizes align lineheight | forecolor backcolor | removeformat"},tools:{title:"Tools",items:"spellchecker spellcheckerlanguage | a11ycheck code wordcount"},table:{title:"Table",items:"inserttable | cell row column | advtablesort | tableprops deletetable"},help:{title:"Help",items:"help"}},lD=function(t){return"string"==typeof t?t.split(" "):t},fD=function(i,u){var a=nt(nt({},sD),u.menus),n=0<kt(u.menus).length,t=u.menubar===undefined||!0===u.menubar?lD("file edit view insert format tools table help"):lD(!1===u.menubar?"":u.menubar),e=H(t,function(t){return n&&u.menus.hasOwnProperty(t)&&u.menus[t].hasOwnProperty("items")||sD.hasOwnProperty(t)}),o=V(e,function(t){var n,e,o,r=a[t];return n={title:r.title,items:lD(r.items)},e=u,o=i.getParam("removed_menuitems","").split(/[ ,]/),{text:n.title,getItems:function(){return U(n.items,function(t){var n=t.toLowerCase();return 0===n.trim().length||F(o,function(t){return t===n})?[]:"separator"===n||"|"===n?[{type:"separator"}]:e.menuItems[n]?[e.menuItems[n]]:[]})}}});return H(o,function(t){return 0<t.getItems().length&&F(t.getItems(),function(t){return"separator"!==t.type})})},dD=function(t){var n=function(){t._skinLoaded=!0,t.fire("SkinLoaded")};return function(){t.initialized?n():t.on("init",n)}},mD=function(n,e){return function(){return t={message:e},n.fire("SkinLoadError",t);var t}},gD=function(t,n){var e,o,r,i,u,a,c=(r=(e=n).getParam("skin"),i=e.getParam("skin_url"),!1!==r&&(o=r||"oxide",i=i?e.documentBaseURI.toAbsolute(i):nv.baseURL+"/skins/ui/"+o),i);c&&(u=c+"/skin.min.css",n.contentCSS.push(c+(t?"/content.inline":"/content")+".min.css")),!1==(!1===n.getParam("skin"))&&u?((a=n.ui.styleSheetLoader).load(u,dD(n),mD(n,"Skin could not be loaded")),n.on("remove",function(){return a.unload(u)})):dD(n)()},pD=g(gD,!1),hD=g(gD,!0),vD=function(e,t,o,r){var n,i=t.outerContainer,u=o.toolbar,a=o.buttons;T(u,S)?(n=u.map(function(t){var n={toolbar:t,buttons:a,allowToolbarGroups:o.allowToolbarGroups};return KE(e,n,{backstage:r},st.none())}),cD.setToolbars(i,n)):cD.setToolbar(i,KE(e,o,{backstage:r},st.none()))},bD=zh.DOM,yD=ze(),xD=yD.os.isiOS()&&yD.os.version.major<=12,wD=function(e,t){var n=e.getWin(),o=e.getDoc().documentElement,r=se(vu(n.innerWidth,n.innerHeight)),i=se(vu(o.offsetWidth,o.offsetHeight)),u=function(){var t=r.get();t.left===n.innerWidth&&t.top===n.innerHeight||(r.set(vu(n.innerWidth,n.innerHeight)),pb(e))},a=function(){var t=e.getDoc().documentElement,n=i.get();n.left===t.offsetWidth&&n.top===t.offsetHeight||(i.set(vu(t.offsetWidth,t.offsetHeight)),pb(e))},c=function(t){return n=t,e.fire("ScrollContent",n);var n};bD.bind(n,"resize",u),bD.bind(n,"scroll",c);var s=ny(fe.fromDom(e.getBody()),"load",a),l=t.uiMothership.element;e.on("hide",function(){Wi(l,"display","none")}),e.on("show",function(){$i(l,"display")}),e.on("NodeChange",a),e.on("remove",function(){s.unbind(),bD.unbind(n,"resize",u),bD.unbind(n,"scroll",c)})},SD=/* */Object.freeze({__proto__:null,render:function(e,n,t,o,r){var i=se(0),u=n.outerContainer;pD(e);var a,c,s=fe.fromDom(r.targetNode),l=Vi(Ri(s));a=s,c=n.mothership,il(a,c,_r),rl(l,n.uiMothership),e.on("PostRender",function(){vD(e,n,t,o),i.set(e.getWin().innerWidth),cD.setMenubar(u,fD(e,t)),cD.setSidebar(u,t.sidebar),wD(e,n)});var f,d,m,g,p=cD.getSocket(u).getOrDie("Could not find expected socket element");xD&&(Gi(p.element,{overflow:"scroll","-webkit-overflow-scrolling":"touch"}),d=function(){e.fire("ScrollContent")},m=20,g=null,f={cancel:function(){null!==g&&(clearTimeout(g),g=null)},throttle:function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];null===g&&(g=setTimeout(function(){d.apply(null,t),g=null},m))}},ty(p.element,"scroll",f.throttle)),Ov(e,n),e.addCommand("ToggleSidebar",function(t,n){cD.toggleSidebar(u,n),e.fire("ToggleSidebar")}),e.addQueryValueHandler("ToggleSidebar",function(){return cD.whichSidebar(u)});var h=gv(e);return h!==Zp.sliding&&h!==Zp.floating||e.on("ResizeWindow ResizeEditor ResizeContent",function(){var t=e.getWin().innerWidth;t!==i.get()&&(cD.refreshToolbar(n.outerContainer),i.set(t))}),{iframeContainer:p.element.dom,editorContainer:u.element.dom}}}),kD=function(t){return/^[0-9\.]+(|px)$/i.test(""+t)?st.some(parseInt(""+t,10)):st.none()},CD=function(t){return et(t)?t+"px":t},OD=function(n,t,e){var o=t.filter(function(t){return n<t}),r=e.filter(function(t){return t<n});return o.or(r).getOr(n)},_D=function(t){var n,e,o,r;return(e=ev(n=t),o=iv(n),r=av(n),kD(e).map(function(t){return OD(t,o,r)})).getOr(ev(t))},TD=function(t){var n=ov(t),e=rv(t),o=uv(t);return kD(n).map(function(t){return OD(t,e,o)})},ED=function(a,c,t,n,s){var e=t.uiMothership,l=t.outerContainer,o=zh.DOM,f=yv(a),d=wv(a),m=uv(a).or(TD(a)),r=n.shared.header,g=r.isPositionedAtTop,i=gv(a),p=i===Zp.sliding||i===Zp.floating,u=se(!1),h=function(){return u.get()&&!a.removed},v=function(t){return p?t.fold(function(){return 0},function(t){return 1<t.components().length?gu(t.components()[1].element):0}):0},b=function(){e.broadcastOn([bl()],{})},y=function(t){var n,e,o,r,i,u;void 0===t&&(t=!1),h()&&(f||(n=m.getOrThunk(function(){var t=kD(Yi(Ni(),"margin-left")).getOr(0);return Su(Ni())-yu(c).left+t}),Wi(s.get().element,"max-width",n+"px")),p&&cD.refreshToolbar(l),f||(e=cD.getToolbar(l),o=v(e),r=Ru(c),i=g()?Math.max(r.y-gu(s.get().element)+o,0):r.bottom,Gi(l.element,{position:"absolute",top:Math.round(i)+"px",left:Math.round(r.x)+"px"})),d&&(u=s.get(),t?DB.reset(u):DB.refresh(u)),b())},x=function(t){var n,e;void 0===t&&(t=!0),!f&&d&&h()&&(n=r.getDockingMode(),(e=function(t){switch(hv(a)){case dv.auto:var n=cD.getToolbar(l),e=v(n),o=gu(t.element)-e,r=Ru(c);if(r.y>o)return"top";var i=br(c),u=Math.max(i.dom.scrollHeight,gu(i));return r.bottom<u-o||Pu().bottom<r.bottom-o?"bottom":"top";case dv.bottom:return"bottom";case dv.top:default:return"top"}}(s.get()))!==n&&(function(t){var n=s.get();DB.setModes(n,[t]),r.setDockingMode(t);var e=g()?$u.TopToBottom:$u.BottomToTop;Vr(n.element,Hc,e)}(e),t&&y(!0)))};return{isVisible:h,isPositionedAtTop:g,show:function(){u.set(!0),Wi(l.element,"display","flex"),o.addClass(a.getBody(),"mce-edit-focus"),$i(e.element,"display"),x(!1),y()},hide:function(){u.set(!1),t.outerContainer&&(Wi(l.element,"display","none"),o.removeClass(a.getBody(),"mce-edit-focus")),Wi(e.element,"display","none")},update:y,updateMode:x,repositionPopups:b}},BD=function(t,n){var e=Ru(t);return{pos:n?e.y:e.bottom,bounds:e}},DD=/* */Object.freeze({__proto__:null,render:function(e,o,r,i,t){var u=o.mothership,a=o.uiMothership,c=o.outerContainer,s=se(null),l=fe.fromDom(t.targetNode),f=ED(e,l,o,i,s),d=e.getParam("toolbar_persist",!1,"boolean");hD(e);var n=function(){var t,n;s.get()?f.show():(s.set(cD.getHeader(c).getOrDie()),t=bv(n=e).getOrThunk(function(){return Vi(Ri(fe.fromDom(n.getElement())))}),rl(t,u),rl(t,a),vD(e,o,r,i),cD.setMenubar(c,fD(e,r)),f.show(),function(c,s,l,t){var f=se(BD(s,l.isPositionedAtTop())),n=function(t){var n=BD(s,l.isPositionedAtTop()),e=n.pos,o=n.bounds,r=f.get(),i=r.pos,u=r.bounds,a=o.height!==u.height||o.width!==u.width;f.set({pos:e,bounds:o}),a&&pb(c,t),l.isVisible()&&(i!==e?l.update(!0):a&&(l.updateMode(),l.repositionPopups()))};t||(c.on("activate",l.show),c.on("deactivate",l.hide)),c.on("SkinLoaded ResizeWindow",function(){return l.update(!0)}),c.on("NodeChange keydown",function(t){ap.requestAnimationFrame(function(){return n(t)})}),c.on("ScrollWindow",function(){return l.updateMode()});var e=HE();e.set(ny(fe.fromDom(c.getBody()),"load",n)),c.on("remove",function(){e.clear()})}(e,l,f,d),e.nodeChanged())};e.on("show",n),e.on("hide",f.hide),d||(e.on("focus",n),e.on("blur",f.hide)),e.on("init",function(){(e.hasFocus()||d)&&n()}),Ov(e,o);var m={show:function(){f.show()},hide:function(){f.hide()}};return{editorContainer:c.element.dom,api:m}}}),AD=function(t,n){var e,o,r,i,u,a,c,s,l,f,d,m,g,p;r=vE(0,o=n,TE(e=t)),e.ui.registry.addNestedMenuItem("align",{text:o.shared.providers.translate("Align"),getSubmenuItems:function(){return r.items.validateItems(r.getStyleItems())}}),a=vE(0,u=n,AE(i=t)),i.ui.registry.addNestedMenuItem("fontformats",{text:u.shared.providers.translate("Fonts"),getSubmenuItems:function(){return a.items.validateItems(a.getStyleItems())}}),c=t,l=nt({type:"advanced"},(s=n).styleselect),f=vE(0,s,jE(c,l)),c.ui.registry.addNestedMenuItem("formats",{text:"Formats",getSubmenuItems:function(){return f.items.validateItems(f.getStyleItems())}}),m=vE(0,n,LE(d=t)),d.ui.registry.addNestedMenuItem("blockformats",{text:"Blocks",getSubmenuItems:function(){return m.items.validateItems(m.getStyleItems())}}),p=vE(0,n,IE(g=t)),g.ui.registry.addNestedMenuItem("fontsizes",{text:"Font sizes",getSubmenuItems:function(){return p.items.validateItems(p.getStyleItems())}})},MD=function(t,n){return function(){t.execCommand("mceToggleFormat",!1,n)}},FD=function(t){var n,e;!function(e){$C.each([{name:"bold",text:"Bold",icon:"bold"},{name:"italic",text:"Italic",icon:"italic"},{name:"underline",text:"Underline",icon:"underline"},{name:"strikethrough",text:"Strikethrough",icon:"strike-through"},{name:"subscript",text:"Subscript",icon:"subscript"},{name:"superscript",text:"Superscript",icon:"superscript"}],function(t,n){e.ui.registry.addToggleButton(t.name,{tooltip:t.text,icon:t.icon,onSetup:zE(e,t.name),onAction:MD(e,t.name)})});for(var t=1;t<=6;t++){var n="h"+t;e.ui.registry.addToggleButton(n,{text:n.toUpperCase(),tooltip:"Heading "+t,onSetup:zE(e,n),onAction:MD(e,n)})}}(t),n=t,$C.each([{name:"cut",text:"Cut",action:"Cut",icon:"cut"},{name:"copy",text:"Copy",action:"Copy",icon:"copy"},{name:"paste",text:"Paste",action:"Paste",icon:"paste"},{name:"help",text:"Help",action:"mceHelp",icon:"help"},{name:"selectall",text:"Select all",action:"SelectAll",icon:"select-all"},{name:"newdocument",text:"New document",action:"mceNewDocument",icon:"new-document"},{name:"removeformat",text:"Clear formatting",action:"RemoveFormat",icon:"remove-formatting"},{name:"remove",text:"Remove",action:"Delete",icon:"remove"}],function(t){n.ui.registry.addButton(t.name,{tooltip:t.text,icon:t.icon,onAction:function(){return n.execCommand(t.action)}})}),e=t,$C.each([{name:"blockquote",text:"Blockquote",action:"mceBlockQuote",icon:"quote"}],function(t){e.ui.registry.addToggleButton(t.name,{tooltip:t.text,icon:t.icon,onAction:function(){return e.execCommand(t.action)},onSetup:zE(e,t.name)})})},ID=function(t){var n;FD(t),n=t,$C.each([{name:"bold",text:"Bold",action:"Bold",icon:"bold",shortcut:"Meta+B"},{name:"italic",text:"Italic",action:"Italic",icon:"italic",shortcut:"Meta+I"},{name:"underline",text:"Underline",action:"Underline",icon:"underline",shortcut:"Meta+U"},{name:"strikethrough",text:"Strikethrough",action:"Strikethrough",icon:"strike-through",shortcut:""},{name:"subscript",text:"Subscript",action:"Subscript",icon:"subscript",shortcut:""},{name:"superscript",text:"Superscript",action:"Superscript",icon:"superscript",shortcut:""},{name:"removeformat",text:"Clear formatting",action:"RemoveFormat",icon:"remove-formatting",shortcut:""},{name:"newdocument",text:"New document",action:"mceNewDocument",icon:"new-document",shortcut:""},{name:"cut",text:"Cut",action:"Cut",icon:"cut",shortcut:"Meta+X"},{name:"copy",text:"Copy",action:"Copy",icon:"copy",shortcut:"Meta+C"},{name:"paste",text:"Paste",action:"Paste",icon:"paste",shortcut:"Meta+V"},{name:"selectall",text:"Select all",action:"SelectAll",icon:"select-all",shortcut:"Meta+A"}],function(t){n.ui.registry.addMenuItem(t.name,{text:t.text,icon:t.icon,shortcut:t.shortcut,onAction:function(){return n.execCommand(t.action)}})}),n.ui.registry.addMenuItem("codeformat",{text:"Code",icon:"sourcecode",onAction:MD(n,"code")})},RD=function(t,n,e){var o=function(){return!!n.undoManager&&n.undoManager[e]()},r=function(){t.setDisabled(n.mode.isReadOnly()||!o())};return t.setDisabled(!o()),n.on("Undo Redo AddUndo TypingUndo ClearUndos SwitchMode",r),function(){return n.off("Undo Redo AddUndo TypingUndo ClearUndos SwitchMode",r)}},VD=function(t){var n,e;(n=t).ui.registry.addMenuItem("undo",{text:"Undo",icon:"undo",shortcut:"Meta+Z",onSetup:function(t){return RD(t,n,"hasUndo")},onAction:function(){return n.execCommand("undo")}}),n.ui.registry.addMenuItem("redo",{text:"Redo",icon:"redo",shortcut:"Meta+Y",onSetup:function(t){return RD(t,n,"hasRedo")},onAction:function(){return n.execCommand("redo")}}),(e=t).ui.registry.addButton("undo",{tooltip:"Undo",icon:"undo",onSetup:function(t){return RD(t,e,"hasUndo")},onAction:function(){return e.execCommand("undo")}}),e.ui.registry.addButton("redo",{tooltip:"Redo",icon:"redo",onSetup:function(t){return RD(t,e,"hasRedo")},onAction:function(){return e.execCommand("redo")}})},PD={unsupportedLength:["em","ex","cap","ch","ic","rem","lh","rlh","vw","vh","vi","vb","vmin","vmax","cm","mm","Q","in","pc","pt","px"],fixed:["px","pt"],relative:["%"],empty:[""]},HD=(xE="[eE][+-]?[0-9]+",SE=["Infinity",(yE="[0-9]+")+"\\."+(wE=function(t){return"(?:"+t+")?"})(yE)+wE(xE),"\\."+yE+wE(xE),yE+wE(xE)].join("|"),new RegExp("^([+-]?(?:"+SE+"))(.*)$")),zD=function(t,r){return st.from(HD.exec(t)).bind(function(t){var n,e=Number(t[1]),o=t[2];return n=o,F(r,function(t){return F(PD[t],function(t){return n===t})})?st.some({value:e,unit:o}):st.none()})},ND=function(t){return zD(t,["fixed","relative","empty"]).map(function(t){return t.value+t.unit}).getOr(t)},LD=function(o){var r=o.getParam("lineheight_formats","1 1.1 1.2 1.3 1.4 1.5 2","string").split(" "),i=new Map,u=PE(function(t){return t.destroy()}),a=function(){var t=ND(o.queryCommandValue("LineHeight"));st.from(i.get(t)).fold(function(){return u.clear()},function(t){u.set({destroy:function(){t.setActive(!1)}}),t.setActive(!0)})};return o.on("nodeChange",a),V(r,function(n,e){return{type:"togglemenuitem",text:n,onSetup:function(t){return i.set(ND(n),t),e+1===r.length&&a(),function(){0===e&&(o.off("nodeChange",a),u.clear())}},onAction:function(){return o.execCommand("LineHeight",!1,n)}}})},jD=function(t){var n,e;(n=t).ui.registry.addNestedMenuItem("lineheight",{type:"nestedmenuitem",text:"Line height",getSubmenuItems:function(){return LD(n)}}),(e=t).ui.registry.addMenuButton("lineheight",{tooltip:"Line height",icon:"line-height",fetch:function(t){return t(LD(e))}})},UD=function(t,n){var e,o,r,i;!function(n){$C.each([{name:"alignleft",text:"Align left",cmd:"JustifyLeft",icon:"align-left"},{name:"aligncenter",text:"Align center",cmd:"JustifyCenter",icon:"align-center"},{name:"alignright",text:"Align right",cmd:"JustifyRight",icon:"align-right"},{name:"alignjustify",text:"Justify",cmd:"JustifyFull",icon:"align-justify"}],function(t){n.ui.registry.addToggleButton(t.name,{tooltip:t.text,onAction:function(){return n.execCommand(t.cmd)},icon:t.icon,onSetup:zE(n,t.name)})});var t="alignnone",e="No alignment",o="JustifyNone",r="align-none";n.ui.registry.addButton(t,{tooltip:e,onAction:function(){return n.execCommand(o)},icon:r})}(t),ID(t),AD(t,n),VD(t),function(t){Cb(t);var n=se(null),e=se(null);Db(t,"forecolor","forecolor","Text color",n),Db(t,"backcolor","hilitecolor","Background color",e),Ab(t,"forecolor","forecolor","Text color"),Ab(t,"backcolor","hilitecolor","Background color")}(t),(o=e=t).ui.registry.addButton("visualaid",{tooltip:"Visual aids",text:"Visual aids",onAction:function(){return o.execCommand("mceToggleVisualAid")}}),(r=e).ui.registry.addToggleMenuItem("visualaid",{text:"Visual aids",onSetup:function(t){return function(n,t){n.setActive(t.hasVisual);var e=function(t){n.setActive(t.hasVisual)};return t.on("VisualAid",e),function(){return t.off("VisualAid",e)}}(t,r)},onAction:function(){r.execCommand("mceToggleVisualAid")}}),(i=t).ui.registry.addButton("outdent",{tooltip:"Decrease indent",icon:"outdent",onSetup:function(t){return function(t,n){t.setDisabled(!n.queryCommandState("outdent"));var e=function(){t.setDisabled(!n.queryCommandState("outdent"))};return n.on("NodeChange",e),function(){return n.off("NodeChange",e)}}(t,i)},onAction:function(){return i.execCommand("outdent")}}),i.ui.registry.addButton("indent",{tooltip:"Increase indent",icon:"indent",onAction:function(){return i.execCommand("indent")}}),jD(t)},WD=function(t,n){return{anchor:"makeshift",x:t,y:n}},GD=function(t){return"longpress"===t.type||0===t.type.indexOf("touch")},XD=function(t,n){var e,o,r,i=zh.DOM.getPos(t);return e=n,o=i.x,r=i.y,WD(e.x+o,e.y+r)},YD=function(t,n){return"contextmenu"===n.type||"longpress"===n.type?t.inline?function(t){if(GD(t)){var n=t.touches[0];return WD(n.pageX,n.pageY)}return WD(t.pageX,t.pageY)}(n):XD(t.getContentAreaContainer(),function(t){if(GD(t)){var n=t.touches[0];return WD(n.clientX,n.clientY)}return WD(t.clientX,t.clientY)}(n)):qD(t)},qD=function(t){return{anchor:"selection",root:fe.fromDom(t.selection.getNode())}},KD=function(t){return{anchor:"node",node:st.some(fe.fromDom(t.selection.getNode())),root:fe.fromDom(t.getBody())}},JD=function(t,n,e,o,r,i){var u=e(),a=i?KD(t):YD(t,n);wC(u,nh.CLOSE_ON_EXECUTE,o,!1).map(function(t){n.preventDefault(),Kg.showMenuAt(r,a,{menu:{markers:dh("normal")},data:t})})},$D={onLtr:function(){return[za,Ia,Ra,Va,Pa,Ha,ip,up,op,np,rp,ep]},onRtl:function(){return[za,Ra,Ia,Pa,Va,Ha,ip,up,rp,ep,op,np]}},QD={valignCentre:[],alignCentre:[],alignLeft:["tox-pop--align-left"],alignRight:["tox-pop--align-right"],right:["tox-pop--right"],left:["tox-pop--left"],bottom:["tox-pop--bottom"],top:["tox-pop--top"]},ZD=function(n,e,t,o,r,i,u){var a,c,s=i?KD(n):(a=n,c=e,nt({bubble:Ic(0,12,QD),layouts:$D,overrides:{maxWidthFunction:M_(),maxHeightFunction:Bc()}},YD(a,c)));wC(t,nh.CLOSE_ON_EXECUTE,o,!0).map(function(t){e.preventDefault(),Kg.showMenuWithinBounds(r,s,{menu:{markers:dh("normal"),highlightImmediately:u},data:t,type:"horizontal"},function(){return st.some(cE(n,o.shared))}),n.fire(F_)})},tA=function(n,e,o,r,i,u){var t,a=ze(),c=a.os.isiOS(),s=a.os.isOSX(),l=a.os.isAndroid(),f=a.deviceType.isTouch(),d=function(){var t=o();ZD(n,e,t,r,i,u,!(l||c||s&&f))};!s&&!c||u?(l&&!u&&n.selection.setCursorLocation(e.target,0),d()):(t=function(){!function(t){var n=t.selection.getRng(),e=function(){ap.setEditorTimeout(t,function(){t.selection.setRng(n)},10),i()};t.once("touchend",e);var o=function(t){t.preventDefault(),t.stopImmediatePropagation()};t.on("mousedown",o,!0);var r=function(){return i()};t.once("longpresscancel",r);var i=function(){t.off("touchend",e),t.off("longpresscancel",r),t.off("mousedown",o)}}(n),d()},!function(t,n){var e=t.selection;if(e.isCollapsed()||n.touches.length<1)return!1;var o=n.touches[0],r=e.getRng();return gs(t.getWin(),qc.domRange(r)).exists(function(t){return t.left<=o.clientX&&t.right>=o.clientX&&t.top<=o.clientY&&t.bottom>=o.clientY})}(n,e)?(n.once("selectionchange",t),n.once("touchend",function(){return n.off("selectionchange",t)})):t())},nA=function(t){return"string"==typeof t?t.split(/[ ,]/):t},eA=function(t){return t.getParam("contextmenu_never_use_native",!1,"boolean")},oA=function(t){return e="contextmenu",o="link linkchecker image imagetools table spellchecker configurepermanentpen",r=(n=t).ui.registry.getAll().contextMenus,st.from(n.getParam(e)).map(nA).getOrThunk(function(){return H(nA(o),function(t){return Ft(r,t)})});var n,e,o,r},rA=function(t){return S(t)?"|"===t:"separator"===t.type},iA={type:"separator"},uA=function(n){if(S(n))return n;switch(n.type){case"separator":return iA;case"submenu":return{type:"nestedmenuitem",text:n.text,icon:n.icon,getSubmenuItems:function(){var t=n.getSubmenuItems();return S(t)?t:V(t,uA)}};default:return{type:"menuitem",text:n.text,icon:n.icon,onAction:(t=n.onAction,function(){return t()})}}var t},aA=function(t,n){if(0===n.length)return t;var e=K(t).filter(function(t){return!rA(t)}).fold(function(){return[]},function(t){return[iA]});return t.concat(e).concat(n).concat([iA])},cA=function(t,n){return"longpress"!==n.type&&(2!==n.button||n.target===t.getBody()&&""===n.pointerType)},sA=function(t,n){return cA(t,n)?t.selection.getStart(!0):n.target},lA=function(a,t,e){var o=ze().deviceType.isTouch,r=lu(Kg.sketch({dom:{tag:"div"},lazySink:t,onEscape:function(){return a.focus()},onShow:function(){return e.setContextMenuState(!0)},onHide:function(){return e.setContextMenuState(!1)},fireDismissalEventInstead:{},inlineBehaviours:nc([ag("dismissContextMenu",[Zo(Vo(),function(t,n){hl.close(t),a.focus()})])])})),n=function(t){return Kg.hide(r)},i=function(u){var t,n;eA(a)&&u.preventDefault(),t=a,u.ctrlKey&&!eA(t)||!1===a.getParam("contextmenu")||(n=function(t,n){var e=t.getParam("contextmenu_avoid_overlap","","string");if(cA(t,n))return!0;if(e){var o=sA(t,n);return oy(fe.fromDom(o),e)}return!1}(a,u),(o()?tA:JD)(a,u,function(){var r,i,t,n=sA(a,u),e=a.ui.registry.getAll(),o=oA(a);return r=e.contextMenus,i=n,0<(t=N(o,function(t,n){if(Ft(r,n)){var e=r[n].update(i);if(S(e))return aA(t,e.split(" "));if(0<e.length){var o=V(e,uA);return aA(t,o)}return t}return t.concat([n])},[])).length&&rA(t[t.length-1])&&t.pop(),t},e,r,n))};a.on("init",function(){var t="ResizeEditor ScrollContent ScrollWindow longpresscancel"+(o()?"":" ResizeWindow");a.on(t,n),a.on("longpress contextmenu",i)})},fA=Rt([{offset:["x","y"]},{absolute:["x","y"]},{fixed:["x","y"]}]),dA=function(n){return function(t){return t.translate(-n.left,-n.top)}},mA=function(n){return function(t){return t.translate(n.left,n.top)}},gA=function(e){return function(t,n){return N(e,function(t,n){return n(t)},vu(t,n))}},pA=function(t,n,e){return t.fold(gA([mA(e),dA(n)]),gA([dA(n)]),gA([]))},hA=function(t,n,e){return t.fold(gA([mA(e)]),gA([]),gA([mA(n)]))},vA=function(t,n,e){return t.fold(gA([]),gA([dA(e)]),gA([mA(n),dA(e)]))},bA=function(t,n,e){var o=t.fold(function(t,n){return{position:st.some("absolute"),left:st.some(t+"px"),top:st.some(n+"px")}},function(t,n){return{position:st.some("absolute"),left:st.some(t-e.left+"px"),top:st.some(n-e.top+"px")}},function(t,n){return{position:st.some("fixed"),left:st.some(t+"px"),top:st.some(n+"px")}});return nt({right:st.none(),bottom:st.none()},o)},yA=function(t,i,u,a){var n=function(o,r){return function(t,n){var e=o(i,u,a);return r(t.getOr(e.left),n.getOr(e.top))}};return t.fold(n(vA,xA),n(hA,wA),n(pA,SA))},xA=fA.offset,wA=fA.absolute,SA=fA.fixed,kA=function(t,n){var e=Pr(t,n);return v(e)?NaN:parseInt(e,10)},CA=function(t,n,e,o){return r=n,i=t.element,u=kA(i,r.leftAttr),a=kA(i,r.topAttr),(isNaN(u)||isNaN(a)?st.none():st.some(vu(u,a))).fold(function(){return e},function(t){return SA(t.left+o.left,t.top+o.top)});var r,i,u,a},OA=function(t,n,e,o,r,i){var u,a,c,s=CA(t,n,e,o),l=(n.mustSnap?EA:BA)(t,n,s,r,i),f=pA(s,r,i);return u=n,a=f,c=t.element,Vr(c,u.leftAttr,a.left+"px"),Vr(c,u.topAttr,a.top+"px"),l.fold(function(){return{coord:SA(f.left,f.top),extra:st.none()}},function(t){return{coord:t.output,extra:t.extra}})},_A=function(t,n){var e,o;e=n,o=t.element,Nr(o,e.leftAttr),Nr(o,e.topAttr)},TA=function(t,l,f,d){return Q(t,function(t){var n,e,o,r,i,u,a,c,s=t.sensor;return(n=l,e=s,o=t.range.left,r=t.range.top,a=hA(n,i=f,u=d),c=hA(e,i,u),Math.abs(a.left-c.left)<=o&&Math.abs(a.top-c.top)<=r)?st.some({output:yA(t.output,l,f,d),extra:t.extra}):st.none()})},EA=function(t,n,d,m,g){var e=n.getSnapPoints(t);return TA(e,d,m,g).orThunk(function(){return N(e,function(n,e){var t,o,r,i,u,a,c,s,l=e.sensor,f=(t=d,o=l,e.range.left,e.range.top,u=hA(t,r=m,i=g),a=hA(o,r,i),c=Math.abs(u.left-a.left),s=Math.abs(u.top-a.top),vu(c,s));return n.deltas.fold(function(){return{deltas:st.some(f),snap:st.some(e)}},function(t){return(f.left+f.top)/2<=(t.left+t.top)/2?{deltas:st.some(f),snap:st.some(e)}:n})},{deltas:st.none(),snap:st.none()}).snap.map(function(t){return{output:yA(t.output,d,m,g),extra:t.extra}})})},BA=function(t,n,e,o,r){var i=n.getSnapPoints(t);return TA(i,e,o,r)},DA=/* */Object.freeze({__proto__:null,snapTo:function(t,n,e,o){var r,i,u,a,c,s,l,f,d=n.getTarget(t.element);n.repositionTarget&&(r=hr(t.element),i=Tu(r),u=dB(d),l=i,f=u,a={coord:yA((s=o).output,s.output,l,f),extra:s.extra},c=bA(a.coord,0,u),Xi(d,c))}}),AA="data-initial-z-index",MA=function(t,n){var e;t.getSystem().addToGui(n),xr((e=n).element).filter(dr).each(function(n){Ki(n,"z-index").each(function(t){Vr(n,AA,t)}),Wi(n,"z-index",Yi(e.element,"z-index"))})},FA=function(t){xr(t.element).filter(dr).each(function(n){Hr(n,AA).fold(function(){return $i(n,"z-index")},function(t){return Wi(n,"z-index",t)}),Nr(n,AA)}),t.getSystem().removeFromGui(t)},IA=function(t,n,e){return t.getSystem().build(Cy.sketch({dom:{styles:{left:"0px",top:"0px",width:"100%",height:"100%",position:"fixed","z-index":"1000000000000000"},classes:[n]},events:e}))},RA=Zn("snaps",[Nn("getSnapPoints"),la("onSensor"),Nn("leftAttr"),Nn("topAttr"),te("lazyViewport",Pu),te("mustSnap",!1)]),VA=[te("useFixed",c),Nn("blockerClass"),te("getTarget",ct),te("onDrag",$),te("repositionTarget",!0),te("onDrop",$),ue("getBounds",Pu),RA],PA=function(n){return t=Ki(n,"left"),e=Ki(n,"top"),o=Ki(n,"position"),r=function(t,n,e){return("fixed"===e?SA:xA)(parseInt(t,10),parseInt(n,10))},(t.isSome()&&e.isSome()&&o.isSome()?st.some(r(t.getOrDie(),e.getOrDie(),o.getOrDie())):st.none()).getOrThunk(function(){var t=yu(n);return wA(t.left,t.top)});var t,e,o,r},HA=function(e,t,i,u,a,c,n){var o,r,s,l,f,d,m,g,p,h=t.fold(function(){var t,e,o,n=(t=i,e=c.left,o=c.top,t.fold(function(t,n){return xA(t+e,n+o)},function(t,n){return wA(t+e,n+o)},function(t,n){return SA(t+e,n+o)})),r=pA(n,u,a);return SA(r.left,r.top)},function(n){var t=OA(e,n,i,c,u,a);return t.extra.each(function(t){n.onSensor(e,t)}),t.coord});return o=h,r=u,s=a,f=(l=n).bounds,d=hA(o,r,s),m=Sc(d.left,f.x,f.x+f.width-l.width),g=Sc(d.top,f.y,f.y+f.height-l.height),p=wA(m,g),o.fold(function(){var t=vA(p,r,s);return xA(t.left,t.top)},function(){return p},function(){var t=pA(p,r,s);return SA(t.left,t.top)})},zA=function(t,n){return{bounds:t.getBounds(),height:pu(n.element),width:ku(n.element)}},NA=function(d,m,t,n,e){var o=t.update(n,e),g=t.getStartData().getOrThunk(function(){return zA(m,d)});o.each(function(t){var n,e,o,r,i,u,a,c,s,l,f;n=d,o=g,r=t,f=(e=m).getTarget(n.element),e.repositionTarget&&(i=hr(n.element),u=Tu(i),a=dB(f),c=PA(f),s=HA(n,e.snaps,c,u,a,r,o),l=bA(s,0,a),Xi(f,l)),e.onDrag(n,f,r)})},LA=function(n,t,e,o){t.each(FA),e.snaps.each(function(t){_A(n,t)});var r=e.getTarget(n.element);o.reset(),e.onDrop(n,r)},jA=function(t){return function(n,e){var o=function(t){e.setStartData(zA(n,t))};return Jo(w([Zo(Mo(),function(t){e.getStartData().each(function(){return o(t)})})],t(n,e,o)))}},UA=/* */Object.freeze({__proto__:null,getData:function(t){return st.from(vu(t.x,t.y))},getDelta:function(t,n){return vu(n.left-t.left,n.top-t.top)}}),WA=function(a,c,s){return[Zo(eo(),function(n,t){var e,o,r,i,u;0===t.event.raw.button&&(t.stop(),r={drop:e=function(){return LA(n,st.some(i),a,c)},delayDrop:(o=ry(e,200)).schedule,forceDrop:e,move:function(t){o.cancel(),NA(n,a,c,UA,t)}},i=IA(n,a.blockerClass,(u=r,Jo([Zo(eo(),u.forceDrop),Zo(io(),u.drop),Zo(oo(),function(t,n){u.move(n.event)}),Zo(ro(),u.delayDrop)]))),s(n),MA(n,i))})]},GA=w(VA,[ga("dragger",{handlers:jA(WA)})]),XA=/* */Object.freeze({__proto__:null,getData:function(t){var n,e=t.raw.touches;return 1===e.length?(n=e[0],st.some(vu(n.clientX,n.clientY))):st.none()},getDelta:function(t,n){return vu(n.left-t.left,n.top-t.top)}}),YA=function(u,a,c){var s=se(st.none());return[Zo(Qe(),function(n,t){t.stop();var e,o=function(){LA(n,s.get(),u,a),s.set(st.none())},r={drop:o,delayDrop:function(){},forceDrop:o,move:function(t){NA(n,u,a,XA,t)}},i=IA(n,u.blockerClass,(e=r,Jo([Zo(Qe(),e.forceDrop),Zo(to(),e.drop),Zo(no(),e.drop),Zo(Ze(),function(t,n){e.move(n.event)})])));s.set(st.some(i));c(n),MA(n,i)}),Zo(Ze(),function(t,n){n.stop(),NA(t,u,a,XA,n.event)}),Zo(to(),function(t,n){n.stop(),LA(t,s.get(),u,a),s.set(st.none())}),Zo(no(),function(t){LA(t,s.get(),u,a),s.set(st.none())})]},qA=w(VA,[ga("dragger",{handlers:jA(YA)})]),KA=w(VA,[ga("dragger",{handlers:jA(function(t,n,e){return w(WA(t,n,e),YA(t,n,e))})})]),JA=ic({branchKey:"mode",branches:/* */Object.freeze({__proto__:null,mouse:GA,touch:qA,mouseOrTouch:KA}),name:"dragging",active:{events:function(t,n){return t.dragger.handlers(t,n)}},extra:{snap:function(t){return{sensor:t.sensor,range:t.range,output:t.output,extra:st.from(t.extra)}}},state:/* */Object.freeze({__proto__:null,init:function(){var i=st.none(),n=st.none(),t=at({});return si({readState:t,reset:function(){i=st.none(),n=st.none()},update:function(r,t){return r.getData(t).bind(function(t){return n=r,e=t,o=i.map(function(t){return n.getDelta(t,e)}),i=st.some(e),o;var n,e,o})},getStartData:function(){return n},setStartData:function(t){n=st.some(t)}})}}),apis:DA}),$A=function(t,r,i,u,n,e){return t.fold(function(){return JA.snap({sensor:wA(i-20,u-20),range:vu(n,e),output:wA(st.some(i),st.some(u)),extra:{td:r}})},function(t){var n=i-20,e=u-20,o=t.element.dom.getBoundingClientRect();return JA.snap({sensor:wA(n,e),range:vu(40,40),output:wA(st.some(i-o.width/2),st.some(u-o.height/2)),extra:{td:r}})})},QA=function(t,i,u){return{getSnapPoints:t,leftAttr:"data-drag-left",topAttr:"data-drag-top",onSensor:function(t,n){var e,o,r=n.td;e=i.get(),o=r,e.exists(function(t){return je(t,o)})||(i.set(st.some(r)),u(r))},mustSnap:!0}},ZA=function(t){return sp(cp.sketch({dom:{tag:"div",classes:["tox-selector"]},buttonBehaviours:nc([JA.config({mode:"mouseOrTouch",blockerClass:"blocker",snaps:t}),Rx.config({})]),eventOrder:{mousedown:["dragging","alloy.base.behaviour"],touchstart:["dragging","alloy.base.behaviour"]}}))},tM=function(c,e){var o=se([]),r=se([]),t=se(!1),i=se(st.none()),u=se(st.none()),n=function(t){var n=Vu(t);return $A(f.getOpt(e),t,n.x,n.y,n.width,n.height)},a=function(t){var n=Vu(t);return $A(d.getOpt(e),t,n.right,n.bottom,n.width,n.height)},s=QA(function(){return V(o.get(),n)},i,function(n){u.get().each(function(t){c.fire("TableSelectorChange",{start:n,finish:t})})}),l=QA(function(){return V(r.get(),a)},u,function(n){i.get().each(function(t){c.fire("TableSelectorChange",{start:t,finish:n})})}),f=ZA(s),d=ZA(l),m=lu(f.asSpec()),g=lu(d.asSpec()),p=function(t,n,e,o){var r=e(n);JA.snapTo(t,r);!function(t,n,e,o){var r=n.dom.getBoundingClientRect();$i(t.element,"display");var i=yr(fe.fromDom(c.getBody())).dom.innerHeight,u=e(r),a=o(r,i);(u||a)&&Wi(t.element,"display","none")}(t,n,function(t){return t[o]<0},function(t,n){return t[o]>n})},h=function(t){return p(m,t,n,"top")},v=function(t){return p(g,t,a,"bottom")};ze().deviceType.isTouch()&&(c.on("TableSelectionChange",function(n){t.get()||(Zs(e,m),Zs(e,g),t.set(!0)),i.set(st.some(n.start)),u.set(st.some(n.finish)),n.otherCells.each(function(t){o.set(t.upOrLeftCells),r.set(t.downOrRightCells),h(n.start),v(n.finish)})}),c.on("ResizeEditor ResizeWindow ScrollContent",function(){i.get().each(h),u.get().each(v)}),c.on("TableSelectionClear",function(){t.get()&&(el(m),el(g),t.set(!1)),i.set(st.none()),u.set(st.none())}))};(CE=kE=kE||{})[CE.None=0]="None",CE[CE.Both=1]="Both",CE[CE.Vertical=2]="Vertical";var nM,eM,oM,rM=function(t,n,e){var o,r,i,u,a,c,s=fe.fromDom(t.getContainer()),l=(o=t,r=n,i=e,u=gu(s),a=Su(s),(c={}).height=OD(u+r.top,iv(o),av(o)),i===kE.Both&&(c.width=OD(a+r.left,rv(o),uv(o))),c);Ot(l,function(t,n){return Wi(s,n,CD(t))}),t.fire("ResizeEditor")},iM=function(i,u,a){u.delimiter||(u.delimiter="\xbb");return{dom:{tag:"div",classes:["tox-statusbar__path"],attributes:{role:"navigation"}},behaviours:nc([ng.config({mode:"flow",selector:"div[role=button]"}),tv.config({disabled:a.isReadOnly}),_v(),Ay.config({}),ug.config({}),ag("elementPathEvents",[ur(function(r,t){i.shortcuts.add("alt+F11","focus statusbar elementpath",function(){return ng.focusIn(r)}),i.on("NodeChange",function(t){var n,o,e=function(t){for(var n=[],e=t.length;0<e--;){var o=t[e];if(1===o.nodeType&&!function(t){if(1===t.nodeType){if("BR"===t.nodeName||t.getAttribute("data-mce-bogus"))return!0;if("bookmark"===t.getAttribute("data-mce-type"))return!0}return!1}(o)){var r=i.fire("ResolveName",{name:o.nodeName.toLowerCase(),target:o});if(r.isDefaultPrevented()||n.push({name:r.name,element:o}),r.isPropagationStopped())break}}return n}(t.parents);0<e.length?ug.set(r,(n=V(e||[],function(n,t){return cp.sketch({dom:{tag:"div",classes:["tox-statusbar__path-item"],attributes:{role:"button","data-index":t,"tab-index":-1,"aria-level":t+1},innerHtml:n.name},action:function(t){i.focus(),i.selection.select(n.element),i.nodeChanged()},buttonBehaviours:nc([Ev(a.isReadOnly),_v()])})}),o={dom:{tag:"div",classes:["tox-statusbar__path-divider"],attributes:{"aria-hidden":!0},innerHtml:" "+u.delimiter+" "}},N(n.slice(1),function(t,n){var e=t;return e.push(o),e.push(n),e},[n[0]]))):ug.set(r,[])})})])]),components:[]}},uM=function(u,a){var t,n,e,o,r,i;return{dom:{tag:"div",classes:["tox-statusbar"]},components:(r=function(){var t,o,n,r,e,i=[];return u.getParam("elementpath",!0,"boolean")&&i.push(iM(u,{},a)),u.hasPlugin("wordcount")&&i.push((t=u,o=a,r=function(t,n,e){return ug.set(t,[au(o.translate(["{0} "+e,n[e]]))])},cp.sketch({dom:{tag:"button",classes:["tox-statusbar__wordcount"]},components:[],buttonBehaviours:nc([Ev(o.isReadOnly),_v(),Ay.config({}),ug.config({}),Vl.config({store:{mode:"memory",initialValue:{mode:"words",count:{words:0,characters:0}}}}),ag("wordcount-events",[sr(function(t){var n=Vl.getValue(t),e="words"===n.mode?"characters":"words";Vl.setValue(t,{mode:e,count:n.count}),r(t,n.count,e)}),ur(function(e){t.on("wordCountUpdate",function(t){var n=Vl.getValue(e).mode;Vl.setValue(e,{mode:n,count:t.wordCount}),r(e,t.wordCount,n)})})])]),eventOrder:((n={})[ko()]=["disabling","alloy.base.behaviour","wordcount-events"],n)}))),u.getParam("branding",!0,"boolean")&&i.push({dom:{tag:"span",classes:["tox-statusbar__branding"],innerHtml:'<a href="https://www.tiny.cloud/?utm_campaign=editor_referral&amp;utm_medium=poweredby&amp;utm_source=tinymce&amp;utm_content=v5" rel="noopener" target="_blank" tabindex="-1" aria-label="'+(e=Nh.translate(["Powered by {0}","Tiny"]))+'">'+e+"</a>"}}),0<i.length?[{dom:{tag:"div",classes:["tox-statusbar__text-container"]},components:i}]:[]}(),n=!(t=u).hasPlugin("autoresize"),(i=!1===(e=t.getParam("resize",n))?kE.None:"both"===e?kE.Both:kE.Vertical)!==kE.None&&r.push((o=i,{dom:{tag:"div",classes:["tox-statusbar__resize-handle"],attributes:{title:a.translate("Resize"),"aria-hidden":"true"},innerHtml:fp("resize-handle",a.icons)},behaviours:nc([JA.config({mode:"mouse",repositionTarget:!1,onDrag:function(t,n,e){rM(u,e,o)},blockerClass:"tox-blocker"})])})),r)}},aM=function(x){var t,n=x.inline,w=n?DD:SD,S=wv(x)?HB:lB,e=st.none(),o=ze(),r=o.browser.isIE()?["tox-platform-ie"]:[],i=o.deviceType.isTouch()?["tox-platform-touch"]:[],u=vv(x),a=Nh.isRtl()?{attributes:{dir:"rtl"}}:{},c={attributes:((t={})[Hc]=u?$u.BottomToTop:$u.TopToBottom,t)},k=function(){return e.bind(cD.getHeader)},C=lu({dom:nt({tag:"div",classes:["tox","tox-silver-sink","tox-tinymce-aux"].concat(r).concat(i)},a),behaviours:nc([qs.config({useFixed:function(){return S.isDocked(k)}})])}),O=function(){return it.value(C)},s=sp({dom:{tag:"div",classes:["tox-anchorbar"]}}),_=function(){return e.bind(function(t){return cD.getThrobber(t)}).getOrDie("Could not find throbber element")},T=A_(C,x,function(){return e.bind(function(t){return s.getOpt(t)}).getOrDie("Could not find a anchor bar element")}),l=cD.parts.menubar({dom:{tag:"div",classes:["tox-menubar"]},backstage:T,onEscape:function(){x.focus()}}),E=gv(x),f=cD.parts.toolbar(nt({dom:{tag:"div",classes:["tox-toolbar"]},getSink:O,providers:T.shared.providers,onEscape:function(){x.focus()},type:E,lazyToolbar:function(){return e.bind(function(t){return cD.getToolbar(t)}).getOrDie("Could not find more toolbar element")},lazyHeader:function(){return k().getOrDie("Could not find header element")}},c)),d=cD.parts["multiple-toolbar"]({dom:{tag:"div",classes:["tox-toolbar-overlord"]},providers:T.shared.providers,onEscape:function(){x.focus()},type:E}),m=cD.parts.socket({dom:{tag:"div",classes:["tox-edit-area"]}}),g=cD.parts.sidebar({dom:{tag:"div",classes:["tox-sidebar"]}}),p=cD.parts.throbber({dom:{tag:"div",classes:["tox-throbber"]},backstage:T}),h=x.getParam("statusbar",!0,"boolean")&&!n?st.some(uM(x,T.shared.providers)):st.none(),v={dom:{tag:"div",classes:["tox-sidebar-wrap"]},components:[m,g]},b=fv(x),y=sv(x),B=cv(x),D=cD.parts.header({dom:nt({tag:"div",classes:["tox-editor-header"]},c),components:rt([B?[l]:[],b?[d]:y?[f]:[],yv(x)?[]:[s.asSpec()]]),sticky:wv(x),editor:x,sharedBackstage:T.shared}),A=rt([u?[]:[D],n?[]:[v],u?[D]:[]]),M=rt([[{dom:{tag:"div",classes:["tox-editor-container"]},components:A}],n?[]:h.toArray(),[p]]),F=xv(x),I=nt(nt({role:"application"},Nh.isRtl()?{dir:"rtl"}:{}),F?{"aria-hidden":"true"}:{}),R=lu(cD.sketch({dom:{tag:"div",classes:["tox","tox-tinymce"].concat(n?["tox-tinymce-inline"]:[]).concat(u?["tox-tinymce--toolbar-bottom"]:[]).concat(i).concat(r),styles:nt({visibility:"hidden"},F?{opacity:"0",border:"0"}:{}),attributes:I},components:M,behaviours:nc([ng.config({mode:"cyclic",selector:".tox-menubar, .tox-toolbar, .tox-toolbar__primary, .tox-toolbar__overflow--open, .tox-sidebar__overflow--open, .tox-statusbar__path, .tox-statusbar__wordcount, .tox-statusbar__branding a"})])})),e=st.some(R);x.shortcuts.add("alt+F9","focus menubar",function(){cD.focusMenubar(R)}),x.shortcuts.add("alt+F10","focus toolbar",function(){cD.focusToolbar(R)}),x.addCommand("ToggleToolbarDrawer",function(){cD.toggleToolbarDrawer(R)}),x.addQueryStateHandler("ToggleToolbarDrawer",function(){return cD.isToolbarDrawerToggled(R)});var V,P,H,z,N,L,j,U,W,G,X,Y,q,K,J,$,Q=Oy(R),Z=Oy(C);V=x,P=Q,H=Z,z=function(n,e){ot([P,H],function(t){t.broadcastEvent(n,e)})},N=function(n,e){ot([P,H],function(t){t.broadcastOn([n],e)})},L=function(t){return N(vl(),{target:t.target})},j=ty(fe.fromDom(document),"touchstart",L),U=ty(fe.fromDom(document),"touchmove",function(t){return z(Do(),t)}),W=ty(fe.fromDom(document),"touchend",function(t){return z(Ao(),t)}),G=ty(fe.fromDom(document),"mousedown",L),X=ty(fe.fromDom(document),"mouseup",function(t){0===t.raw.button&&N(yl(),{target:t.target})}),Y=function(t){return N(vl(),{target:fe.fromDom(t.target)})},q=function(t){0===t.button&&N(yl(),{target:fe.fromDom(t.target)})},K=function(t){return z(Mo(),ey(t))},J=function(t){N(bl(),{}),z(Fo(),ey(t))},$=function(){return N(bl(),{})},V.on("PostRender",function(){V.on("click",Y),V.on("tap",Y),V.on("mouseup",q),V.on("ScrollWindow",K),V.on("ResizeWindow",J),V.on("ResizeEditor",$)}),V.on("remove",function(){V.off("click",Y),V.off("tap",Y),V.off("mouseup",q),V.off("ScrollWindow",K),V.off("ResizeWindow",J),V.off("ResizeEditor",$),G.unbind(),j.unbind(),U.unbind(),W.unbind(),X.unbind()}),V.on("detach",function(){ul(P),ul(H),P.destroy(),H.destroy()});var tt=function(){var t,n=CD(_D(x)),e=CD(TD(t=x).getOr(ov(t)));return x.inline||(Ji("div","width",e)&&Wi(R.element,"width",e),Ji("div","height",n)?Wi(R.element,"height",n):Wi(R.element,"height","200px")),n};return{mothership:Q,uiMothership:Z,backstage:T,renderUI:function(){var o,r,e,n,i,u,a,c;S.setup(x,T.shared,k),UD(x,T),lA(x,O,T),r=(o=x).ui.registry.getAll().sidebars,ot(kt(r),function(n){var t=r[n],e=function(){return st.from(o.queryCommandValue("ToggleSidebar")).is(n)};o.ui.registry.addToggleButton(n,{icon:t.icon,tooltip:t.tooltip,onAction:function(t){o.execCommand("ToggleSidebar",!1,n),t.setActive(e())},onSetup:function(t){var n=function(){return t.setActive(e())};return o.on("ToggleSidebar",n),function(){o.off("ToggleSidebar",n)}}})}),e=x,n=_,i=T.shared,u=se(!1),a=se(st.none()),c=function(t){t!==u.get()&&(tD(n(),t,i.providers),u.set(t))},e.on("ProgressState",function(t){var n;a.get().each(ap.clearTimeout),et(t.time)?(n=ap.setEditorTimeout(e,function(){return c(t.state)},t.time),a.set(st.some(n))):(c(t.state),a.set(st.none()))}),_t(x.getParam("toolbar_groups",{},"object"),function(t,n){x.ui.registry.addGroupToolbarButton(n,t)});var t,s=x.ui.registry.getAll(),l=s.buttons,f=s.menuItems,d=s.contextToolbars,m=s.sidebars,g=lv(x),p={menuItems:f,menus:(t=x.getParam("menu"))?_t(t,function(t){return nt(nt({},t),{items:t.items})}):{},menubar:x.getParam("menubar"),toolbar:g.getOrThunk(function(){return x.getParam("toolbar",!0)}),allowToolbarGroups:E===Zp.floating,buttons:l,sidebar:m};tB(x,d,C,{backstage:T}),tM(x,C);var h=x.getElement(),v=tt(),b={mothership:Q,uiMothership:Z,outerContainer:R},y={targetNode:h,height:v};return w.render(x,b,p,T,y)},getUi:function(){return{channels:{broadcastAll:Z.broadcast,broadcastOn:Z.broadcastOn,register:function(){}}}}}},cM=at([Nn("lazySink"),qn("dragBlockClass"),ue("getBounds",Pu),te("useTabstopAt",b),te("eventOrder",{}),Pl("modalBehaviours",[ng]),fa("onExecute"),ma("onEscape")]),sM={sketch:ct},lM=at([pf({name:"draghandle",overrides:function(t,n){return{behaviours:nc([JA.config({mode:"mouse",getTarget:function(t){return Wu(t,'[role="dialog"]').getOr(t)},blockerClass:t.dragBlockClass.getOrDie(new Error("The drag blocker class was not specified for a dialog with a drag handle: \n"+JSON.stringify(n,null,2)).message),getBounds:t.getDragBounds})])}}}),mf({schema:[Nn("dom")],name:"title"}),mf({factory:sM,schema:[Nn("dom")],name:"close"}),mf({factory:sM,schema:[Nn("dom")],name:"body"}),pf({factory:sM,schema:[Nn("dom")],name:"footer"}),gf({factory:{sketch:function(t,n){return nt(nt({},t),{dom:n.dom,components:n.components})}},schema:[te("dom",{tag:"div",styles:{position:"fixed",left:"0px",top:"0px",right:"0px",bottom:"0px"}}),te("components",[])],name:"blocker"})]),fM=Wf({name:"ModalDialog",configFields:cM(),partFields:lM(),factory:function(a,t,n,o){var e,r=Xr("alloy.dialog.busy"),c=Xr("alloy.dialog.idle"),s=nc([ng.config({mode:"special",onTab:function(){return st.some(!0)},onShiftTab:function(){return st.some(!0)}}),dg.config({})]),i=Xr("modal-events"),u=nt(nt({},a.eventOrder),((e={})[Io()]=[i].concat(a.eventOrder["alloy.system.attached"]||[]),e));return{uid:a.uid,dom:a.dom,components:t,apis:{show:function(i){var t=a.lazySink(i).getOrDie(),u=se(st.none()),n=o.blocker(),e=t.getSystem().build(nt(nt({},n),{components:n.components.concat([fu(i)]),behaviours:nc([dg.config({}),ag("dialog-blocker-events",[ir(ao(),function(){ng.focusIn(i)}),Zo(c,function(t,n){zr(i.element,"aria-busy")&&(Nr(i.element,"aria-busy"),u.get().each(function(t){return ug.remove(i,t)}))}),Zo(r,function(t,n){Vr(i.element,"aria-busy","true");var e=n.event.getBusySpec;u.get().each(function(t){ug.remove(i,t)});var o=e(i,s),r=t.getSystem().build(o);u.set(st.some(r)),ug.append(i,fu(r)),r.hasConfigured(ng)&&ng.focusIn(r)})])])}));Zs(t,e),ng.focusIn(i)},hide:function(n){xr(n.element).each(function(t){n.getSystem().getByDom(t).each(function(t){el(t)})})},getBody:function(t){return Ef(t,a,"body")},getFooter:function(t){return Ef(t,a,"footer")},setIdle:function(t){Wo(t,c)},setBusy:function(t,n){Go(t,r,{getBusySpec:n})}},eventOrder:u,domModification:{attributes:{role:"dialog","aria-modal":"true"}},behaviours:zl(a.modalBehaviours,[ug.config({}),ng.config({mode:"cyclic",onEnter:a.onExecute,onEscape:a.onEscape,useTabstopAt:a.useTabstopAt}),ag(i,[ur(function(t){var n,e,o,r,i,u;n=t.element,e=Ef(t,a,"title").element,o=Hr(n,"id").fold(function(){var t=Xr("dialog-label");return Vr(e,"id",t),t},ct),Vr(n,"aria-labelledby",o),r=t.element,i=Ef(t,a,"body").element,u=st.from(Pr(r,"id")).fold(function(){var t=Xr("dialog-describe");return Vr(i,"id",t),t},ct),Vr(r,"aria-describedby",u)})])])}},apis:{show:function(t,n){t.show(n)},hide:function(t,n){t.hide(n)},getBody:function(t,n){return t.getBody(n)},getFooter:function(t,n){return t.getFooter(n)},setBusy:function(t,n,e){t.setBusy(n,e)},setIdle:function(t,n){t.setIdle(n)}}}),dM=ln([jn("type"),jn("name")].concat(Sh)),mM=Rn,gM=[vn("name","name",Gt(function(){return Xr("button-name")}),In),$n("icon"),re("align","end",["start","end"]),ie("primary",!1),ie("disabled",!1)],pM=w(gM,[jn("text")]),hM=w([Un("type",["submit","cancel","custom"])],pM),vM=w([Un("type",["menu"]),$n("text"),$n("tooltip"),$n("icon"),Yn("items",dM)],gM),bM=Dn("type",{submit:hM,cancel:hM,custom:hM,menu:vM}),yM=[jn("type"),jn("text"),Un("level",["info","warn","error","success"]),jn("icon"),te("url","")],xM=ln(yM),wM=[jn("type"),jn("text"),ie("disabled",!1),ie("primary",!1),vn("name","name",Gt(function(){return Xr("button-name")}),In),$n("icon"),ie("borderless",!1)],SM=ln(wM),kM=[jn("type"),jn("name"),jn("label"),ie("disabled",!1)],CM=ln(kM),OM=Rn,_M=[jn("type"),jn("name")],TM=_M.concat([$n("label")]),EM=TM.concat([te("columns","auto")]),BM=ln(EM),DM=wn([jn("value"),jn("text"),jn("icon")]),AM=ln(TM),MM=In,FM=ln(TM),IM=In,RM=_M.concat([oe("tag","textarea"),jn("scriptId"),jn("scriptUrl"),(nM=undefined,ne("settings",nM,Hn))]),VM=_M.concat([oe("tag","textarea"),Wn("init")]),PM=kn(function(t){return On("customeditor.old",sn(VM),t).orThunk(function(){return On("customeditor.new",sn(RM),t)})}),HM=In,zM=ln(TM),NM=fn(xn),LM=function(t){return[jn("type"),Ln("columns",Fn),t]},jM=[jn("type"),jn("html"),re("presets","presentation",["presentation","document"])],UM=ln(jM),WM=TM.concat([ie("sandboxed",!0)]),GM=ln(WM),XM=In,YM=TM.concat([Ln("currentState",ln([Nn("blob"),jn("url")]))]),qM=ln(YM),KM=TM.concat([$n("inputMode"),$n("placeholder"),ie("maximized",!1),ie("disabled",!1)]),JM=ln(KM),$M=In,QM=[jn("text"),jn("value")],ZM=[jn("text"),Yn("items",(eM=function(){return tF},oM=Nt(function(){return eM()}),{extract:function(t,n,e){return oM().extract(t,n,e)},toString:function(){return oM().toString()}}))],tF=dn([ln(QM),ln(ZM)]),nF=TM.concat([Yn("items",tF),ie("disabled",!1)]),eF=ln(nF),oF=In,rF=TM.concat([Xn("items",[jn("text"),jn("value")]),ee("size",1),ie("disabled",!1)]),iF=ln(rF),uF=In,aF=TM.concat([ie("constrain",!0),ie("disabled",!1)]),cF=ln(aF),sF=ln([jn("width"),jn("height")]),lF=[jn("type"),Yn("header",In),Yn("cells",fn(In))],fF=ln(lF),dF=TM.concat([$n("placeholder"),ie("maximized",!1),ie("disabled",!1)]),mF=ln(dF),gF=In,pF=TM.concat([re("filetype","file",["image","media","file"]),te("disabled",!1)]),hF=ln(pF),vF=ln([jn("value"),te("meta",{})]),bF=function(n){return vn("items","items",Ut(),fn(kn(function(t){return On("Checking item of "+n,yF,t).fold(function(t){return it.error(En(t))},function(t){return it.value(t)})})))},yF=Sn(function(){return Bn("type",{alertbanner:xM,bar:ln((n=bF("bar"),[jn("type"),n])),button:SM,checkbox:CM,colorinput:AM,colorpicker:FM,dropzone:zM,grid:ln(LM(bF("grid"))),iframe:GM,input:JM,listbox:eF,selectbox:iF,sizeinput:cF,textarea:mF,urlinput:hF,customeditor:PM,htmlpanel:UM,imagetools:qM,collection:BM,label:ln((t=bF("label"),[jn("type"),jn("label"),t])),table:fF,panel:wF});var t,n}),xF=[jn("type"),te("classes",[]),Yn("items",yF)],wF=ln(xF),SF=[vn("name","name",Gt(function(){return Xr("tab-name")}),In),jn("title"),Yn("items",yF)],kF=[jn("type"),Xn("tabs",SF)],CF=ln(kF),OF=pM,_F=bM,TF=ln([jn("title"),Ln("body",Bn("type",{panel:wF,tabpanel:CF})),oe("size","normal"),Yn("buttons",_F),te("initialData",{}),ue("onAction",$),ue("onChange",$),ue("onSubmit",$),ue("onClose",$),ue("onCancel",$),te("onTabChange",$)]),EF=ln(w([Un("type",["cancel","custom"])],OF)),BF=ln([jn("title"),jn("url"),Jn("height"),Jn("width"),Kn("buttons",fn(EF)),ue("onAction",$),ue("onCancel",$),ue("onClose",$),ue("onMessage",$)]),DF=function(t){return k(t)?[t].concat(U(At(t),DF)):h(t)?U(t,DF):[]},AF=function(t){return S(t.type)&&S(t.name)},MF={checkbox:OM,colorinput:MM,colorpicker:IM,dropzone:NM,input:$M,iframe:XM,sizeinput:sF,selectbox:uF,listbox:oF,size:sF,textarea:gF,urlinput:vF,customeditor:HM,collection:DM,togglemenuitem:mM},FF=function(t){var n=H(DF(t),AF),e=U(n,function(n){return t=n,st.from(MF[t.type]).fold(function(){return[]},function(t){return[Ln(n.name,t)]});var t});return ln(e)},IF=function(t){return{internalDialog:_n(On("dialog",TF,t)),dataValidator:FF(t),initialData:t.initialData}},RF={open:function(t,n){var e=IF(n);return t(e.internalDialog,e.initialData,e.dataValidator)},openUrl:function(t,n){return t(_n(On("dialog",BF,n)))},redial:IF},VF=function(t){var e=[],o={};return Ot(t,function(t,n){t.fold(function(){e.push(n)},function(t){o[n]=t})}),0<e.length?it.error(e):it.value(o)},PF=Uf({name:"TabButton",configFields:[te("uid",undefined),Nn("value"),vn("dom","dom",Xt(function(){return{attributes:{role:"tab",id:Xr("aria"),"aria-selected":"false"}}}),An()),qn("action"),te("domModification",{}),Pl("tabButtonBehaviours",[dg,ng,Vl]),Nn("view")],factory:function(t,n){return{uid:t.uid,dom:t.dom,components:t.components,events:_g(t.action),behaviours:zl(t.tabButtonBehaviours,[dg.config({}),ng.config({mode:"execution",useSpace:!0,useEnter:!0}),Vl.config({store:{mode:"memory",initialValue:t.value}})]),domModification:t.domModification}}}),HF=at([Nn("tabs"),Nn("dom"),te("clickToDismiss",!1),Pl("tabbarBehaviours",[ud,ng]),ca(["tabClass","selectedClass"])]),zF=hf({factory:PF,name:"tabs",unit:"tab",overrides:function(o){var r=function(t,n){ud.dehighlight(t,n),Go(t,Lo(),{tabbar:t,button:n})},i=function(t,n){ud.highlight(t,n),Go(t,No(),{tabbar:t,button:n})};return{action:function(t){var n=t.getSystem().getByUid(o.uid).getOrDie(),e=ud.isHighlighted(n,t);(e&&o.clickToDismiss?r:e?$:i)(n,t)},domModification:{classes:[o.markers.tabClass]}}}}),NF=at([zF]),LF=Wf({name:"Tabbar",configFields:HF(),partFields:NF(),factory:function(t,n,e,o){return{uid:t.uid,dom:t.dom,components:n,"debug.sketcher":"Tabbar",domModification:{attributes:{role:"tablist"}},behaviours:zl(t.tabbarBehaviours,[ud.config({highlightClass:t.markers.selectedClass,itemClass:t.markers.tabClass,onHighlight:function(t,n){Vr(n.element,"aria-selected","true")},onDehighlight:function(t,n){Vr(n.element,"aria-selected","false")}}),ng.config({mode:"flow",getInitial:function(t){return ud.getHighlighted(t).map(function(t){return t.element})},selector:"."+t.markers.tabClass,executeOnMove:!0})])}}}),jF=Uf({name:"Tabview",configFields:[Pl("tabviewBehaviours",[ug])],factory:function(t,n){return{uid:t.uid,dom:t.dom,behaviours:zl(t.tabviewBehaviours,[ug.config({})]),domModification:{attributes:{role:"tabpanel"}}}}}),UF=at([te("selectFirst",!0),la("onChangeTab"),la("onDismissTab"),te("tabs",[]),Pl("tabSectionBehaviours",[])]),WF=mf({factory:LF,schema:[Nn("dom"),Gn("markers",[Nn("tabClass"),Nn("selectedClass")])],name:"tabbar",defaults:function(t){return{tabs:t.tabs}}}),GF=mf({factory:jF,name:"tabview"}),XF=at([WF,GF]),YF=Wf({name:"TabSection",configFields:UF(),partFields:XF(),factory:function(i,t,n,e){var o=function(t,n){Tf(t,i,"tabbar").each(function(t){n(t).each(Xo)})};return{uid:i.uid,dom:i.dom,components:t,behaviours:Hl(i.tabSectionBehaviours),events:Jo(rt([i.selectFirst?[ur(function(t,n){o(t,ud.getFirst)})]:[],[Zo(No(),function(t,n){var o,r,e=n.event.button;o=e,r=Vl.getValue(o),Tf(o,i,"tabview").each(function(e){L(i.tabs,function(t){return t.value===r}).each(function(t){var n=t.view();Hr(o.element,"id").each(function(t){Vr(e.element,"aria-labelledby",t)}),ug.set(e,n),i.onChangeTab(e,o,n)})})}),Zo(Lo(),function(t,n){var e=n.event.button;i.onDismissTab(t,e)})]])),apis:{getViewItems:function(t){return Tf(t,i,"tabview").map(function(t){return ug.contents(t)}).getOr([])},showTab:function(t,e){o(t,function(n){var t=ud.getCandidates(n);return L(t,function(t){return Vl.getValue(t)===e}).filter(function(t){return!ud.isHighlighted(n,t)})})}}}},apis:{getViewItems:function(t,n){return t.getViewItems(n)},showTab:function(t,n,e){t.showTab(n,e)}}}),qF=function(t,n){Wi(t,"height",n+"px"),ze().browser.isIE()?$i(t,"flex-basis"):Wi(t,"flex-basis",n+"px")},KF=function(t,m,n){Wu(t,'[role="dialog"]').each(function(d){Gu(d,'[role="tablist"]').each(function(f){n.get().map(function(t){return Wi(m,"height","0"),Wi(m,"flex-basis","0"),Math.min(t,(e=m,o=f,r=br(n=d).dom,i=Wu(n,".tox-dialog-wrap").getOr(n),u="fixed"===Yi(i,"position")?Math.max(r.clientHeight,window.innerHeight):Math.max(r.offsetHeight,r.scrollHeight),a=gu(e),c=e.dom.offsetLeft>=o.dom.offsetLeft+Su(o)?Math.max(gu(o),a):a,s=parseInt(Yi(n,"margin-top"),10)||0,l=parseInt(Yi(n,"margin-bottom"),10)||0,u-(gu(n)+s+l-c)));var n,e,o,r,i,u,a,c,s,l}).each(function(t){qF(m,t)})})})},JF=function(t){return Gu(t,'[role="tabpanel"]')},$F=function(a){var c;return{smartTabHeight:(c=se(st.none()),{extraEvents:[ur(function(t){var e=t.element;JF(e).each(function(u){var n;Wi(u,"visibility","hidden"),t.getSystem().getByDom(u).toOptional().each(function(t){var o,r,i,n=(r=u,i=t,V(o=a,function(t,n){ug.set(i,o[n].view());var e=r.dom.getBoundingClientRect();return ug.set(i,[]),e.height})),e=q(Y(n,function(t,n){return n<t?-1:t<n?1:0}));c.set(e)}),KF(e,u,c),$i(u,"visibility"),n=t,q(a).each(function(t){return YF.showTab(n,t.value)}),ap.requestAnimationFrame(function(){KF(e,u,c)})})}),Zo(Fo(),function(t){var n=t.element;JF(n).each(function(t){KF(n,t,c)})}),Zo(Wy,function(t,n){var r=t.element;JF(r).each(function(n){var t=lc();Wi(n,"visibility","hidden");var e=Ki(n,"height").map(function(t){return parseInt(t,10)});$i(n,"height"),$i(n,"flex-basis");var o=n.dom.getBoundingClientRect().height;e.forall(function(t){return t<o})?(c.set(st.from(o)),KF(r,n,c)):e.each(function(t){qF(n,t)}),$i(n,"visibility"),t.each(sc)})})],selectFirst:!1}),naiveTabHeight:{extraEvents:[],selectFirst:!0}}},QF="send-data-to-section",ZF="send-data-to-view",tI=Xr("update-dialog"),nI=Xr("update-title"),eI=Xr("update-body"),oI=Xr("update-footer"),rI=Xr("body-send-message"),iI=function(t,n,d,e){return{dom:{tag:"div",classes:["tox-dialog__content-js"],attributes:nt(nt({},n.map(function(t){return{id:t}}).getOr({})),e?{"aria-live":"polite"}:{})},components:[],behaviours:nc([HS(0),LT.config({channel:eI,updateState:function(t,n){return st.some({isTabPanel:function(){return"tabpanel"===n.body.type}})},renderComponents:function(t){switch(t.body.type){case"tabpanel":return[(r=t.body,i=d,u=se({}),a=function(t){var n=Vl.getValue(t),e=VF(n).getOr({}),o=u.get(),r=Ht(o,e);u.set(r)},c=function(t){var n=u.get();Vl.setValue(t,n)},s=se(null),l=V(r.tabs,function(t){return{value:t.name,dom:{tag:"div",classes:["tox-dialog__body-nav-item"],innerHtml:i.shared.providers.translate(t.title)},view:function(){return[ES.sketch(function(n){return{dom:{tag:"div",classes:["tox-form"]},components:V(t.items,function(t){return GO(n,t,i)}),formBehaviours:nc([ng.config({mode:"acyclic",useTabstopAt:x(ek)}),ag("TabView.form.events",[ur(c),ar(a)]),ac.config({channels:Jt([{key:QF,value:{onReceive:a}},{key:ZF,value:{onReceive:c}}])})])}})]}}}),f=$F(l).smartTabHeight,YF.sketch({dom:{tag:"div",classes:["tox-dialog__body"]},onChangeTab:function(t,n,e){var o=Vl.getValue(n);Go(t,Uy,{name:o,oldName:s.get()}),s.set(o)},tabs:l,components:[YF.parts.tabbar({dom:{tag:"div",classes:["tox-dialog__body-nav"]},components:[LF.parts.tabs({})],markers:{tabClass:"tox-tab",selectedClass:"tox-dialog__body-nav-item--active"},tabbarBehaviours:nc([Ay.config({})])}),YF.parts.tabview({dom:{tag:"div",classes:["tox-dialog__body-content"]}})],selectFirst:f.selectFirst,tabSectionBehaviours:nc([ag("tabpanel",f.extraEvents),ng.config({mode:"acyclic"}),Kf.config({find:function(t){return q(YF.getViewItems(t))}}),Vl.config({store:{mode:"manual",getValue:function(t){return t.getSystem().broadcastOn([QF],{}),u.get()},setValue:function(t,n){u.set(n),t.getSystem().broadcastOn([ZF],{})}}})])}))];default:return[(e=t.body,o=d,{dom:{tag:"div",classes:["tox-dialog__body"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-content"]},components:[(n=sp(ES.sketch(function(n){return{dom:{tag:"div",classes:["tox-form"].concat(e.classes)},components:V(e.items,function(t){return GO(n,t,o)})}}))).asSpec()]}],behaviours:nc([ng.config({mode:"acyclic",useTabstopAt:x(ek)}),PS(n),GS(n,{postprocess:function(t){return VF(t).fold(function(t){return console.error(t),{}},function(t){return t})}})])})]}var e,o,n,r,i,u,a,c,s,l,f},initialData:t})])}},uI=Hv.deviceType.isTouch(),aI=function(t,n){return{dom:{tag:"div",styles:{display:"none"},classes:["tox-dialog__header"]},components:[t,n]}},cI=function(t,n){return fM.parts.close(cp.sketch({dom:{tag:"button",classes:["tox-button","tox-button--icon","tox-button--naked"],attributes:{type:"button","aria-label":n.translate("Close")}},action:t,buttonBehaviours:nc([Ay.config({})])}))},sI=function(){return fM.parts.title({dom:{tag:"div",classes:["tox-dialog__title"],innerHtml:"",styles:{display:"none"}}})},lI=function(t,n){return fM.parts.body({dom:{tag:"div",classes:["tox-dialog__body"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-content"]},components:[{dom:ZB("<p>"+n.translate(t)+"</p>")}]}]})},fI=function(t){return fM.parts.footer({dom:{tag:"div",classes:["tox-dialog__footer"]},components:t})},dI=function(t,n){return[Cy.sketch({dom:{tag:"div",classes:["tox-dialog__footer-start"]},components:t}),Cy.sketch({dom:{tag:"div",classes:["tox-dialog__footer-end"]},components:n})]},mI=function(n){var t,e="tox-dialog",o=e+"-wrap",r=o+"__backdrop",i=e+"__disable-scroll";return fM.sketch({lazySink:n.lazySink,onEscape:function(t){return n.onEscape(t),st.some(!0)},useTabstopAt:function(t){return!ek(t)},dom:{tag:"div",classes:[e].concat(n.extraClasses),styles:nt({position:"relative"},n.extraStyles)},components:w([n.header,n.body],n.footer.toArray()),parts:{blocker:{dom:ZB('<div class="'+o+'"></div>'),components:[{dom:{tag:"div",classes:uI?[r,r+"--opaque"]:[r]}}]}},dragBlockClass:o,modalBehaviours:nc(w([dg.config({}),ag("dialog-events",n.dialogEvents.concat([ir(ao(),function(t,n){ng.focusIn(t)})])),ag("scroll-lock",[ur(function(){Oi(Ni(),i)}),ar(function(){Ti(Ni(),i)})])],n.extraBehaviours)),eventOrder:nt(((t={})[ko()]=["dialog-events"],t[Io()]=["scroll-lock","dialog-events","alloy.base.behaviour"],t[Ro()]=["alloy.base.behaviour","dialog-events","scroll-lock"],t),n.eventOrder)})},gI=function(t){return cp.sketch({dom:{tag:"button",classes:["tox-button","tox-button--icon","tox-button--naked"],attributes:{type:"button","aria-label":t.translate("Close"),title:t.translate("Close")}},components:[{dom:{tag:"div",classes:["tox-icon"],innerHtml:fp("close",t.icons)}}],action:function(t){Wo(t,Hy)}})},pI=function(t,n,e){var o=function(t){return[au(e.translate(t.title))]};return{dom:{tag:"div",classes:["tox-dialog__title"],attributes:nt({},n.map(function(t){return{id:t}}).getOr({}))},components:o(t),behaviours:nc([LT.config({channel:nI,renderComponents:o})])}},hI=function(){return{dom:ZB('<div class="tox-dialog__draghandle"></div>')}},vI=function(t,n){return e={title:n.shared.providers.translate(t),draggable:n.dialog.isDraggableModal()},o=n.shared.providers,r=fM.parts.title(pI(e,st.none(),o)),i=fM.parts.draghandle(hI()),u=fM.parts.close(gI(o)),a=[r].concat(e.draggable?[i]:[]).concat([u]),Cy.sketch({dom:ZB('<div class="tox-dialog__header"></div>'),components:a});var e,o,r,i,u,a},bI=function(t,n){return{onClose:function(){return n.closeWindow()},onBlock:function(e){fM.setBusy(t(),function(t,n){return{dom:{tag:"div",classes:["tox-dialog__busy-spinner"],attributes:{"aria-label":e.message},styles:{left:"0px",right:"0px",bottom:"0px",top:"0px",position:"absolute"}},behaviours:n,components:[{dom:ZB('<div class="tox-spinner"><div></div><div></div><div></div></div>')}]}})},onUnblock:function(){fM.setIdle(t())}}},yI=function(t,n,e,o){var r;return lu(mI(nt(nt({},t),{lazySink:o.shared.getSink,extraBehaviours:w([LT.config({channel:tI,updateState:function(t,n){return st.some(n)},initialData:n}),qS({})],t.extraBehaviours),onEscape:function(t){Wo(t,Hy)},dialogEvents:e,eventOrder:((r={})[So()]=["reflecting","receiving"],r[Io()]=["scroll-lock","reflecting","messages","dialog-events","alloy.base.behaviour"],r[Ro()]=["alloy.base.behaviour","dialog-events","messages","reflecting","scroll-lock"],r)})))},xI=function(t){return V(t,function(t){return"menu"===t.type?(e=V((n=t).items,function(t){var n=se(!1);return nt(nt({},t),{storage:n})}),nt(nt({},n),{items:e})):t;var n,e})},wI=function(t){return N(t,function(t,n){return"menu"!==n.type?t:N(n.items,function(t,n){return t[n.name]=n.storage,t},t)},{})},SI=function(t,e){return[or(ao(),nk),t(Py,function(t,n){e.onClose(),n.onClose()}),t(Hy,function(t,n,e,o){n.onCancel(t),Wo(o,Py)}),Zo(jy,function(t,n){return e.onUnblock()}),Zo(Ly,function(t,n){return e.onBlock(n.event)})]},kI=function(i,t){var n=function(t,r){return Zo(t,function(e,o){u(e,function(t,n){r(i(),t,o.event,e)})})},u=function(n,e){LT.getState(n).get().each(function(t){e(t,n)})};return w(SI(n,t),[n(zy,function(t,n,e){n.onAction(t,{name:e.name})})])},CI=function(i,t,c){var n=function(t,r){return Zo(t,function(e,o){u(e,function(t,n){r(i(),t,o.event,e)})})},u=function(n,e){LT.getState(n).get().each(function(t){e(t.internalDialog,n)})};return w(SI(n,t),[n(Ny,function(t,n){return n.onSubmit(t)}),n(Vy,function(t,n,e){n.onChange(t,{name:e.name})}),n(zy,function(t,n,e,o){var r=function(){return ng.focusIn(o)},i=function(t){return zr(t,"disabled")||Hr(t,"aria-disabled").exists(function(t){return"true"===t})},u=Ri(o.element),a=lc(u);n.onAction(t,{name:e.name,value:e.value}),lc(u).fold(r,function(n){i(n)||a.exists(function(t){return We(n,t)&&i(t)})?r():c().toOptional().filter(function(t){return!We(t.element,n)}).each(r)})}),n(Uy,function(t,n,e){n.onTabChange(t,{newTabName:e.name,oldTabName:e.oldName})}),ar(function(t){var n=i();Vl.setValue(t,n.getData())})])},OI=function(t,n){var e=n.map(function(t){return t.footerButtons}).getOr([]),o=P(e,function(t){return"start"===t.align}),r=function(t,n){return Cy.sketch({dom:{tag:"div",classes:["tox-dialog__footer-"+t]},components:V(n,function(t){return t.memento.asSpec()})})};return[r("start",o.pass),r("end",o.fail)]},_I=function(t,i){return{dom:ZB('<div class="tox-dialog__footer"></div>'),components:[],behaviours:nc([LT.config({channel:oI,initialData:t,updateState:function(t,n){var r=V(n.buttons,function(t){var n,e,o=sp((e=i,AC(n=t,n.type,e)));return{name:t.name,align:t.align,memento:o}});return st.some({lookupByName:function(t,n){return e=t,o=n,L(r,function(t){return t.name===o}).bind(function(t){return t.memento.getOpt(e)});var e,o},footerButtons:r})},renderComponents:OI})])}},TI=_I,EI=function(t,n){return fM.parts.footer(_I(t,n))},BI=function(n,e){if(n.getRoot().getSystem().isConnected()){var o=Kf.getCurrent(n.getFormWrapper()).getOr(n.getFormWrapper());return ES.getField(o,e).fold(function(){var t=n.getFooter();return LT.getState(t).get().bind(function(t){return t.lookupByName(o,e)})},function(t){return st.some(t)})}return st.none()},DI=function(c,o,s){var t=function(t){var n=c.getRoot();n.getSystem().isConnected()&&t(n)},l={getData:function(){var t=c.getRoot(),n=t.getSystem().isConnected()?c.getFormWrapper():t,e=Vl.getValue(n),o=_t(s,function(t){return t.get()});return nt(nt({},e),o)},setData:function(a){t(function(t){var n,e,o=l.getData(),r=nt(nt({},o),a),i=(n=r,e=c.getRoot(),LT.getState(e).get().map(function(t){return _n(On("data",t.dataValidator,n))}).getOr(n)),u=c.getFormWrapper();Vl.setValue(u,i),Ot(s,function(t,n){Ft(r,n)&&t.set(r[n])})})},disable:function(t){BI(c,t).each(tv.disable)},enable:function(t){BI(c,t).each(tv.enable)},focus:function(t){BI(c,t).each(dg.focus)},block:function(n){if(!S(n))throw new Error("The dialogInstanceAPI.block function should be passed a blocking message of type string as an argument");t(function(t){Go(t,Ly,{message:n})})},unblock:function(){t(function(t){Wo(t,jy)})},showTab:function(e){t(function(t){var n=c.getBody();LT.getState(n).get().exists(function(t){return t.isTabPanel()})&&Kf.getCurrent(n).each(function(t){YF.showTab(t,e)})})},redial:function(e){t(function(t){var n=o(e);t.getSystem().broadcastOn([tI],n),t.getSystem().broadcastOn([nI],n.internalDialog),t.getSystem().broadcastOn([eI],n.internalDialog),t.getSystem().broadcastOn([oI],n.internalDialog),l.setData(n.initialData)})},close:function(){t(function(t){Wo(t,Py)})}};return l},AI=function(t,n,e){var o,r,i,u=vI(t.internalDialog.title,e),a=(o={body:t.internalDialog.body},r=e,i=iI(o,st.none(),r,!1),fM.parts.body(i)),c=xI(t.internalDialog.buttons),s=wI(c),l=EI({buttons:c},e),f=CI(function(){return p},bI(function(){return g},n),e.shared.getSink),d="normal"!==t.internalDialog.size?"large"===t.internalDialog.size?["tox-dialog--width-lg"]:["tox-dialog--width-md"]:[],m={header:u,body:a,footer:st.some(l),extraClasses:d,extraBehaviours:[],extraStyles:{}},g=yI(m,t,f,e),p=DI({getRoot:function(){return g},getBody:function(){return fM.getBody(g)},getFooter:function(){return fM.getFooter(g)},getFormWrapper:function(){var t=fM.getBody(g);return Kf.getCurrent(t).getOr(t)}},n.redial,s);return{dialog:g,instanceApi:p}},MI=function(t,n,e,o){var r,i,u,a,c,s,l,f,d,m=Xr("dialog-label"),g=Xr("dialog-content"),p=sp((u={title:t.internalDialog.title,draggable:!0},a=m,c=e.shared.providers,Cy.sketch({dom:ZB('<div class="tox-dialog__header"></div>'),components:[pI(u,st.some(a),c),hI(),gI(c)],containerBehaviours:nc([JA.config({mode:"mouse",blockerClass:"blocker",getTarget:function(t){return Xu(t,'[role="dialog"]').getOrDie()},snaps:{getSnapPoints:function(){return[]},leftAttr:"data-drag-left",topAttr:"data-drag-top"}})])}))),h=sp((s={body:t.internalDialog.body},l=g,f=e,d=o,iI(s,st.some(l),f,d))),v=xI(t.internalDialog.buttons),b=wI(v),y=sp(TI({buttons:v},e)),x=CI(function(){return S},{onBlock:function(){},onUnblock:function(){},onClose:function(){return n.closeWindow()}},e.shared.getSink),w=lu({dom:{tag:"div",classes:["tox-dialog","tox-dialog-inline"],attributes:((r={role:"dialog"})["aria-labelledby"]=m,r["aria-describedby"]=""+g,r)},eventOrder:((i={})[So()]=[LT.name(),ac.name()],i[ko()]=["execute-on-form"],i[Io()]=["reflecting","execute-on-form"],i),behaviours:nc([ng.config({mode:"cyclic",onEscape:function(t){return Wo(t,Py),st.some(!0)},useTabstopAt:function(t){return!ek(t)&&("button"!==lr(t)||"disabled"!==Pr(t,"disabled"))}}),LT.config({channel:tI,updateState:function(t,n){return st.some(n)},initialData:t}),dg.config({}),ag("execute-on-form",x.concat([ir(ao(),function(t,n){ng.focusIn(t)})])),qS({})]),components:[p.asSpec(),h.asSpec(),y.asSpec()]}),S=DI({getRoot:function(){return w},getFooter:function(){return y.get(w)},getBody:function(){return h.get(w)},getFormWrapper:function(){var t=h.get(w);return Kf.getCurrent(t).getOr(t)}},n.redial,b);return{dialog:w,instanceApi:S}},FI=tinymce.util.Tools.resolve("tinymce.util.URI"),II=["insertContent","setContent","execCommand","close","block","unblock"],RI=function(t){return k(t)&&-1!==II.indexOf(t.mceAction)},VI=function(o,t,r,n){var e,i,u,a,c=vI(o.title,n),s=(i={dom:{tag:"div",classes:["tox-dialog__content-js"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-iframe"]},components:[ZS({dom:{tag:"iframe",attributes:{src:o.url}},behaviours:nc([Ay.config({}),dg.config({})])})]}],behaviours:nc([ng.config({mode:"acyclic",useTabstopAt:x(ek)})])},fM.parts.body(i)),l=o.buttons.bind(function(t){return 0===t.length?st.none():st.some(EI({buttons:t},n))}),f=kI(function(){return y},bI(function(){return b},t)),d=nt(nt({},o.height.fold(function(){return{}},function(t){return{height:t+"px","max-height":t+"px"}})),o.width.fold(function(){return{}},function(t){return{width:t+"px","max-width":t+"px"}})),m=o.width.isNone()&&o.height.isNone()?["tox-dialog--width-lg"]:[],g=new FI(o.url,{base_uri:new FI(window.location.href)}),p=g.protocol+"://"+g.host+(g.port?":"+g.port:""),h=se(st.none()),v=[ag("messages",[ur(function(){var t=ty(fe.fromDom(window),"message",function(t){var n,e;g.isSameOrigin(new FI(t.raw.origin))&&(n=t.raw.data,RI(n)?function(t,n,e){switch(e.mceAction){case"insertContent":t.insertContent(e.content);break;case"setContent":t.setContent(e.content);break;case"execCommand":var o=!!C(e.ui)&&e.ui;t.execCommand(e.cmd,o,e.value);break;case"close":n.close();break;case"block":n.block(e.message);break;case"unblock":n.unblock()}}(r,y,n):!RI(e=n)&&k(e)&&Ft(e,"mceAction")&&o.onMessage(y,n))});h.set(st.some(t))}),ar(function(){h.get().each(function(t){return t.unbind()})})]),ac.config({channels:((e={})[rI]={onReceive:function(t,n){Gu(t.element,"iframe").each(function(t){t.dom.contentWindow.postMessage(n,p)})}},e)})],b=yI({header:c,body:s,footer:l,extraClasses:m,extraBehaviours:v,extraStyles:d},o,f,n),y=(a=function(t){u.getSystem().isConnected()&&t(u)},{block:function(n){if(!S(n))throw new Error("The urlDialogInstanceAPI.block function should be passed a blocking message of type string as an argument");a(function(t){Go(t,Ly,{message:n})})},unblock:function(){a(function(t){Wo(t,jy)})},close:function(){a(function(t){Wo(t,Py)})},sendMessage:function(n){a(function(t){t.getSystem().broadcastOn([rI],n)})}});return{dialog:u=b,instanceApi:y}},PI=function(t){var c,s,l,f,p=t.backstage,h=t.editor,v=wv(h),e=(s=(c=t).backstage.shared,{open:function(t,n){var e=function(){fM.hide(u),n()},o=sp(AC({name:"close-alert",text:"OK",primary:!0,align:"end",disabled:!1,icon:st.none()},"cancel",c.backstage)),r=sI(),i=cI(e,s.providers),u=lu(mI({lazySink:function(){return s.getSink()},header:aI(r,i),body:lI(t,s.providers),footer:st.some(fI(dI([],[o.asSpec()]))),onEscape:e,extraClasses:["tox-alert-dialog"],extraBehaviours:[],extraStyles:{},dialogEvents:[Zo(Hy,e)],eventOrder:{}}));fM.show(u);var a=o.get(u);dg.focus(a)}}),o=(f=(l=t).backstage.shared,{open:function(t,n){var e=function(t){fM.hide(a),n(t)},o=sp(AC({name:"yes",text:"Yes",primary:!0,align:"end",disabled:!1,icon:st.none()},"submit",l.backstage)),r=AC({name:"no",text:"No",primary:!1,align:"end",disabled:!1,icon:st.none()},"cancel",l.backstage),i=sI(),u=cI(function(){return e(!1)},f.providers),a=lu(mI({lazySink:function(){return f.getSink()},header:aI(i,u),body:lI(t,f.providers),footer:st.some(fI(dI([],[r,o.asSpec()]))),onEscape:function(){return e(!1)},extraClasses:["tox-confirm-dialog"],extraBehaviours:[],extraStyles:{},dialogEvents:[Zo(Hy,function(){return e(!1)}),Zo(Ny,function(){return e(!0)})],eventOrder:{}}));fM.show(a);var c=o.get(a);dg.focus(c)}}),r=function(t,e){return RF.openUrl(function(t){var n=VI(t,{closeWindow:function(){fM.hide(n.dialog),e(n.instanceApi)}},h,p);return fM.show(n.dialog),n.instanceApi},t)},i=function(t,i){return RF.open(function(t,n,e){var o=n,r=AI({dataValidator:e,initialData:o,internalDialog:t},{redial:RF.redial,closeWindow:function(){fM.hide(r.dialog),i(r.instanceApi)}},p);return fM.show(r.dialog),r.instanceApi.setData(o),r.instanceApi},t)},u=function(t,d,m,g){return RF.open(function(t,n,e){var o,r,i,u=_n(On("data",e,n)),a=(o=se(st.none()),{clear:function(){return o.set(st.none())},set:function(t){return o.set(st.some(t))},isSet:function(){return o.get().isSome()},on:function(t){return o.get().each(t)}}),c=p.shared.header.isPositionedAtTop(),s=function(){return a.on(function(t){Kg.reposition(t),DB.refresh(t)})},l=MI({dataValidator:e,initialData:u,internalDialog:t},{redial:RF.redial,closeWindow:function(){a.on(Kg.hide),h.off("ResizeEditor",s),a.clear(),m(l.instanceApi)}},p,g),f=lu(Kg.sketch(nt(nt({lazySink:p.shared.getSink,dom:{tag:"div",classes:[]},fireDismissalEventInstead:{}},c?{}:{fireRepositionEventInstead:{}}),{inlineBehaviours:nc(w([ag("window-manager-inline-events",[Zo(Vo(),function(t,n){Wo(l.dialog,Hy)})])],(r=h,i=c,v&&i?[]:[DB.config({contextual:{lazyContext:function(){return st.some(Ru(fe.fromDom(r.getContentAreaContainer())))},fadeInClass:"tox-dialog-dock-fadein",fadeOutClass:"tox-dialog-dock-fadeout",transitionClass:"tox-dialog-dock-transition"},modes:["top"]})]))),isExtraPart:function(t,n){return oy(e=n,".tox-alert-dialog")||oy(e,".tox-confirm-dialog");var e}})));return a.set(f),Kg.showWithin(f,d,fu(l.dialog),st.some(Ni())),v&&c||(DB.refresh(f),h.on("ResizeEditor",s)),l.instanceApi.setData(u),ng.focusIn(l.dialog),l.instanceApi},t)};return{open:function(t,n,e){return n!==undefined&&"toolbar"===n.inline?u(t,p.shared.anchors.inlineDialog(),e,n.ariaAttrs):n!==undefined&&"cursor"===n.inline?u(t,p.shared.anchors.cursor(),e,n.ariaAttrs):i(t,e)},openUrl:function(t,n){return r(t,n)},alert:function(t,n){e.open(t,function(){n()})},close:function(t){t.close()},confirm:function(t,n){o.open(t,function(t){n(t)})}}};t.add("silver",function(t){var n=aM(t),e=n.uiMothership,o=n.backstage,r=n.renderUI,i=n.getUi;Qb(t,o.shared);var u=PI({editor:t,backstage:o});return{renderUI:r,getWindowManagerImpl:at(u),getNotificationManagerImpl:function(){return gp(0,{backstage:o},e)},ui:i()}})}();