<?php

return [
    'IsFirstActiveOrRepaidLoan' => 'Първи активен/изплатен заем.',
    'IsFirstLoanEver' => 'Първи заем.',
    'SpecialPaymentAgreement' => 'Специална уговорка за плащане',
    'VeriffStats' => 'Статистики верификация',
    'VeriffStatsSkip' => 'Пропускане на верификация',
    'SkippedVerification' => 'Пропусната верификация',
    'VeriffStatsStart' => 'Започнати верификации',
    'VeriffStatsFinished' => 'Приключени верефикации',
    'VeriffProcessedAt' => 'Дата на верификация',
    'ReportDuplicateLoansByEmail' => 'Дублирани заявки по имейл',
    'ReportDuplicateLoansByPhone' => 'Дублирани заявки по телефон',
    'DuplicateLoans' => 'Дублирани заявки по телефон и имейл',
    'SavedFilters' => 'Запазени филтри',
    'TotalRows' => 'Общо',
    'OfferWithoutCredit' => 'Предложение без кредит',
    'DisableCreditLimit' => 'Изключи кредитен лимит',
    'BlockingAndUnblockingHistory' => 'История на блокирания.',
    'ClientManualCreditLimitRule' => 'Кредитен лимит за клиента.',
    'WillBeExitedAutomatic' => 'Ще бъде изваден автоматично',
    'WasExitedOn' => 'Беше изваден от черен списък.',
    'ToManualExit' => 'До ръчно изваждане',
    'BlackListBy' => 'Блокиран от',
    'BlackListFrom' => 'В черен списък от дата',
    'BlackListТо' => 'В черен списък до дата',
    'DateFromTo' => 'Дата от - до',
    'AddLoanToOuterCollector' => 'Добави заем за външно събиране',
    'PaymentDetails' => 'Данни за плащането',
    'OuterCollectorFromDate' => 'Дата на листване',
    'OuterCollectorBy' => 'Добавен от',
    'ForOuterCollector' => 'За външно събиране',
    'CompanyData' => 'Данни на фирмата',
    'CompanyAddress' => 'Адрес на фирмата',
    'LoanDistribution' => 'Разбивка по кредита',
    'DailyReports' => 'Отчети',
    'AvailableBoxes' => 'Налични кутии в клиентска карта',
    'PreApprovedClients' => 'Предварително одобрени клиенти',
    'InterestFreeOffers' => 'Безлихвени оферти',
    'AllLoans' => 'Всички заеми',
    'FinalAgent' => 'Последен агент',
    'FirstProcessingTime' => 'Време на обработка',
    'FirstWaitingTime' => 'Очакване за обработка',
    'FirstDecisionReason' => 'Първа причина за решение',
    'FirstDecision' => 'Първо решение',
    'FirstProcessingEndAt' => 'Първо завършване на обработка',
    'FirstProcessingStartAt' => 'Първо стартиране за обработка',
    'FirstAgent' => 'Първи агент',
    'LoanSignedAt' => 'Дата на подписване',
    'LoanCreatedAt' => 'Лоан дата на създаване',
    'RequestCreatedAt' => 'Дата на създаване',
    'Statistics' => 'Статистики',
    'UnclaimedMoney' => 'Непотърсени пари',
    'PaymentTasks' => 'Задачи за плащане',
    'Yes' => 'Да',
    'No' => 'Не',
    'ContactPhoneNumber' => 'Тел: (лице за контакт)',
    'NotAvailable' => 'N/A',
    'Days' => 'Дни',
    'Options' => 'Опции',
    'Status' => 'Статус',
    'Actions' => 'Действия',
    'Action' => 'Действие',
    'Description' => 'Описание',
    'Default' => 'По подразбиране',
    'CreatedAt' => 'Създаден на',
    'applicationDate' => 'Дата на Заявка',
    'UpdatedAt' => 'Променен на',
    'CreatedBy' => 'Създал',
    'UpdatedBy' => 'Променен от',
    'UpdatedBy2' => 'Редактирал',
    'Email' => 'Имейл',
    'Phone' => 'Телефон',
    'Phone2' => 'Втори телефон',
    'clientPhone' => 'Клиент телефон',
    'clientNames' => 'Имена на Клиента',
    'Fax' => 'Факс',
    'Trans' => 'Превод',
    'TransactionsDelay' => 'Закъснение на транзакции',
    'startWorkAt' => 'Започва работа',
    'endWorkAt' => 'Приключва работа',
    'PhoneAdditional' => 'Доп. телефон',
    'Name' => 'Име',
    'Username' => 'Username',
    'ClientCardBoxToGroup' => 'Групи в които ще се показва този бокс',
    'FilterByBoxGroup' => 'Филтер по група',
    'TradeName' => 'Търговско име',
    'Overdue' => 'Просрочие',
    'InOverdue' => 'В просрочие',
    'OverdueAmount' => 'Просрочена сума',
    'InstallmentsOverdue' => 'Просрочени вноски',
    'OverdueDays' => 'Дни просочие',
    'OverdueDaysFrom' => 'Дни просочие от',
    'OverdueDaysTo' => 'Дни просочие до',
    'PaymentDaysFrom' => 'Дни на последно плащане от',
    'PaymentDaysTo' => 'Дни на последно плащане до',
    'PastDays' => 'Минали дни',
    'StartDate' => 'Начало',
    'FinalRepaidDate' => 'Край',
    'Bucket' => 'Бъкет',
    'From' => 'от',
    'To' => 'до',
    'StatusHistory' => 'История на статусите',
    'Task' => 'Задача',
    'LoanHistory' => 'История на кредита',
    'ClientHistory' => 'История на клиента',
    'History' => 'История',
    'Field' => 'Поле',
    'ValueFrom' => 'Стара стойност',
    'ValueTo' => 'Нова стойност',
    'Score' => 'Точки',
    'MiddleName' => 'Презиме',
    'LastName' => 'Фамилия',
    'FirstNameLatin' => 'Име лат',
    'MiddleNameLatin' => 'Презиме лат',
    'LastNameLatin' => 'Фамилия лат',
    'Address' => 'Адрес',
    'ClientAddress' => 'Клиент текущ адрес',
    'ClientProfile' => 'Клиентски профил',
    'PermanentAddress' => 'Постоянен адрес',
    'CurrentAddress' => 'Настоящ адрес',
    'CurrentCity' => 'Настоящ град',
    'CurrentPostCode' => 'Пощенски Код',
    'DifferentAddress' => 'Различен Адрес',
    'Password' => 'Парола',
    'Branch' => 'Клон',
    'Active' => 'Активен',
    'Blocked' => 'Блокиран',
    'Deleted' => 'Изтрит',
    'DeletedData' => 'Изтрити данни',
    'NotActive' => 'Неактивен',
    'Canceled' => 'Отказано',
    'ReceivedNotDelivered' => 'Получено/Неразнесено',
    'Priority' => 'Приоритет',
    'DefaultValue' => 'Стойност по подразбиране',
    'FilterById' => 'ИД',
    'FilterByPin' => 'ЕГН',
    'FilterByName' => 'Име',
    'FilterByCode' => 'Код',
    'FilterByProductCode' => 'Продукт код',
    'FilterByEmail' => 'Имейл',
    'FilterByPhone' => 'Телефон',
    'FilterByCreatedAt' => 'Дата',
    'FilterByDateCreated' => 'Дата на Създаване',
    'FilterByDateSent' => 'Дата на изпращане',
    'FilterByDateReceived' => 'Дата на получаване',
    'FilterByUpdatedAt' => 'Променен на',
    'FilterByLastUpdated' => 'Последна Редакция',
    'FilterByBranch' => 'Клон',
    'FilterByBlocked' => 'Блокирани',
    'FilterByDeleted' => 'Изтрити',
    'FilterByLoanStatus' => 'Статус',
    'FilterByJuridical' => 'Съдебен',
    'FilterByCession' => 'Цесия',
    'FilterByFraud' => 'Измама',
    'FilterByLoanId' => 'Кредит ID',
    'FilterByProductId' => 'ID на продукт',
    'FilterByProductName' => 'Име продукт',
    'FilterByProductGroup' => 'Продуктова група',
    'FilterByClientPin' => 'ЕГН',
    'FilterByAmountApprovedFrom' => 'Сума От',
    'FilterByAmountApprovedТо' => 'Сума До',
    'LoansApproveAttemptSkipTill' => 'Пропускане до',
    'FilterByAmountRequestedFrom' => 'Заявена Сума От',
    'FilterByAmountRequestedТо' => 'Заявена Сума До',
    'FilterByLoanType' => 'Тип',
    'FilterByPaymentMethod' => 'Метод на плащане',
    'FilterByOffice' => 'Офис',
    'FilterByInstallments' => 'Вноски',
    'FilterByAdmin' => 'Създал',
    'FilterBySource' => 'Източник',
    'FilterBySaleTaskType' => 'Тип на задача',
    'FilterByRepaidCredits' => 'Изплатени кредити',
    'FilterByBucket' => 'Бъкет',
    'FilterByPeriod' => 'Филтрация по период',
    'FilterByOverdueFrom' => 'Просрочена сума от',
    'FilterByOverdueTo' => 'Просрочена сума до',
    'FilterByOverdueDaysFrom' => 'Просрочена дни от',
    'FilterByOverdueDaysTo' => 'Просрочена дни до',
    'FilterByStatus' => 'Филтрирай по статус',
    'FilterByRoles' => 'Роля',
    'FilterByConsultantId' => 'Консултант ID',
    'Export' => 'Експорт',
    'ExportAcceptedRequests' => 'Експорт подадени заявки',
    'ExportTelemarketing' => 'Експорт телемаркетинг',
    'City' => 'Град',
    'Bic' => 'BIC',
    'Iban' => 'Сметка',
    'Main' => 'Основна',
    'Code' => 'Код',
    'Website' => 'Сайт',
    'SelectOption' => 'Избери опция',
    'SelectCity' => 'Населено място',
    'Bank' => 'Банка',
    'BankAccount' => 'Банкова сметка',
    'SelectBank' => 'Избери банка',
    'IssueDate' => 'Дата на издаване',
    'ValidDate' => 'Дата на валидност',
    'DateOfAmount' => 'Дата, от която е сумата',
    'IssuedBy' => 'ЛК издадена от',
    'PostCode' => 'Пощенски код',
    'Sex' => 'Пол',
    'Bulstat' => 'Булстат',
    'Details' => 'Детайли',
    'Position' => 'Позиция',
    'Experience' => 'Опит в месеци',
    'Permissions' => 'Права',
    'Agreement' => 'Съглашение',
    'Url' => 'Линк',
    'TmpRequestStep' => 'Стъпка за временна заявка',
    'CloseReason' => 'Причина за приключване',
    'ClientId' => 'Клиент ID',
    'ReferClientId' => 'Препоръчващи клиенти (Client id)',
    'CountReferrals' => 'Брой направени препоръки',
    'ReferralsLoansCount' => 'Заявки',
    'ReferralsCountApprovedLoans' => 'Одобрени заявки',
    'ReferralsCountCancelledLoans' => 'Отказани заявки',
    'ReferralsTotalApprovedAmount' => 'Обща отпусната',
    'ReferralsTotalReturnAmount' => 'Обща върната',
    'ApproveLoanSerialNumber' => '№ Заявка',
    'LoanId' => 'Кредит ID',
    'SaleTaskId' => 'ID на задачата',
    'LoanNumber' => 'Кредит No.',
    'FilterByCreatedAtFrom' => 'Дата на Създаване',
    'FilterByCreatedAtTo' => 'Създаден до',
    'FilterByValidFrom' => 'Дата на Валидност',
    'FilterByValidTo' => 'Валиден до',
    'Loan' => 'Кредит',
    'LoanStatus' => 'Статус на кредита',
    'DownLoadLink' => 'Свали Образец',
    'AdminUserName' => 'Потребител',
    'AdminPwdConfirm' => 'Потвържаване на парола',
    'AdminChooseOffices' => 'Офиси',
    'AdminAvatar' => 'Снимка',
    'Documents' => 'Документи',
    'Manual' => 'Ръчно',
    'canBeGeneratedManually' => 'Може да се генерира ръчно',
    'NoManualTemplates' => 'Няма темплейти за ръчно изпращане',
    'BlockClient' => 'Блокирай клиентски профил',
    'UnblockClient' => 'Разблокирай клиентски профил',
    'ipAddress' => 'IP адрес',
    'Key' => 'Ключ',
    'ChooseKey' => 'Избери ключ',
    'Token' => 'Токен',
    'ImportFile' => 'Импортиране на файл',

    //Translations for products
    'ChooseProduct' => 'Изберете продукт',
    'ChooseKind' => 'Изберете вид',
    'ChooseOffice' => 'Изберете офис',
    'PaymentMethods' => 'Метод на плащане',
    'productSettingsCommunication' => 'Комуникация',
    'productSettingsDocuments' => 'Документи',
    'documentName' => 'Име документ',
    'documentStatus' => 'Статус',
    'documentCreatedAt' => 'Дата Създаване',
    'documentActions' => 'Редактирай',
    'ChooseCard' => 'Изберете карта',

    //Translation for employments
    'ChooseEmployment' => 'Трудов статус',

    //Translations for edit/create office
    'OfficeName' => 'Офис',
    'OfficeType' => 'Офис тип',
    'OfficeCreateBranch' => 'Нов Клон',
    'OfficeNameRequited' => 'Моля попълнете полето Офис',
    'OfficeSelectByStatus' => '',
    'OfficeSelectByStatusLabel' => 'Статус',
    'OfficeSelectByOfficeType' => 'Филтриране по офис тип',
    'OfficeSkipCcr' => 'Без ЦКР отчетност',
    'OfficeSkipAccounting' => 'Без счетоводни записи',
    'AccessStartedAt' => 'Дата на стартиране на достъп',
    'AccessEndAt' => 'Дата на прекратяване на достъп',

    //Translations for settings
    'SettingsName' => 'Име на настройката',
    'SettingsDefaultValue' => 'По подразбиране',
    'SettingsSettingTypeName' => 'Вид настройка',
    'SettingsType' => 'Тип',
    'SettingsFilterDescription' => 'Филтриране по описание',
    'SettingsFilterDefaultValue' => 'Филтриране по подразбиране',
    'SettingsFilterSettingType' => 'Филтриране по тип настройка',
    'SettingsFilterDocumentType' => 'Филтриране по вид документ',
    'ValueType' => 'Тип стойност',

    //Translation for clients
    'Pin' => 'ЕГН',
    'IdCardNumber' => 'Номер на лична карта',
    'IdCard' => 'Лична карта',
    'Reason' => 'Причина',
    'FilterReason' => 'Филтриране по причина',
    'Comment' => 'Коментар',
    'Send' => 'Изпрати',
    'Man' => 'Мъж',
    'Woman' => 'Жена',
    'TotalLoans' => 'Брой кредити',
    'Client' => 'Клиент',
    'Message' => 'Съобщение',
    'Employer' => 'Работодател',
    'Contract' => 'Договор',
    'Hiring' => 'Назначаване',
    'Salary' => 'Заплата',
    'Ending' => 'Прекратяване',
    'Term' => 'Срок',
    'Guarant' => 'Поръчител',
    'GuarantorPin' => 'ЕГН на поръчител',
    'ContactPerson' => 'Лице за контакт',
    'AddContactPerson' => 'Добави лице за контакт',
    'ChooseGuarantType' => 'Избери тип гарант',
    'GuarantType' => 'Тип гарант',
    'ChooseContactType' => 'Избери тип контакт',
    'Guarantor' => '{0} Кредитът няма поръчители|{1} Поръчител:|[2,*] Поръчители:',
    'Guarantors' => 'Поръчители',
    'LoanInsurance' => 'Застраховка',
    'AddLoanInsurance' => 'Добави застраховката',
    'New' => 'Нов',
    'Processing' => 'Обработва се',
    'Processed' => 'Обработен',
    'Sent' => 'Изпратено',
    'Error' => 'Грешка',
    'Done' => 'Приключено',
    'Old' => 'Стар',
    'ClientFullName' => 'Имена на Клиента',
    'ClientFullNameApproveList' => 'Имена на Клиента',
    'Identification' => 'Идентификация',
    'ChangeInAmounts' => 'Промяна в суми',
    'ManualApproval' => 'Ръчно одобрение',
    'EditPhone' => 'Промяна на телефон',
    'AddPhone' => 'Добавяне на телефон',
    'EditCurrentAddress' => 'Промяна на текущ адрес',
    'EditGuarant' => 'Промяна на гарант',
    'CreateAddress' => 'Създаване на адрес',
    'EditContact' => 'Промяна на контактно лице',
    'EditEmployer' => 'Промяна на работодател',
    'RepaidLoansCount' => 'Бр. Изплатени<br>кредити',
    'AmountDue' => 'Дължима<br>сума',
    'overdueDays' => 'Просрочие<br>(дни)',
    'startAction' => 'Започни<br>Преглед',
    'Method' => 'Метод',
    'SentData' => 'Изпратени данни',
    'SentStatus' => 'Статус на изпращане',
    'SentAt' => 'Дата на изпращане',
    'ReceivedData' => 'Получени данни',
    'ReceivedStatus' => 'Статус',
    'ReceivedAt' => 'Дата',

    //Translation for loans
    'AmountRequested' => 'Искана Сума',
    'ApproveCreatedAt' => 'Дата на Задачата',
    'AmountApproved' => 'Сума',
    'Id' => 'Номер',
    'Cession' => 'Цесия',
    'Fraud' => 'Измама',
    'Juridical' => 'Съдебен',
    'LastStatusUpdateAdministrator' => 'Админ',
    'LoanChooseClients' => 'Изберете клиент',
    'LoanChooseOffices' => 'Изберете офис',
    'cannotSelectАnOffice' => 'Не може да изберете офис',
    'Office' => 'Офис',
    'PaymentMethod' => 'Метод на плащане',
    'PeriodRequested' => 'Период на Кредита',
    'PeriodApproved' => 'Период',
    'Product' => 'Продукт',
    'ProductType' => 'Амортизация',
    'DocumentType' => 'Вид документ',
    'Source' => 'Източник',
    'LoanType' => 'Тип на кредит',
    'Levs' => 'лв.',
    'Months' => 'Месеца',
    'Month' => 'Месец',
    'Waiting' => 'В изчакване от',
    'Signed' => 'Подписан',
    'MaxOverdue' => 'Макс. Просрочие',

    //Translation for payment
    'DateOfPayment' => 'Дата на плащане',
    'Total' => 'Обща сума',
    'Currency' => 'Валута',
    'Principal' => 'Главница',
    'Interest' => 'Лихви',
    'InterestSingular' => 'Лихва',
    'PenaltyInterest' => 'Наказателни лихви',
    'Fee' => 'Такси',
    'Date' => 'Дата',
    'LastUpdate' => 'Последно извършено действие',
    'Installment' => 'Вноска',
    'CountInstallments' => 'Брой вноски',

    //Translation for block reasons
    'ClientRequest' => 'По искане на клиент',
    'Dead' => 'Починал',
    'HealthReasons' => 'По здравословни причини',
    'SomethingElse' => 'Друго',
    'GDPR' => 'GDPR',
    'EarlyRepayment' => 'Погасява предсрочно',
    'No income' => 'Без доход',

    //Translation for product
    'ProductId' => 'ID на продукт',
    'ProductName' => 'Име на продукт',
    'ProductGroup' => 'Продуктова група',
    'Products' => 'Продукти',
    'ChooseProductGroup' => 'Изберете тип',
    'Incorrect' => 'Грешка',
    'RelativeRequest' => 'По искане на роднина',
    'Relative request' => 'По искане на роднина',
    'Health reasons' => 'По здравословни причини',
    'something else' => 'Друго',

    //Translations for approved decision
    'FilterByApproveDecisionId' => 'Решение за одобрение ID',
    'FilterByApproveDecisionType' => 'Тип',
    'ApproveDecisionType' => 'Тип',
    'FilterByType' => 'Филтрирай по тип',

    //Translation for docs
    'Content' => 'Съдържание',
    'Variables' => 'Променливи',
    'FileName' => 'Име на файла',
    'documentId' => 'ID документ',
    'document' => 'Документ',
    'generatedBy' => 'Генериран от',

    //Translations for sale task
    'SaleTaskType' => 'Вид Продажба',
    'SaleTaskAgent' => 'Имена на агента',
    'SaleTaskCreatedAt' => 'Дата на Създаване',
    'SaleTaskStartProcessing' => 'Време на натискане на бутон „Обработи“',
    'SaleTaskEndProcessing' => 'Време на натискане на бутон за изход от задачата',
    'SaleTaskExit' => 'Изход от задача',
    'SaleTaskDiscount' => 'Отстъпка в задачата.',
    'SignLoanTime' => 'Време на Подписване на заявката',
    'ProcessingBy' => 'Обработва се от',
    'ProcessedBy' => 'Обработен от',
    'ValidTill' => 'Валиден до',

    //Client Card
    'Lock' => '',
    'Unlock' => '',

    //Translation for email
    'Body' => 'Допълнително описание',
    'Common' => 'Друг',

    //Translations for client discount actual
    'ClientDiscountCheckbox' => 'Изтрии Избрани',
    'ClientDiscountActualId' => 'Отстъпка ID',
    'ClientDiscountId' => 'ID Клиент',
    'ClientDiscountActualCreatedBy' => 'Създал',
    'ClientDiscountActualCreatedAt' => 'Дата Създаване',
    'DiscountPercent' => 'Отстъпка',
    'FilterByDiscountPercentFrom' => 'Отстъпка От %',
    'FilterByDiscountPercentTo' => 'Отстъпка До %',
    'ImportDiscounts' => 'Импортирай отстъпки',
    'Discount' => 'Намаление',
    'Percent' => '%',
    'ValidFrom' => 'Валидна от',
    'NewDiscount' => 'Нова отстъпка',


    // Modal discounts
    'DiscountCreation' => 'Създаване на отстъпка',
    'MadeManualDiscount' => 'Посочи ръчно',
    'OrUploadDiscountFile' => 'Или качи файл с остъпки',
    'UploadDiscountFile' => 'Качи файл с остъпки',
    'UploadFile' => 'Качи файл',
    'EnterClientIdPinName' => 'Въведи ID на клиент, ЕГН или имена',
    'ValidFromTill' => 'Валидна от/до',
    'SelectValidPeriod' => 'Избери дата',
    'DiscountProduct' => 'Продукт(и)',
    'SelectDiscountProduct' => 'Избери продукт(и)',
    'CreateSaleTask' => 'Генерирай задачи за обаждане',
    'AttachDiscountFile' => 'Прикачи',
    'DownloadDiscountFile' => 'Свали образец',


    //Translations for interest term
    'downloadCopy' => 'Свали образец',
    'inGpr' => 'Влиза в ГПР',
    'commonPriceIncrease' => 'Основно оскъпяване',
    'additionalPriceIncrease' => 'Допълнително оскъпяване',
    'InterestTermId' => 'Ид на настройка',
    'InterestTermPeriodFrom' => 'Период от',
    'InterestTermPeriodTo' => 'Период до',
    'InterestTermAmountFrom' => 'Сума от',
    'InterestTermAmountTo' => 'Сума до',
    'InterestTermInterestRate' => 'Лихвен процент',
    'InterestTermCreatedAt' => 'Създадена на',
    'InterestTermCreatedBy' => 'Създадена от',
    'InterestTermImport' => 'Импортирай настройки',

    //Translations for penalty term
    'PenaltyTermId' => 'Ид на настройка',
    'PenaltyTermPeriodFrom' => 'Период от',
    'PenaltyTermPeriodTo' => 'Период до',
    'PenaltyTermAmountFrom' => 'Сума от',
    'PenaltyTermAmountTo' => 'Сума до',
    'PenaltyTermPenaltyRate' => 'Неустойка процент',
    'PenaltyTermCreatedAt' => 'Създадена на',
    'PenaltyTermCreatedBy' => 'Създадена от',
    'PenaltyTermImport' => 'Импортирай настройки',
    'UploadClientDocument' => 'Качи документ',

    //Translations for investor tasks :/payments/payments
    'HandledAt' => 'Обработен в',
    'HandledBy' => 'Обработен от',
    'Basis' => 'Основание',
    'InsuranceAmount' => 'Сума на застраховката',
    'Amount' => 'Сума',
    'AmountFromTo' => 'Сума от/до',
    'AmountFrom' => 'Сума от',
    'AmountTo' => 'Сума до',
    'FilterByIban' => 'IBAN',
    'FilterByBic' => 'BIC',


    //Translation for repayment schedule tab in client card
    'GeneralInformation' => 'Обща информация',
    'DueOnTheLoan' => 'Дължимо по заема',
    'LoanPayments' => 'Платено до момента',
    'Payments' => 'Плащания',
    'RemainsToBePaid' => 'Остава за плащане',
    'MaturedAmounts' => 'Падежирали суми',

    //Translations for client card schedule tab HEADERS

    'DueDate' => 'Дата Падеж',
    'LastInstallmentDate' => 'Последна дата на падеж',
    'OutstandingAmount' => 'Остатък по кредит',
    'RowType' => 'Вид Ред',
    'TotalGeneral' => 'Общо',
    'LastPayment' => 'Последно Плащане',
    'RemainsToOwe' => 'Остава да Дължи',
    'CurrentOverdue' => 'Текущо Просрочие',
    'RefinanceLoans' => 'Рефинанс',


    //Translations for client card schedule tab
    'PaymentId' => 'No. плащане',
    'TaskType' => 'Вид задача',
    'BucketNum' => 'Бъкет',
    'ContractNumber' => 'No. кредит',
    'ContractNumber2' => 'Номер на дог',
    'ContractNumber3' => 'Номер на договор',
    'ContractNumber4' => 'No.<br>кредит',
    'LoanAmount' => 'Сума на<br>Кредита',
    'LoanPeriod' => 'Период на<br>Кредита',
    'LoanAmountApproved' => 'Усвоена сума',
    'LoanRemainingPrincipal' => 'Оставаща<br>Главница',
    'LoanTotalPayment' => 'Общо<br>Платено',
    'DateAmountApproved' => 'Дата на усвояване',
    'KindOfProduct' => 'Вид продукт',
    'Period' => 'Период',
    'MethodOfAssimilation' => 'Начин на усвояване',
    'Penalty' => 'Неустойка',
    'RestPenalty' => 'Оставаща Неустойка',
    'PaidPenalty' => 'Платена Неустойка',
    'Paid' => 'Платено',
    'RestPrincipal' => 'Оставаща Главница',
    'RestOther' => 'Оставащо др разходи',
    'PaidOther' => 'Платено др разходи',
    'PaidPrincipal' => 'Платена Главница',
    'PaidRLInterest' => 'Платена РЛ',
    'PaidNLPenalty' => 'Платена НЛ',
    'NL' => 'НЛ',
    'OtherExpenses' => 'Други разходи',
    'TotalRemains' => 'Общо дължимо',
    'WithoutOtherCosts' => 'Без други разходи',
    'interestOverdue' => 'Лихва просрочие',
    'penaltyOverdue' => 'Неустойка просрочие',
    'TotalPaid' => 'Общо Платено',
    'PaidInstallments' => 'Платени вноски',
    'TotalRemainsSecond' => 'Общо остава',
    'TotalMaturedAmounts' => 'Общо в падеж',
    'FinalAmountToGet' => 'За получаване',


    //Buttons
    'ExtendLoan' => 'Удължи кредит',
    'ResetDueDates' => 'Част.предсрочно',
    'AddCost' => 'Добави разход',
    'DeleteCost' => 'Изтрий разход',
    'OfficeConsultant' => 'Офис/Консултант',
    'Edit' => 'Редактирай',
    'Consultant' => 'Консултант',
    'Collector' => 'Инкасатор',
    'Save' => 'Запази',
    'Cancel' => 'Откажи',
    'Alter' => 'Промени',


    // module sales table translations

    'TypeSale' => 'Вид продажба',
    'AmountRequestedSales' => 'Сума',
    'PeriodLoan' => 'Период на Кредита',
    'Timer' => 'Таймер',
    'StartReview' => 'Започни Преглед',
    'InReviewFrom' => 'В преглед от',
    'paginationShow' => 'показва',
    'paginationShowTo' => 'до',
    'paginationShowFrom' => 'от',
    'paginationShowResults' => 'резултата',

    'paymentDateSoon' => 'Предстоящо плащане',
    'clientHasOverdue' => 'Просрочие',


    // New application
    'NumberIdCard' => 'ЛК номер',
    'IdCardIssuedBy' => 'ЛК издадена от',


    'Contact' => 'Лице за контакт',
    'ContactPhone' => 'Тел. ЛК',
    'SkipAutoProcess' => 'Без автоматично решение',
    'WithInsurance' => 'Със застраховка.',
    'SkipRefAmountCheck' => 'Предоговорен просрочен кредит',
    'AddingGuarant' => 'Добави поръчител',
    'AddClientComment' => 'Добави коментар към клиента',

    // module cashDesk label translations
    'RawData' => 'Raw дата',
    'Transaction' => 'Транзакция',
    'TransactionFromTo' => 'От кого/На кого',
    'StartBalance' => 'Нач. салдо',
    'Earnings' => 'Приход',
    'Expense' => 'Разход',
    'ClosingBalance' => 'Кр. салдо',
    'FPBalance' => 'Касов апарат',
    'Supply' => 'Захранване',
    'Output' => 'Извеждане',
    'SupplyCashDesc' => 'Захранване на каса',
    'ErrorFP' => 'Грешка с фискалното устройство',
    'DeductedFundsDesc' => 'Извеждане на средства',
    'EarningsCashDesk' => 'Създай нов проход',
    'ExpenseCashDesk' => 'Създай нов разход',
    'InitialBalance' => 'Начално салдо',
    'DailyReport' => 'Дневен отчет',
    'CashWithdrawal' => 'Извеждане на касова наличност',
    'CashSupply' => 'Захранване на каса',
    'Change' => 'Промяна',
    'initialBalanceAlreadyDone' => 'Началното салдо е вече генерирано',
    'dailyReportAlreadyDone' => 'Дневен отчет за деня е направен',
    'taxFee' => 'Такса удостоверение',


    // all payments list

    'createdDate' => 'Дата Обработка',
    'datePayment' => 'Дата Плащане',
    'direction' => 'Вх. / Изх.',
    'paymentSum' => 'Сума на Плащане',
    'source' => 'Източник',
    'numberLoan' => 'No. Кредит',
    'ClientPin' => 'eng',
    'reasonPayment' => 'Основание плащане',
    'processed' => 'Обработено',
    'statusProcessed' => 'Статус обработка',
    'processedFinish' => 'Обработен',
    'editedBy' => 'Редактира се от',
    'manual' => 'Ръчно',
    'automatic' => 'Автоматично',
    'inOut' => 'Вх./Изх.',
    'inDirection' => 'Входящо',
    'outDirection' => 'Изходящо',
    'ChosePaymentProcessingByName' => 'Изберете източник',
    'toDate' => 'Към дата',
    'blockToDate' => 'Блокирай до дата',
    'sendEmail' => 'Изпрати Email',
    'downloadPdf' => 'Свали в PDF',
    'ExportToPdf' => 'Експорт към PDF',
    'Results' => 'резултати',
    'Shows' => 'показва',

    // Manual payment

    'Payment' => 'Плащане',
    'LeftAfterPayment' => 'Остава След Плащане',
    'EditSpreadSum' => 'Редактирай Разнесена Сума',
    'TypeRow' => 'Вид Ред',
    'RestInterest' => 'Оставаща Лихва',
    'PaidInterest' => 'Платена Лихва',

    // thirdparty module

    'WrongPin' => 'Грешен пин',
    'ReportCodesNotFound' => 'Не е намерен такъв код',
    'ReportCodesNotMapped' => 'Не е мапнат НОЙ код',
    'LoanNotFound' => 'Не е намерен такъв кредит',
    'NoiReportNotCreated' => 'Грешка при създаването на НОИ справка',


    'BirthDate' => 'Дата на раждане',
    'LegalStatus' => 'Вид лице',
    'individual' => 'Физическо лице',
    'company' => 'Юридическо лице',
    'ccr_data' => 'Данни за ЦКР',

    'CitizenshipType' => 'Тип на лице',
    'local' => 'Местно лице',
    'foreigner' => 'Чуждестранно лице',

    'Gender' => 'Пол',
    'male' => 'Мъж',
    'female' => 'Жена',
    'unknown' => 'Неизвестен',

    'LegalStatusCode' => 'Юридически статут',
    '1998' => '1998 - местно физическо лице',
    '1999' => '1999 - чуждестранно физическо лице',
    '437' => '437 - търговец по ТЗ',

    'EconomySector' => 'Сектор в икономиката',
    '3312' => '3312 - домакинства - население',
    '9' => '9 - некласифицирани по сектор',

    'IndustryCode' => 'Отрасъл',
    'ic_00' => '00 - физическо лицe',
    'ic_96' => '96 - други персонални услуги ',

    'CcrPersonType' => 'Тип на лицето',
    'ccr_person_type_1' => 'физическо местно лице',
    'ccr_person_type_2' => 'юридическо лице',
    'ccr_person_type_3' => 'физическо чуждестранно лице',
    'ccr_person_type_5' => 'неизвестно лице',


    // ccr reports out
    'ReportId' => 'ID',
    'Type' => 'Тип',
    'FileZip' => 'ZIP файл',
    'FileCsv' => 'CSV файл',
    'DateCheck' => 'Дата проверка',
    'Identifier' => 'Идентификатор',
    'BasisCheck' => 'Основание за справка',
    'MadeFrom' => 'Извършена от',
    'UserName' => 'Потребителско име',

    // Section
    'SectionName' => 'Име на Секцията',
    'SectionDescription' => 'Описание на Секцията',

    // Document
    'FilterBySectionName' => 'Име на секция',
    'FilterByDocName' => 'Име на документ',
    'DocName' => 'Име на документ',
    'FilterByDocCreatedAt' => 'Дата на създаване',
    'ChooseLandingSectionName' => 'Избери Секция',
    'DocDescription' => 'Описание на Документа',
    'ChooseDocFile' => 'Прикачи файл',

    // Company
    'CompanyName' => 'Име на фирмата',
    'CompanyNameLatin' => 'Име на фирмата (латиница)',
    'Eik' => 'ЕИК',
    'CompanyTaxRegistryNumber' => 'Данъчен номер',
    'RepresentorData' => 'Данни на представител',
    'RepresentorFirstName' => 'Име на представител',
    'RepresentorMiddleName' => 'Бащино име на представител',
    'RepresentorLastName' => 'Фамилно име на представител',
    'RepresentorMainPhone' => 'Телефон на представител',
    'RepresentorSecondaryPhone' => 'Допълнителен телефон на представител',
    'RepresentorEmail' => 'Имейл адрес на представител',
    'DeletedAt' => 'Изтрит в',
    'DeletedBy' => 'Изтрити',
    'ConfirmAt' => 'Потвърдено в',
    'ConfirmBy' => 'Потвърдено',
    'paidInstallment' => 'Платена',
    'incomingInstallment' => 'Предстояща',
    'pastInstallment' => 'Падежирала',
    'overdueInstallment' => 'Просрочена',
    'unclaimedDirection' => 'Движение',
    'unclaimedSum' => 'Сума',
    'UnclaimedMoneyReturn' => 'Върнати пари',
    'Delivered' => 'Разнесен',
    'SentViaEasypay' => 'Изпратен по Изипей',
    'Refunded' => 'Върнати от Изипей',
    'JuridicalFee' => 'Съдебен разход',

    'ReceivedPayments' => 'Получени плащания',
    'PaymentMethodShort' => 'Плащане метод',
    'PaymentYear' => 'Година на плащане',
    'PaymentMonth' => 'Месец на плащане',
    'TotalPaidAmount' => 'Платена сума',
    'OtherIncomes' => 'Други приходи',
    'Profit' => 'Печалба',

    // Installments In Due
    'InstallmentsInDue' => 'В падеж',
    'PrimaryTotalRestAmount' => 'Оставаща Вноска',
    'AccruedAmountTotal' => 'Текущо дължимо',
    'OutstandingAmountTotal' => 'Остава Всичко',
    'OverdueDays1' => 'Проср.',

    // ThirdPartyChecks
    'ReportType' => 'Вид справка',

    // Clients without active loan
    'FromNumberOfDaysSinceLastLoan' => 'Дни от последно изплатен кредит (от)',
    'ToNumberOfDaysSinceLastLoan' => 'Дни от последно изплатен кредит (до)',
    'FromDiscountPercentage' => 'От дискаунт %',
    'ToDiscountPercentage' => 'До дискаунт %',
    'FromNoDeniedAppsLast30Days' => 'Бр. отказани заявки посл. 30 дни (от)',
    'ToNoDeniedAppsLast30Days' => 'Бр. отказани заявки посл. 30 дни (до)',
    'FromMaxOverdueLastLoan' => 'Проср. последен кредит (от)',
    'ToMaxOverdueLastLoan' => 'Проср. последен кредит (до)',
    'LastRepaidProduct' => 'Последно изплатен продукт',
    'LastLoanRepaidAt' => 'Изплатен на',
    'DaysSinceLastLoan' => 'Дни след последно изплатен',
    'ActiveDiscount' => 'Дискаунт %',
    'NoRepaidLoans' => 'Бр. изплатени кредити',
    'LastApplicationDate' => 'Дата последна заявка',
    'MaxOverdueLastLoan' => 'Проср. последен кредит',
    'NoDeniedAppsLast30Days' => 'Бр. отказани заявки посл. 30 дни',

    // Accounting stats
    'AccountingStatsType' => 'Вид Плащане',
    'AccountingStatsInterest' => 'Редовна лихва',
    'AccountingDate' => 'Счет. дата',
    'AccountingStatsLateInterest' => 'Лихва при просрочие',

    // Agent stats
    'AgentStatistics' => 'Агенти одобрение',
    'referer' => 'рефер',
    'FinalProcessingStartAt' => 'Окончателната обработка започва в',
    'FinalProcessingEndAt' => 'Окончателната обработка приключи на',
    'FinalDecision' => 'Окончателно решение',
    'FinalDecisionReason' => 'Причина за окончателното решение',
    'FinalWaitingTime' => 'Крайно време на изчакване',
    'FinalProcessingTime' => 'Крайно време за обработка',
    'Online' => 'Онлайн',
    'CreditLimit' => 'Кредит Лимит',
    'CreditAmount' => 'Кредит',
    'DebitAmount' => 'Дебит',
    'CreditAccount' => 'Кредит акаунт',
    'DebitAccount' => 'Дебит акаунт',

    'DailyReportAction' => 'Дневен отчет',

    'SeparatedByConsultant' => 'Разделение по консултант',

    // Messages With Sending Errors
    'TemplateKey' => 'Шаблон',
    'CommunicationType' => 'Вид комуникация',
    'TemplateId' => 'Шаблон ID',

    'TaskId' => 'ID задача',
    'ButtonExit' => 'Бутон изход',
    'ButtonExitPressedAt' => 'Дата натиснат бутон изход',
    'ButtonExitPressedBy' => 'Кой админ го е натиснал',
    'PromisedAmount' => 'Уговорка сума',
    'PromisedDate' => 'Уговорка дата',
    'PaidBeforePromiseDate' => 'Платена сума по уговорка',
    'PaidBeforePromiseDateAnd3days' => 'Платена сума по уговорка + 3д',

    // Discounted loan
    'IsLoanFirst' => 'Първи кредит ли е',
    'NumberOfLoansAtTimeOfApp' => 'Бр. кредити (активен/изплатен/отписан) към момента на кандидатстване',
    'CollectedInstallments' => 'Събрани вноски',
    'Ratio' => 'Рейтинг',
    'NewClients' => 'Нови клиенти',
    'InstallmentsLessThan400' => 'Първи вноски (<=400)',
    'InstallmentsMoreThan400' => 'Първи вноски (>400)',
    'LoansRepaidBefore3DaysOverdue' => 'Изплатен кредит в просрочие до 3 дни',
    'LoansRepaidBetween_4_30_DaysOverdue' => 'Изплатен кредит в просрочие (4-30) дни',
    'LoansRepaidBetween_31_60_DaysOverdue' => 'Изплатен кредит в просрочие (31-60) дни',
    'AutoDiscount' => 'Авт. отстъпка',
    'AbsoluteFirstRequest' => 'Абс. първа заявка',

    // Manual Mailings Stats
    'Clients' => 'Клиенти',
    'SuccessfulCount' => 'Брой успешни',
    'UnsuccessfulCount' => 'Брой неуспешни',
    'UnsentCount' => 'Брой неизпратени',
    'ReceivedRequests' => 'Получени заявки (+ 3 дни)',
    'ApprovedRequests' => 'Одобрени заявки (+ 3 дни)',
    'SumApprovedRequests' => 'Отпуснати кредити (+ 3 дни)',
    'PaidAmountOnSubmitTemplate' => 'Платено днес (ден на изпращане)',
    'PaidAmountOnSubmitTemplate3Days' => 'Платено днес (+3 дни)',
    'Preview' => 'Превю',

    // Overdue Stats Physical Offices
    'LastPromiseDetails' => 'Последна уговорка',
    'FromOverdueAmount' => 'Просрочена сума (от)',
    'ToOverdueAmount' => 'Просрочена сума (до)',
    'FromOverdueDays' => 'Дни просочие (от)',
    'ToOverdueDays' => 'Дни просочие (до)',
    'GuarantorFullName' => 'Имена на Поръчител',

    // Clients without profile
    'DaysSinceFirstRequest' => 'Дни без заявка',

    // Clients who have never had active loans

    'DateOfFirstRequest' => 'Дата на първа заявка',
    'DateOfLastRequest' => 'Дата на последна заявка',
    'NumberOfRequests' => 'Брой заявки',
    'DaysSinceLastRequest' => 'Дни от последна заявка',

    // Reports BirCho
    'ClientsInOverdue' => 'Клиенти в забава',
    'UplcomingPayments' => 'Предстоящи плащания',

    // Admin settings
    'max_discount_percent_administrator' => 'Макс. процент отстъпка',
    'count_of_reports_per_month_for_a_given_client_administrator' => 'Брой на репорти към 1 клиент за 1 месец(МВР,ЦКР,НОЙ)',
];
