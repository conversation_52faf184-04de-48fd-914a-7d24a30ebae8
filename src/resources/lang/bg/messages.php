<?php

return array(
    'LoanInsuranceController' => [
        'successAddInsuranceToLoan' => 'Успешно добавихте застраховка към кредита.',
        'successRemoveInsurance' => 'Успешно изтриване на застраховката',
    ],
    'LoansApproveController' => [
        'successRemovedAgent' => 'Успешно премахнахте агента от заем.'
    ],
    'AdministratorFilters' => [
        'successDeleteFilter' => 'Успешно истриване на филтр.',
        'successSaveFilter' => 'Успешно запазване на филтр.',
        'errorFilterNotFound' => 'Грешка този филтр не съществува.',
    ],
    'Yes' => 'Да',
    'No' => 'Не',
    'ErrorDeletingAmountLessThanPaid' => 'Сума която изтривате е по голяма от платената.',
    'ErrorCreatingCompanyLoanForIndividualClient' => 'Грешка клиент с ЕГН: :pin вече е регистриран като ФИЗ лизце.',
    'FormatIdCardNumber' => 'Могат да се използват само цифри и латински букви пример: АА0000000 или *********',
    'GuarantorLoans' => 'Поръчител по кредити',
    'ErrorNoAvailableLoansToImport' => 'Нама налични заеми за импорт.',
    'generalSuccessMessage' => 'Успешно запазване на промените.',
    'importFileSuccess' => 'Успешно импортирахте файла.',
    'GettingNewReportsForImprtedIds' => 'Теглиме ной справки за импортираните кредити.',
    'errorClientCreditLimitAlreadyExists' => 'Грешка. За този клиент, вече има създаден лимит.',
    'generalErrorSomethingWrong' => 'Грешка нещо не е наред',
    'storeOuterCollectorSuccess' => 'Успешно добавихте заем за външно събиране',
    'clientIdRequired' => 'Клиент ид задължително поле',
    'loanIdRequired' => 'Кредит ид задължително поле',
    'skipDaysRequired' => 'Дни за отлагане задължително поле',
    'detailsRequired' => 'Полето Детайли е задължително',
    'bucket_task_skip' => [
        'haveOpenBucketTasks' => 'Не може да бъде отложен, вече има отворени задачи за събиране',
        'clientNoHaveIncomingInstallment' => 'Вноската на клиент вече е с изтекъл падеж.',
        'success' => 'Успешно отложихте създаване на първо напомняне.',
        'error' => 'Грешка нещо не е наред',
    ],
    'success' => 'Успех данните са запазени успешно',
    'error' => 'Грешка нещо не е наред',
    'ErrorDeleteClientComment' => 'Грешка при истриване на коментар.',
    'ErrorCreateClientComment' => 'Грешка при добавяне на коментар.',
    'SuccessCreateClientComment' => 'Успешно добавихте коментар към клиента.',
    'SuccessDeleteClientComment' => 'Успешно истрихте коментар.',
    'AdminUserName' => 'Моля въведете потребителско име. Минимум 5 символа',
    'AdminFirstName' => 'Моля въведете име',
    'AdminLastName' => 'Моля въведете фамилно име',
    'AdminPhone' => 'Моля въведете телефонен номер. Дължината трябва да е между 5 и 12 символа',
    'AdminOffice' => 'Моля изберете офис',
    'AdminRole' => 'Моля изберете роля',
    'AdminPermission' => 'Моля изберете права',
    'Required' => 'Полето е задължително',
    'Invalid' => 'Полето има невалидна стойност',
    'IdCardDateAfterNow' => 'Невалидна дата',
    'Max10' => 'Таксата не може да е по-голяма от 10 лева',
    'SymbolNotAllowed' => 'Въведен е непозволен символ. Използвайте само числа.',
    'InvalidFormatIBAN' => 'IBAN може да съдържа само цифри и букви с дилжина 16 - 34 символа',
    'MvrErrorSubmit' => 'За да заредите данни от  МВР полетата ЕГН и Номер на лична карта трябва да бъдат попълнени',
    'SearchByClientPinLength' => 'Моля въведете ЕГН. Дължината трябва да е минимум 8 символа',
    'markAsJuridicalSuccess' => 'Успешно маркирахте заем като съдебен.',
    'ccrSkipImpossible' => 'Кредит вече е бил докладван в ЦКР и не може да бъде изключен от отчетите.',
    'ccrSkipAlreadySet' => 'Кредит вече е маркиран като изкючен от ЦКР.',
    'ccrSkipSuccess' => 'Успешно изключихте кредит от ЦКР.',
    'loanNotActiveYouCantMarkItAsJuridical' => 'За да маркирате заем като съдебен, той трябва да бъде в статус активен.',
    'cantChangeStatusToWrittenOf' => 'Кредит не е маркиран като приключено дело, не може да го направите Отписан.',
    'loanStatusChangedToWrittenOf' => 'Успешно сменихте статус на кредит.',
    'ExportIsGenerated' => 'Експорт е готов.',
    'WrongInputData' => 'Грешни входящи данни.',
    'errorOccurred' => 'Възника грешка',
    'successChangeLoanOffice' => 'Успешно сменен офис.',
    'confirmYourAction' => 'Подтвърди действието',
    'Client: :client has unprocessed loans' => 'Клиент: :client има незавършена заявка.',
    'Client :pin is blocked' => 'Клиент: :pin е блокиран.',
    'GuarantorNotFound' => 'Не съществува такъв поръчител.',
    'MaxFileSize' => 'Максималния размер на файла е: :maxFileSize МБ',
    'MaxInputLength' => 'Името не може да е по-дълго от :max символа',
    'OnlyNumbers' => 'Полето може да съдържа само букви и цифри',
    'DistinctContactPhone' => 'Лицата за контакт не могат да бъдат с един и същи телефонен номер',
    'DistinctGuarantorPin' => 'Поръчителите не могат да са с еднакво ЕГН',
    'DistinctGuarantorIdCardNumber' => 'Номер на лична карта на поръчителите трябва да е различен',
    'Invalid PIN' => 'Невалидно ЕГН',
    'Invalid client phone' => 'Телефона на клиента може да съдържа само цифри',
    'Invalid first name' => 'Името може да съдържа само букви',
    'Invalid middle name' => 'Презимето може да съдържа само букви',
    'Invalid last name' => 'Фамилията може да съдържа само букви',
    'Invalid phone' => 'Телефона на поръчителя е невалиден',
    'Invalid issue date' => 'Невалидна дата на издаване на лична карта',
    'Invalid valid to date' => 'Невалидна дата на валидност на лична карта',
    'Invalid address' => 'Невалидна стойност',
    'toNumberOfDaysSinceLastLoanGte' => 'Стойноста в това поле трябва да бъде по голяма или равна на "Дни от последно изплатен кредит"',
    'toDiscountPercentageGte' => 'Стойноста в това поле трябва да бъде по голяма или равна на "Дискаунт % от"',
    'toNoDeniedAppsLast30DaysGte' => 'Стойноста в това поле трябва да бъде по голяма или равна на "Бр. отказани заявки посл. 30 дни (от)"',
    'toMaxOverdueLastLoanGte' => 'Стойноста в това поле трябва да бъде по голяма или равна на "Проср. последен кредит (от)"',
    'errorGenerateNewPaymentScheduleDoc' => 'Грешка при генериране на документ за нов погастителен план',
    'successGenerateNewPaymentScheduleDoc' => 'Успешно генерирахте документ за нов пог.план',
    'LoanByIdNotFound' => 'Заем не е намерен #:id',
    'LoanIsNotActive' => 'Заем #:id не е активен',
    'NoOutPaymentForLoan' => 'Няма изходящо плащане по кредит #:id',
    'LoanPaymentMethodIsNotAllowed' => 'Кредит #:id не може да се откаже, грешен начин на изходящо плащане.',
    'LoanHasDeliveredIncomingPayment' => 'Кредит #:id не може да се откаже, има плащане по него.',

    // Overdue Stats Physical Offices
    'toOverdueAmountGte' => 'Стойноста в това поле трябва да бъде по голяма или равна на "Просрочена сума (от)"',
    'toOverdueDaysGte' => 'Стойноста в това поле трябва да бъде по голяма или равна на "Дни просочие (от)"',
    'InvalidClientNames' => "Грешка в имената на клиент, няма съвпадение с МВР данни.<br/><br/> <strong>MVR:</strong> :mvr_names<br/> <strong>Система:</strong> :coming_names",
    'ReferFriendStatsController' => [
        'successRefreshStats' => 'Успех. Статистики обновени!'
    ]
);
