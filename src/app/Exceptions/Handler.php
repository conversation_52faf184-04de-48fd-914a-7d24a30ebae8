<?php

namespace App\Exceptions;

use Illuminate\Auth\AuthenticationException;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Support\Arr;
use Illuminate\View\ViewException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Response;
use Modules\Api\Domain\Exceptions\ClientAlreadyExists;
use Modules\Api\Domain\Exceptions\ClientHasUnreceivedEasyPayMoney;
use Modules\Api\Domain\Exceptions\ClientRequestHasAlreadyBeenProcessed;
use Modules\Api\Domain\Exceptions\EasyPay\ClientHasNoActiveLoans;
use Modules\Api\Domain\Exceptions\EasyPay\InvalidPinProvided;
use Modules\Api\Domain\Exceptions\EasyPay\NotExistingClient;
use Modules\Api\Domain\Exceptions\Login\CustomMessage;
use Modules\Api\Domain\Exceptions\Login\SmsCodeNotFoundByCode;
use Modules\Api\Domain\Exceptions\MvrReportIsNotCorrect;
use Modules\Approve\Domain\Exceptions\LoanHasIncorrectStatus;
use Modules\Common\Domain\Exceptions\DomainException;
use Modules\Common\Exceptions\ShouldBeReportedToUser;
use Modules\Common\Exceptions\AccessDeniedApiException;
use Modules\Common\Exceptions\PrincipleEqualToZeroException;
use Modules\Common\Exceptions\RuntimeWarningException;
use Modules\Sales\Domain\Exceptions\ClientHasUnprocessedLoans;
use Modules\Sales\Domain\Exceptions\NewLoanAmountTooSmall;
use Modules\Sales\Exceptions\AmountForRefinanceIsLargerThanProductMaxAmount;
use Modules\Common\Exceptions\ExceptionForUser;
use Psr\Log\LogLevel;
use Throwable;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Request;

class Handler extends ExceptionHandler
{
    protected $dontReport = [
        ExceptionForUser::class,
        AccessDeniedApiException::class,
        CustomMessage::class,
        ClientAlreadyExists::class,
        PrincipleEqualToZeroException::class,
        SmsCodeNotFoundByCode::class,
        ClientRequestHasAlreadyBeenProcessed::class,
        ClientHasNoActiveLoans::class,
        ClientHasUnprocessedLoans::class,
        InvalidPinProvided::class,
        NotExistingClient::class,
        NewLoanAmountTooSmall::class,
        MvrReportIsNotCorrect::class,
        AmountForRefinanceIsLargerThanProductMaxAmount::class,
        ClientHasUnreceivedEasyPayMoney::class,
    ];

    protected $dontFlash = [
        'password',
        'password_confirmation',
    ];

    protected $levels = [
        LoanHasIncorrectStatus::class => LogLevel::WARNING,
        AmountForRefinanceIsLargerThanProductMaxAmount::class => LogLevel::WARNING,
        ClientRequestHasAlreadyBeenProcessed::class => LogLevel::WARNING,
        ClientAlreadyExists::class => LogLevel::WARNING,
        RuntimeWarningException::class => LogLevel::WARNING,
        ClientHasUnreceivedEasyPayMoney::class => LogLevel::WARNING,
        AccessDeniedApiException::class => LogLevel::WARNING,
    ];

    // FrontEndExceptions -> redirect to home with error
    // ViewException -> redirect to home with error
    // DomainException : if METHOD request -> redirect to home with error
    // DomainException : if API request -> nothing to do, api handle them by itself
    public function render(
        $request,
        Throwable $e
    ): Response|JsonResponse|RedirectResponse|\Symfony\Component\HttpFoundation\Response {

        if ("Unauthenticated." != $e->getMessage() && getAdmin()->isSuperAdmin()) dd('DUMP:', $e);

        if ($e instanceof AuthenticationException) {
            if ($request->wantsJson() || $request->hasHeader('OnErrorJson')) {
                return response()->json([
                    'status' => false,
                    'message' => $e->getMessage(),
                ]);
            }

            return redirect()->guest(route('login'));
        }


        // all front end messages will extend this exception (with redirect)
        if (
            $e instanceof FrontEndExceptions
            || $e instanceof DomainException
            || $e instanceof ViewException
        ) {
            return to_route('home')->with('fail',
                (method_exists($e, 'getFrontEndMessage') ? $e->getFrontEndMessage() : $e->getMessage())
            );
        }

        if ($request->wantsJson() || $request->hasHeader('OnErrorJson')) {
            if ($e instanceof ShouldBeReportedToUser) {
                return response()->json([
                        'status' => false,
                        'message' => $e->getMessage(),
                    ],
                    $e->getCode()
                );
            }

            // \Illuminate\Foundation\Exceptions\Handler::convertExceptionToArray
            return response()->json(config('app.debug') ? [
                'status' => false,
                'message' => $e->getMessage(),
                'exception' => get_class($e),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => collect($e->getTrace())->map(static fn ($trace) => Arr::except($trace, ['args']))->all(),
            ] : [
                'status' => false,
                'message' => $this->isHttpException($e) ? $e->getMessage() : __('System error'),
            ]);
        }

        abort_if($e instanceof ShouldBeReportedToUser, Response::HTTP_BAD_REQUEST, $e->getMessage());

        return parent::render($request, $e);
    }

    public function report(Throwable $e)
    {
        if ($this->shouldReport($e) && !array_key_exists($e::class, $this->levels) && !app()->runningInConsole()) {
            $this->logExceptionDetails($e);
        }

        parent::report($e);
    }

    protected function logExceptionDetails(Throwable $e): void
    {
        $url = Request::fullUrl();
        $method = Request::method();
        $ip = Request::ip();
        $userAgent = Request::header('User-Agent');

        Log::error('Exception occurred', [
            'exception' => $e->getMessage(),
            'url' => $url,
            'method' => $method,
            'ip' => $ip,
            'user_agent' => $userAgent,
            'trace' => $e->getTraceAsString(),
        ]);
    }
}
