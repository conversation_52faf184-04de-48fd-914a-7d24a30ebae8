@extends('layouts.app')
@section('content')
    <x-card title="{{__('Available blog posts')}}">

        <div class="mb-3">
            <x-btn-create
                    btn-name="{{__('Create blog post')}}"
                    url="{{route('admin.blog-post.createBlogPost')}}"
            />
        </div>

        <x-table>
            <x-slot:head>
                <tr>
                    <th>{{__('table.Id')}}</th>
                    <th>{{__('table.Active')}}</th>
                    <th>{{__('table.Name')}}</th>
                    <th>{{__('Publish date')}}</th>
                    <th>{{__('table.CreatedAt')}}</th>
                    <th>{{__('table.CreatedBy')}}</th>
                    <th>{{__('table.Actions')}}</th>
                </tr>
            </x-slot:head>

            @foreach($blogPosts as $blogPost)
                <tr>
                    <td>{{$blogPost->getKey()}}</td>
                    <td>{!! $blogPost->is_active->labelHtml() !!}</td>
                    <td>{{$blogPost->title}}</td>
                    <td>{{$blogPost->published_at}}</td>
                    <td>{{$blogPost->created_at}}</td>
                    <td>{{$blogPost->creator->getFullNames()}}</td>
                    <td>
                        <form action="{{route('admin.blog-post.destroy', $blogPost->getKey())}}" method="POST">
                            @method('DELETE')
                            @csrf
                            <a href="{{route('admin.blog-post.edit', $blogPost->getKey())}}"
                               class="btn btn-sm btn-primary"
                            >
                                <i class="fa fa-pencil"></i>&nbsp;
                                {{__('table.Edit')}}
                            </a>
                            <button type="submit" class="btn btn-sm btn-danger">
                                <i class="fa fa-trash-alt"></i>&nbsp;
                                {{__('Delete blog post')}}
                            </button>
                        </form>
                    </td>
                </tr>
            @endforeach
        </x-table>

        <x-table-pagination :rows="$blogPosts"/>

    </x-card>
@endsection
