<?php

namespace Modules\Head\Http\Controllers;

use Illuminate\Http\RedirectResponse;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Common\Models\Loan;
use Modules\Common\Models\LoanStatus;

class LoanInsuranceController extends BaseController
{
    public function addLoanInsurance(Loan $loan): RedirectResponse
    {
        try {
            if (!in_array($loan->loan_status_id, [LoanStatus::PROCESSING_STATUS_ID, LoanStatus::NEW_STATUS_ID])) {
                throw new \RuntimeException('Loan is not processed yet');
            }

            $loan->setAttribute('insurance', true);
            $loan->setAttribute('insurance_amount', getInsuranceAmount());
            if ($loan->saveQuietly()) {
                return back()->with('success', __('messages.LoanInsuranceController.successAddInsuranceToLoan'));
            }

            return back()->with('fail', __('messages.errorOccurred'));
        } catch (\Throwable $throwable) {
            $msg = "{$throwable->getMessage()}, {$throwable->getFile()}:{$throwable->getLine()}";
            \Log::error("LoanInsuranceController::addLoanInsurance() - " . $msg);

            return back()->with('fail', __('messages.errorOccurred'));
        }
    }

    public function removeLoanInsurance(Loan $loan): RedirectResponse
    {
        try {
            if (!in_array($loan->loan_status_id, [LoanStatus::PROCESSING_STATUS_ID, LoanStatus::NEW_STATUS_ID])) {
                throw new \RuntimeException('Loan is not processed yet');
            }

            $loan->setAttribute('insurance', false);
            $loan->setAttribute('insurance_amount', null);
            if ($loan->saveQuietly()) {
                return back()->with('success', __('messages.LoanInsuranceController.successRemoveInsurance'));
            }

            return back()->with('fail', __('messages.errorOccurred'));
        } catch (\Throwable $throwable) {
            $msg = "{$throwable->getMessage()}, {$throwable->getFile()}:{$throwable->getLine()}";
            \Log::error("LoanInsuranceController::removeLoanInsurance() - " . $msg);

            return back()->with('fail', __('messages.errorOccurred'));
        }
    }
}