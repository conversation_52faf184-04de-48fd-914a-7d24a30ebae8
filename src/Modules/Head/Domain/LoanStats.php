<?php

namespace Modules\Head\Domain;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Carbon;
use Modules\Common\Models\Loan;
use Modules\Common\Models\LoanStatus;
use Modules\Common\Models\LoanActualStats as DbModel;
use Modules\Common\Models\Payment;
use Modules\Common\Models\Product;
use Modules\Common\Models\Installment;
use Modules\Head\Repositories\LoanRepository;
use Modules\Head\Repositories\Loan\LoanActualStatsRepository;
use StikCredit\Calculators\FreshInstallment\FreshInstallmentCollection;
use StikCredit\Calculators\LoanCalculator;
use StikCredit\Calculators\LoanCarton;

class LoanStats
{
    public bool $debug = false;
    private Loan $loan;
    private DbModel $dbModel;

    // Collections
    private $previousLoans = null;
    private $previousApplications = null;
    private $allInstallments = null;
    private $paidInstallments = null;
    private $unpaidInstallments = null;

    private $previousLoansCount = null;

    // single objects
    private ?Installment $actualInstallment = null;
    private ?Installment $firstInstallment = null;
    private ?Installment $lastInstallment = null;

    private ?Payment $currentPayment = null;
    private ?Payment $firstPayment = null;
    private ?Payment $lastPayment = null;

    private ?Product $product = null;

    private ?FreshInstallmentCollection $calcInstallments = null;
    private ?LoanCalculator $calculator = null;
    private ?LoanCarton $carton = null;

    private ?array $creationIpData = null;
    private ?Carbon $contractEndDate = null;

    /********************************BUILDER AND CONSTRUCTOR*******************************/

    public function __construct(
        private LoanRepository $loanRepo,
        private LoanActualStatsRepository $repo
    ) {}

    public function new(): self
    {
        return new LoanStats($this->loanRepo, $this->repo);
    }

    public function buildFromLoan(Loan $loan): self
    {
        $this->loan = $loan;
        $this->actualInstallment = $loan->getCurrentInstallment();

        $this->dbModel = $this->loan->loanActualStats ?: new DbModel();
        if(! $this->dbModel->exists){
            $this->dbModel->setRelation(Loan::class, $loan);
            $this->dbModel->loan_id = $loan->getKey();
        }

        return $this;
    }

    public function setPayment(?Payment $payment = null): self
    {
        $this->currentPayment = $payment;
        return $this;
    }

    /*************************HELPERS********************************/

    private function getFirstInstallment(): ?Installment
    {
        if (is_null($this->firstInstallment)) {
            $this->firstInstallment = $this->getAllInstallments()->first();
        }

        return $this->firstInstallment;
    }

    private function getLastInstallment(): ?Installment
    {
        if (is_null($this->lastInstallment)) {
            $this->lastInstallment = $this->getAllInstallments()->last();
        }

        return $this->lastInstallment;
    }

    private function getFirstPayment(): ?Payment
    {
        if (is_null($this->firstPayment)) {
            $this->firstPayment = $this->loan->getFirstPayment();
        }

        return $this->firstPayment;
    }

    private function getLastPayment(): ?Payment
    {
        if (is_null($this->lastPayment)) {
            $this->lastPayment = $this->loan->getLastPayment();
        }

        return $this->lastPayment;
    }

    private function getFullPaidInstallments()
    {
        if (is_null($this->paidInstallments)) {
            $this->paidInstallments = $this->loan->getPaidInstallments();
        }

        return $this->paidInstallments;
    }

    private function getUnpaidInstallments()
    {
        if (is_null($this->unpaidInstallments)) {
            $this->unpaidInstallments = $this->loan->getUnpaidInstallments();
        }

        return $this->unpaidInstallments;
    }

    private function getAllInstallments()
    {
        if (is_null($this->allInstallments)) {
            $this->allInstallments = $this->loan->getAllInstallments();
        }

        return $this->allInstallments;
    }

    private function getOverdueInstallments()
    {
        $allInst = $this->getAllInstallments();

        $today = Carbon::today();

        $filteredInst = $allInst->filter(function ($item) use ($today) {
            return $item->paid == 0 && Carbon::parse($item->due_date)->lte($today);
        });

        return $filteredInst;
    }

    private function getCurrentOverdueDays()
    {
        $overdueInst = $this->getOverdueInstallments();
        $days = $overdueInst->max(function ($item) {
            return $item->current_overdue_days;
        });

        if (empty($days)) {
            $days = 0;
        }

        return $days;
    }

    private function getCurrentOverdueDaysReal()
    {
        $overdueInst = $this->getUnpaidInstallments();

        $days = 0;
        foreach ($overdueInst as $inst) {
            if ($inst->overdue_days > $days) {
                $days = $inst->overdue_days;
            }
        }

        if (empty($days)) {
            $days = 0;
        }

        return $days;
    }

    private function getCurrentOverdueAmount()
    {
        $overdueInst = $this->getOverdueInstallments();
        $totalOverdueAmount = $overdueInst->sum('current_overdue_amount');
        if (empty($totalOverdueAmount)) {
            $totalOverdueAmount = 0;
        }

        return $totalOverdueAmount;
    }

    private function getProduct(): Product
    {
        if (is_null($this->product)) {
            $this->product = $this->loan->product;
        }

        return $this->product;
    }

    private function getPreviousLoans()
    {
        if (is_null($this->previousLoans)) {
            $this->previousLoans = $this->loan->getPreviousLoans();
        }

        return $this->previousLoans;
    }

    private function getPreviousLoansCount()
    {
        if (is_null($this->previousLoansCount)) {
            $this->previousLoansCount = $this->loan->getPreviousLoansCount();
        }

        return $this->previousLoansCount;
    }

    private function getPreviousApplications()
    {
        if (is_null($this->previousApplications)) {
            $this->previousApplications = $this->loan->getPreviousApplications();
        }

        return $this->previousApplications;
    }

    private function getContractEndDate(): Carbon
    {
        if (is_null($this->contractEndDate)) {
            $this->contractEndDate = Carbon::parse($this->getLastInstallment()->due_date);
        }

        return $this->contractEndDate;
    }

    /*****************************SETTERS**********************************/
    public function setDate(): self
    {
        $this->dbModel->date = Carbon::today()->format('Y-m-d');
        return $this;
    }

    public function setRateAnnualPercentage(): self
    {
        $this->dbModel->rate_annual_percentage = $this->loan->getCredit()->gpr;
        return $this;
    }

    public function setApproved(?int $approved = null, bool $recalc = false): self
    {
        if (!is_null($approved)) {
            $this->dbModel->approved = $approved;
            return $this;
        }

        if ($recalc) {
            $this->dbModel->approved = 0;
            if (in_array($this->loan->loan_status_id, LoanStatus::ACTIVE_STATUSES)) {
                $this->dbModel->approved = 1;
            }

            return $this;
        }

        $this->dbModel->approved = ($this->loan->isFinished() || $this->loan->isActive() ? 1 : 0);

        return $this;
    }

    public function setFirstLoan(?int $value = null): self
    {
        if (!is_null($value)) {
            $this->dbModel->first_loan = 0;

            return $this;
        }

        $this->dbModel->first_loan = $this->getPreviousLoansCount() > 0 ? 0 : 1;

        return $this;
    }

    public function setHasPayment(?int $hasPayment = null): self
    {
        $this->dbModel->has_payment = !is_null($hasPayment)
            ? $hasPayment
            : ($this->getLastPayment() ? 1 : 0);

        return $this;
    }

    public function setTotalInstallmentsCount(): self
    {
        $this->dbModel->total_installments_count = $this->getAllInstallments()->count();

        return $this;
    }

    public function setPaidInstallmentsCount(?int $paidInstallmentsCount = null): self
    {
        if ($this->loan->isRepaid()) {
            $this->dbModel->paid_installments_count = $this->getAllInstallments()->count();

            return $this;
        }

        $this->dbModel->paid_installments_count = is_null($paidInstallmentsCount)
            ?  $this->getFullPaidInstallments()->count()
            : $paidInstallmentsCount;

        return $this;
    }

    public function setUnpaidInstallmentsCount(?int $unpaidInstallmentsCount = null, bool $justCreated = false): self
    {
        if ($this->loan->isRepaid()) {
            $this->dbModel->unpaid_installments_count = 0;

            return $this;
        }

        if ($justCreated) {
            $this->dbModel->unpaid_installments_count = $this->getAllInstallments()->count();

            return $this;
        }

        $this->dbModel->unpaid_installments_count = is_null($unpaidInstallmentsCount)
            ? $this->getUnpaidInstallments()?->count() ?? 0
            : $unpaidInstallmentsCount;

        return $this;
    }

    public function setOverdueInstallments(?int $overdueInstallmentsCount = null): self
    {
        if($this->loan->isFinished()){
            $overdueInstallmentsCount = 0;
        }

        $this->dbModel->overdue_installments = is_null($overdueInstallmentsCount)
            ? $this->getOverdueInstallments()->count()
            : $overdueInstallmentsCount;

        return $this;
    }

    public function setFirstInstallmentDate(bool $overwrite = false): self
    {
        // make sure we keep first entering, this should never change
        if (!$overwrite && !empty($this->dbModel->first_installment_date)) {
            return $this;
        }

        $this->dbModel->first_installment_date = $this->getFirstInstallment()->due_date;

        return $this;
    }

    public function setLastInstallmentDate(): self
    {
        $this->dbModel->last_installment_date = $this->getLastInstallment()->due_date;

        return $this;
    }

    public function setAllDatesAtOnce(): self
    {
        $allInstallments = $this->getAllInstallments();

        $this->dbModel->total_installments_count = $allInstallments->count();
        $this->dbModel->first_installment_date   = $allInstallments->first()->due_date;
        $this->dbModel->last_installment_date   = $allInstallments->last()->due_date;

        $today = (Carbon::now()->startOfDay())->toDateString();
        $this->dbModel->unpaid_installments_count = $allInstallments->where('paid', 0)->count();
        $this->dbModel->paid_installments_count = $allInstallments->where('paid', 1)->count();
        $this->dbModel->overdue_installments = $allInstallments->where('paid', 0)->where('due_date', '<', $today)->count();

        return $this;
    }

    public function setPreviousLoans(): self
    {
        $this->dbModel->previous_loans_count = $this->getPreviousLoansCount();

        return $this;
    }

    public function setPreviousApplicationsCount(): self
    {
        $this->dbModel->previous_applications_count = $this->getPreviousApplications()->count();
        return $this;
    }

    public function setDaysAfterPreviousLoan(): self
    {
        if ($this->getPreviousLoansCount() < 1) {
            $this->dbModel->days_after_previous_loan = -1; // according to doc
            return $this;
        }

        // else get the last one and check diff
        $lastLoan = $this->getPreviousLoans()->last();
        if ($lastLoan->isActive()) {
            $this->dbModel->days_after_previous_loan = -1;  // according to doc
            return $this;
        }

        if (!empty($lastLoan->repaid_at)) {
            $repDate = Carbon::parse($lastLoan->repaid_at);
            if ($repDate->gte($this->loan->created_at)) {
                $this->dbModel->days_after_previous_loan = -1;  // according to doc, esli bil viplachen posle vzyatia etogo
                return $this;
            } else {
                $dateDiff =  (int) $repDate->diffInDays($this->loan->created_at);
                if ($dateDiff > 0) {
                    $this->dbModel->days_after_previous_loan = $dateDiff;  // according to doc, esli bil viplachen posle vzyatia etogo
                    return $this;
                }
            }
        }

        $this->dbModel->days_after_previous_loan = 0;

        return $this;
    }

    public function setDaysInUse(?int $daysInUse = null, bool $recalc = false): self
    {
        // recalc flow

        if ($recalc) {
            $this->dbModel->days_in_use = 0;

            if ($this->loan->isActive()) {
                $this->dbModel->days_in_use = Carbon::parse($this->loan->activated_at)->startOfDay()->diffInDays(Carbon::today());
            }
            if ($this->loan->isRepaid()) {
                $lpD = null;
                $lp = $this->getLastPayment();
                if (!empty($lp->created_at)) {
                    $lpD = Carbon::parse($lp->created_at);
                } else if (!empty($this->loan->repaid_at)) {
                    $lpD = Carbon::parse($this->loan->repaid_at);
                }

                if (!empty($lpD)) {
                    $this->dbModel->days_in_use = Carbon::parse($this->loan->activated_at)->startOfDay()->diffInDays($lpD);
                }
            }

            return $this;
        }

        // normal flow

        if ($this->loan->loan_status_id < LoanStatus::ACTIVE_STATUS_ID) {
            $this->dbModel->days_in_use = 0;
            return $this;
        }

        $this->dbModel->days_in_use = is_null($daysInUse)
            ? Carbon::parse($this->loan->activated_at)->startOfDay()->diffInDays(Carbon::today())
            : $daysInUse;

        return $this;
    }

    public function setDaysByContract(bool $recalc = false): self
    {
        if ($this->loan->loan_status_id >= LoanStatus::ACTIVE_STATUS_ID && !$recalc) {
            return $this; // setup it only on creation OR activation
        }

        $lastInstDate = $this->getContractEndDate()->endOfDay();
        $createdDate = Carbon::parse($this->loan->created_at)->startOfDay();
        $this->dbModel->days_by_contract = $lastInstDate->diffInDays($createdDate);

        return $this;
    }

    public function setDaysAmended(): self
    {
        $lastInstDate = $this->getContractEndDate()->endOfDay();
        $createdDate = Carbon::parse($this->loan->created_at)->startOfDay();
        $this->dbModel->days_amended = $lastInstDate->diffInDays($createdDate);

        return $this;
    }

    public function setContractEndDate(): self
    {
        $this->dbModel->contract_end_date = $this->getContractEndDate()->format('Y-m-d');
        return $this;
    }

    public function setRepaymentDate(): self
    {
        $this->dbModel->repayment_date = $this->getContractEndDate()->format('Y-m-d');

        return $this;
    }

    public function setFirstRepaymentDate(bool $justCreated = false): self
    {
        if (!$justCreated) {
            return $this; // setup it only on creation
        }

        $this->dbModel->first_repayment_date = $this->getContractEndDate()->format('Y-m-d');

        return $this;
    }

    public function setLoanExtensionCount(?int $value = null): self
    {
        $this->dbModel->loan_extension_count = is_null($value) ? $this->loanRepo->getExtensionCount($this->loan->getKey()) : $value;

        return $this;
    }

    public function setAllOverdues(): self
    {
        // Prepare basic stuff for the game
        $loan = $this->loan;
        $repaidAt = Carbon::parse($loan->repaid_at)->startOfDay();

        $installments = $loan->getAllInstallments();
        $payments = $loan->getAllPayments([], 'ASC');
        $taxes = $loan->getAllTaxes();


        // Prepare arrays with:
        // - [instalment.seq_num => overdue_days]
        // - [instalment.seq_num => date of overdue]
        $today = Carbon::today()->startOfDay();
        $currentOverdueDaysArr = [0];
        $installmentOverdueDays = [];
        $installmentOverdueDates = [];
        foreach ($installments as $installment) {

            $dueDate = Carbon::parse($installment->due_date)->startOfDay();
            $overdueDays = 0;
            $overdueDate = null;

            if ($installment->paid == 1) {
                $paidDate = Carbon::parse($installment->paid_at)->startOfDay();

                // fix, when we written wrong, paid_at in installment via migration(used today date)
                // so we use loan.repaid_at instead and update instalment.paid_at
                if ($paidDate->gt($repaidAt)) {
                    $paidDate = $repaidAt;
                    $installment->paid_at = $loan->repaid_at;
                }

                if ($dueDate->lt($paidDate)) {
                    $overdueDays = $dueDate->diffInDays($paidDate);
                    $overdueDate = $paidDate;
                }

                // update on installment
                if ($overdueDays != $installment->max_overdue_days) {
                    $installment->max_overdue_days = $overdueDays;
                    $installment->saveQuietly();
                }

            } else if($today->gt($dueDate)) {
                $overdueDays = $dueDate->diffInDays($today);
                $overdueDate = $today;

                // update on installment
                if ($overdueDays > $installment->overdue_days) {
                    $installment->overdue_days = $overdueDays;
                }
                if ($overdueDays > $installment->max_overdue_days) {
                    $installment->max_overdue_days = $overdueDays;
                }
                if ($installment->isDirty()) {
                    $installment->saveQuietly();
                }

                $currentOverdueDaysArr[] = $overdueDays;
            }

            // another case, since we searching for max! it could be in the past
            if ($installment->max_overdue_days > $overdueDays) {
                $overdueDays = $installment->max_overdue_days;
            }

            $installmentOverdueDays[$installment->seq_num] = $overdueDays;
            $installmentOverdueDates[$installment->seq_num] = $overdueDate;
        }

        // Find max overdue days
        $maxOverdueDays = max($installmentOverdueDays);
        // Find the key of the installment with the biggest overdue days
        $seqNumOfInstWithMaxOverdueDays = array_search($maxOverdueDays, $installmentOverdueDays);
        // Find max overdue date
        $maxOverdueDate = null;
        if (isset($installmentOverdueDates[$seqNumOfInstWithMaxOverdueDays])) {
            $maxOverdueDate = $installmentOverdueDates[$seqNumOfInstWithMaxOverdueDays]->format('Y-m-d');
        }
        // Find current overdue days
        $currentOverdueDays = max($currentOverdueDaysArr);


        // FIND max_overdue_amount
        $strategy = 'repaid';
        if (!$loan->isRepaid()) {

            $lastInstallment = $installments->last();
            $lastInstallmentDueDate = Carbon::parse($installment->due_date)->startOfDay();

            $strategy = 'active_accrued';
            if ($today->gte($lastInstallmentDueDate)) {
                $strategy = 'active_overdue';
            }
        }

        $unpaidAmounts = [];
        if ($payments->count() > 0) {
            foreach ($payments as $payment) {

                if (!$payment->isIncoming()) {
                    continue;
                }

                $totalUnpaid = 0;
                $totalPaidAmount = $payment->getTotalPaidAmountBeforeThePayment();

                $paymentDate = new \DateTime($payment->created_at);
                $paymentDateYM = $paymentDate->format('Y-m-d');

                // Process installments
                $totalAccruedAmount = 0;
                foreach ($installments as $installment) {
                    $dueDate = new \DateTime($installment->due_date);
                    $dueDateYM = $dueDate->format('Y-m-d');

                    if ($dueDateYM < $paymentDateYM) {
                        $instRest = (
                            floatToInt($installment->principal)
                            + floatToInt($installment->interest)
                            + floatToInt($installment->penalty)
                            + floatToInt($installment->late_interest)
                            + floatToInt($installment->late_penalty)
                        );

                        $totalAccruedAmount += $instRest;
                    }
                }

                // Process taxes
                foreach ($taxes as $tax) {
                    $createdDate = new \DateTime($tax->created_at);

                    if ($createdDate < $paymentDate) {
                        $totalAccruedAmount += $tax->amount;
                    }
                }

                // Assign total unpaid amount to the corresponding payment ID
                $totalUnpaid = $totalAccruedAmount - $totalPaidAmount;
                $unpaidAmounts[$payment->payment_id] = [
                    'accrued' => $totalAccruedAmount,
                    'paid' => $totalPaidAmount,
                    'unpaid' => $totalUnpaid,
                ];
            }
        }

        $currentOverdueAmount = 0;
        switch ($strategy) {
            case 'repaid':
                // nothing to do, we already prepare array with unpaid amounts
                break;

            case 'active_accrued':
                $loanCartonDb = $loan->getCartonDb();
                $totalAmount = $loanCartonDb['current_overdue_amount'];
                $currentOverdueAmount = intToFloat($totalAmount);
                $unpaidAmounts[time()] = [
                    'accrued' => $totalAmount,
                    'paid' => 0,
                    'unpaid' => $totalAmount,
                ];
                break;

            case 'active_overdue':
                $loanCartonDb = $loan->getCartonDb();
                $totalAmount = $loanCartonDb['outstanding_amount_total'];
                $currentOverdueAmount = intToFloat($totalAmount);
                $unpaidAmounts[time()] = [
                    'accrued' => $totalAmount,
                    'paid' => 0,
                    'unpaid' => $totalAmount,
                ];
                break;
        }

        $overdueAmounts = array_column($unpaidAmounts, 'unpaid');
        $maxOverdueAmount = intToFloat(max($overdueAmounts));


        $this->dbModel->current_overdue_days = $currentOverdueDays;
        $this->dbModel->current_overdue_amount = $currentOverdueAmount;

        $this->dbModel->max_overdue_days = $maxOverdueDays;
        $this->dbModel->max_overdue_date = $maxOverdueDate;
        $this->dbModel->max_overdue_amount = $maxOverdueAmount;

        return $this;
    }

    public function setCurrentOverdueDays(?int $overdueDays = null): self
    {
        if ($this->loan->isFinished()) {
            $this->dbModel->current_overdue_days = 0;

            return $this;
        }

        $this->dbModel->current_overdue_days = is_null($overdueDays)
            ? $this->getCurrentOverdueDaysReal()
            : $overdueDays;

        return $this;
    }

    public function setCurrentOverdueAmount(?int $overdueAmount = null): self
    {
        if ($this->loan->isFinished()) {
            $this->dbModel->current_overdue_amount = 0;

            return $this;
        }

        $this->dbModel->current_overdue_amount = is_null($overdueAmount)
            ? $this->getCurrentOverdueAmount()
            : intToFloat($overdueAmount);

        return $this;
    }

    public function setMaxOverdueDays(bool $recalculate = false, bool $justCreated = false): self
    {
        if ($justCreated) {
            $this->dbModel->max_overdue_days = 0;
            return $this;
        }

        if ($this->loan->isFinished()) {
            return $this;
        }

        $this->dbModel->max_overdue_days = max(
            (int) $this->dbModel->current_overdue_days,
            (int) $this->dbModel->max_overdue_days
        );

        return $this;
    }

    public function setMaxOverdueAmount(bool $recalculate = false, bool $justCreated = false): self
    {
        if ($justCreated) {
            $this->dbModel->max_overdue_amount = 0;
            return $this;
        }

        if ($this->loan->isFinished()) {
            return $this;
        }

        $this->dbModel->max_overdue_amount = max(
            $this->dbModel->current_overdue_amount,
            $this->dbModel->max_overdue_amount
        );

        return $this;
    }

    public function setMaxOverdueDate(bool $recalculate = false): self
    {
        $prevMax = (int) $this->dbModel->max_overdue_days;
        $currMax = (int) $this->dbModel->current_overdue_days;

        if ($currMax && $currMax >= $prevMax) {
            $this->dbModel->max_overdue_date = Carbon::today()->format('Y-m-d');
        }

        return $this;
    }

    // lifetime

    public function setLifetimeValues(): self
    {
        $result = DB::selectOne("
            select
                sum(i.principal) as total_principal,
                sum(i.paid_principal) as total_paid_principal,
                (sum(i.paid_interest) + sum(i.paid_penalty) + sum(i.paid_late_interest) + sum(i.paid_late_penalty)) as total_paid_income
            from installment i
            where
                i.loan_id in (
                    select l.loan_id
                    from loan l
                    where
                        l.client_id = " . $this->loan->client_id . "
                        and l.loan_status_id in (" . implode(',', LoanStatus::ACTIVE_STATUSES) . ")
                        and l.created_at <= '" . $this->loan->created_at->format('Y-m-d H:i:s') . "'
                )
        ");

        $lfPrincipalWd = !empty($result->total_principal) ? $result->total_principal : 0;
        $lfPrincipalRepaid = !empty($result->total_paid_principal) ? $result->total_paid_principal : 0;
        $lfPrincipalIncome = !empty($result->total_paid_income) ? $result->total_paid_income : 0;

        $result = DB::selectOne("
            select
                sum(t.paid_amount) as tax_amount
            from tax t
            where
                t.loan_id in (
                    select l.loan_id
                    from loan l
                    where
                        l.client_id = " . $this->loan->client_id . "
                        and l.loan_status_id in (" . implode(',', LoanStatus::ACTIVE_STATUSES) . ")
                        and l.created_at <= '" . $this->loan->created_at->format('Y-m-d H:i:s') . "'
                )
        ");

        $lfPrincipalIncomeTax = !empty($result->tax_amount) ? (float) intToFloat($result->tax_amount) : 0;


        $this->dbModel->lifetime_principal_withdrawn = $lfPrincipalWd;
        $this->dbModel->lifetime_principal_repaid = $lfPrincipalRepaid;
        $this->dbModel->lifetime_income_repaid = ($lfPrincipalIncome + $lfPrincipalIncomeTax);

        return $this;
    }

    //  repayments

    public function setLastPaidDate(): self
    {
        $this->dbModel->last_paid_date = $this->getLastPayment()?->created_at;

        return $this;
    }

    public function setFirstPaymentReceivedAt(bool $refactor = false): self
    {
        if (!$refactor && !empty($this->dbModel->first_payment_received_at)) {
            return $this;
        }

        $payment = $this->getFirstPayment();

        $this->dbModel->first_payment_received_at = !empty($payment->created_at)
            ? Carbon::parse($payment->created_at)->format('Y-m-d')
            : null;

        return $this;
    }

    public function setRealRepaymentDate(): self
    {
        $lp = $this->getLastPayment();

        // do nothing if not repaid
        if (!$this->loan->isRepaid() || empty($lp->payment_id)) {
            $this->dbModel->real_repayment_date = null;

            return $this;
        }

        $this->dbModel->real_repayment_date = $lp->created_at;

        return $this;
    }

    public function setRepaymentDaysValue(): self
    {
        $lp = $this->getLastPayment();

        if (!$this->loan->isRepaid() || empty($lp->payment_id)) {
            $this->dbModel->repayment_days_value = null;

            return $this;
        }

        $repaymentDate = Carbon::parse($lp->created_at)->startOfDay();
        $contractStartDate = $this->getContractEndDate()->startOfDay();

        $this->dbModel->repayment_days_value = $contractStartDate->diffInDays($repaymentDate, false);

        return $this;
    }

    // carton
    public function setCartonAmounts(bool $onlyBasic = false)
    {
        // on creation we create all amounts = 0, just due: principal, interest, penalty
        if ($onlyBasic) {

            $properties = [
                'due_amount_total_principal',
                'due_amount_total_interest',
                'due_amount_total_penalty',
                'due_amount_total_late_interest',
                'due_amount_total_late_penalty',
                'due_amount_total_taxes',
                'due_amount_total',
                'repaid_amount_principal',
                'repaid_amount_interest',
                'repaid_amount_penalty',
                'repaid_amount_late_interest',
                'repaid_amount_late_penalty',
                'repaid_amount_taxes',
                'repaid_amount_total',
                'outstanding_amount_principal',
                'outstanding_amount_interest',
                'outstanding_amount_penalty',
                'outstanding_amount_late_interest',
                'outstanding_amount_late_penalty',
                'outstanding_amount_taxes',
                'outstanding_amount_total',
                'outstanding_amount_total_no_taxes',
                'accrued_amount_principal',
                'accrued_amount_interest',
                'accrued_amount_penalty',
                'accrued_amount_late_interest',
                'accrued_amount_late_penalty',
                'accrued_amount_taxes',
                'accrued_amount_total',
                'current_overdue_amount',
                'current_overdue_days',
            ];

            foreach ($properties as $property) {
                $this->dbModel->$property = 0;
            }

            $dueAmounts = $this->loan->getDueAmounts();
            foreach ($dueAmounts as $property => $value) {
                $this->dbModel->$property = $value;
            }

            // by default equal to total principal
            $this->dbModel->outstanding_amount_total = $this->dbModel->due_amount_total_principal;
            $this->dbModel->outstanding_amount_principal = $this->dbModel->due_amount_total_principal;

        } else {

            // due_amount_total_principal
            // due_amount_total_interest
            // due_amount_total_penalty
            // due_amount_total_late_interest
            // due_amount_total_late_penalty
            // due_amount_total_taxes
            // due_amount_total
            // repaid_amount_principal
            // repaid_amount_interest
            // repaid_amount_penalty
            // repaid_amount_late_interest
            // repaid_amount_late_penalty
            // repaid_amount_taxes
            // repaid_amount_total
            // outstanding_amount_principal
            // outstanding_amount_interest
            // outstanding_amount_penalty
            // outstanding_amount_late_interest
            // outstanding_amount_late_penalty
            // outstanding_amount_taxes
            // outstanding_amount_total
            // outstanding_amount_total_no_taxes
            // accrued_amount_principal
            // accrued_amount_interest
            // accrued_amount_penalty
            // accrued_amount_late_interest
            // accrued_amount_late_penalty
            // accrued_amount_taxes
            // accrued_amount_total
            // current_overdue_amount
            // current_overdue_days

            $amounts = Loan::getCombinedAmountsForLoanId($this->dbModel->loan_id);
            foreach ($amounts as $property => $value) {
                $this->dbModel->$property = $value;
            }
        }

        return $this;
    }

    // -- bullshits

    public function setCreationBrowser(?string $creationBrowser = null, bool $recalc = false): self
    {
        if (!empty($this->dbModel->creation_browser)) {
            return $this;
        }

        if (empty($creationBrowser) && $recalc) {
            $data = $this->getCreationIpData($this->loan->getKey());
            $creationBrowser = $data['browser'];
        }

        $this->dbModel->creation_browser = $creationBrowser;

        return $this;
    }

    public function setCreationIp(?string $creationIp = null, bool $recalc = false): self
    {
        if (!empty($this->dbModel->creation_ip)) {
            return $this;
        }

        if (empty($creationIp) && $recalc) {
            $data = $this->getCreationIpData($this->loan->getKey());
            $creationIp = $data['ip'];
        }

        $this->dbModel->creation_ip = $creationIp;

        return $this;
    }

    public function getCreationIpData(int $loanId): array
    {
        if (is_null($this->creationIpData)) {
            $this->creationIpData = [
                'ip' => null,
                'browser' => null,
            ];

            $loanIp = $this->loanRepo->getCreationLoanIpData($loanId);
            if (!empty($loanIp->ip)) {
                $this->creationIpData['ip'] = $loanIp->ip;
            }
            if (!empty($loanIp->browser)) {
                $this->creationIpData['browser'] = $loanIp->browser;
            }
        }

        return $this->creationIpData;
    }

    public function setCreationLocation(?string $creationLocation = null): self
    {
        if($creationLocation) {
            $this->dbModel->creation_location = $creationLocation;
        }
        if($this->dbModel->creation_ip){
            return $this; //TODO: use location service
        }
        $this->dbModel->creation_location = null;
        return $this;
    }

    public function setSignBrowser(?string $signBrowser = null): self
    {
        $this->dbModel->sign_browser = $signBrowser;
        return $this;
    }

    public function setSignIp(?string $signIp = null): self
    {
        $this->dbModel->sign_ip = $signIp;
        return $this;
    }

    public function setSignLocation(?string $signLocation = null): self
    {
        if($signLocation){
            $this->dbModel->sign_location = $signLocation;
        }
        if($this->dbModel->sign_ip){
            return $this;//TODO: use somehow location service
        }
        $this->dbModel->sign_location = null;
        return $this;
    }

    public function setA4eExpectedPd($value=null): self
    {
        //TODO: feature
        $this->dbModel->a4e_expected_pd = $value;
        return $this;
    }

    public function setA4eScorecardVersion($value=null): self
    {
        //TODO: feature
        $this->dbModel->a4e_scorecard_version = $value;
        return $this;
    }

    public function setCreatedAt(): self
    {
        $this->dbModel->created_at = Carbon::now();
        return $this;
    }

    public function setCreatedBy(?int $adminId = null): self
    {
        if(!is_null($adminId)) $this->dbModel->created_by = $adminId;
        return $this;
    }

    public function setUpdatedAt(?Carbon $value=null): self
    {
        $this->dbModel->updated_at = $value;
        return $this;
    }

    public function setUpdatedBy(?int $value=null): self
    {
        $this->dbModel->updated_by = $value;
        return $this;
    }

    /***********************Common domain methods*******************************/
    public function save(): DbModel
    {
        return $this->repo->save($this->dbModel);
    }

    public function dbModel(): DbModel
    {
        return $this->dbModel;
    }
}
