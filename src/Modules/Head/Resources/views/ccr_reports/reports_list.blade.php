@extends('layouts.app')

@section('content')
    <div class="row" id="container-row">
        <div class="col-lg-12">
            <div class="row">
                <!-- Panel 1: Генерирай -->
                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Генерирай</h5>
                        </div>
                        <div class="card-body" style="margin-top: 33px; margin-bottom: 10px;">
                            <div class="d-flex flex-wrap gap-2">
                                <a id="btn-cred" href="{{ route('head.ccrReports.generate', 'cred') }}" class="btn btn-success" style="min-width: 120px; margin-right: 10px;">CRED</a>
                                <a id="btn-borr" href="{{ route('head.ccrReports.generate', 'borr') }}" class="btn btn-success" style="min-width: 120px; margin-right: 10px;">BORR</a>
                                <a id="btn-cucr" href="{{ route('head.ccrReports.generate', 'cucr') }}" class="btn btn-success" style="min-width: 120px; margin-right: 10px;">CUCR</a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Panel 2: Филтрирай резултати -->
                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Филтрирай резултати</h5>
                        </div>
                        <div class="card-body">
                            <form id="rep_form" action="{{ route('head.ccrReports.list') }}" method="PUT">
                                @csrf
                                <div class="row">
                                    <div class="col-md-4">
                                        <label for="type">{{__('table.Type')}}:</label>
                                        <select class="form-control mb-3" name="type">
                                            <option value=""></option>
                                            <option value="borr">BORR</option>
                                            <option value="cred">CRED</option>
                                            <option value="cucr">CUCR</option>
                                        </select>
                                    </div>
                                    <div class="col-md-5">
                                        <label for="created_at">{{__('table.FilterByCreatedAtFrom')}}:</label>
                                        <input id="createdAt" name="created_at" autocomplete="off" class="form-control mb-3" type="text" value="">
                                    </div>
                                    <div class="col-md-3">
                                        <label>&nbsp;</label>
                                        <input type="submit" class="form-control btn btn-primary" value="{{ __('btn.Filter') }}">
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            @if($warningNoCucr)
            <div class="row mt-3">
                <div class="col-lg-12">
                    <div class="alert alert-warning" role="alert">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>Внимание:</strong> Няма генериран отчет CUCR за миналия месец.
                    </div>
                </div>
            </div>
            @endif

            <br/>
            <br/>
            <div id="main-table" class="card">
                <div id="reports-table" class="card-body">
                    <div id="loaded-content" class="table-responsive">
                        @include('head::ccr_reports.reports_list_table')
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- CUCR Warning Modal -->
    @if($warningLastCucr)
    <div class="modal fade" id="cucrWarningModal" tabindex="-1" role="dialog" aria-labelledby="cucrWarningModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="cucrWarningModalLabel">
                        <i class="fas fa-exclamation-triangle text-warning"></i>
                        Внимание
                    </h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p>Внимание: Ако не сте качили последен CUCR отчет в ЦКР и ще генерирате нови записи за BORR/CRED, CUCR отчет ще стане невалиден, защото няма да съдържа нови записи.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Отмяна</button>
                    <button type="button" class="btn btn-primary" id="confirmCucrGenerate">Продължи</button>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- CRED Written-off Loans Warning Modal -->
    @if($warningCredHasWrittenOffLoans)
    <div class="modal fade" id="credWarningModal" tabindex="-1" role="dialog" aria-labelledby="credWarningModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="credWarningModalLabel">
                        <i class="fas fa-exclamation-triangle text-warning"></i>
                        Внимание
                    </h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p>CRED файла ще съдържа записи с продадени кредити. Сменен ли е БУЛСТАТ на купувача в кода? Ако не е сменен, откажи, за да не генерираш файл.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Отмяна</button>
                    <button type="button" class="btn btn-primary" id="confirmCredGenerate">Продължи</button>
                </div>
            </div>
        </div>
    </div>
    @endif
@endsection

@push('scripts')
<script type="text/javascript" src="{{ asset('js/jsGrid.js') }}"></script>
<script>
$(document).ready(function () {
    const oldWidth = $('#createdAt').width();
    loadDateRangePicker($('#createdAt'));

    let repUrl = "{{ route('head.ccrReports.refresh') }}";
    let repFormId = $("#rep_form");
    let repTableId = $('#loaded-content');

    loadSimpleDataGrid(repUrl, repFormId, repTableId);

    // super stupid tmp fix, to save input from auto-resize
    $('div.drp-buttons').on('click', 'button', function(){
        $('#createdAt').width(oldWidth);
    });

    // Variables for modal handling
    let pendingUrl = null;
    let pendingButton = null;
    let warningQueue = [];
    let isConfirmed = false;

    function processWarningQueue() {
        if (warningQueue.length === 0) {
            // No more warnings, proceed with generation
            if (pendingUrl && pendingButton) {
                pendingButton.attr('disabled', true);
                pendingButton.css({
                    'pointer-events': 'none',
                    'opacity': '0.6',
                    'cursor': 'not-allowed'
                });
                window.location.href = pendingUrl;

                // Reset variables
                pendingUrl = null;
                pendingButton = null;
                isConfirmed = false;
            }
            return;
        }

        // Show next warning modal
        const nextWarning = warningQueue.shift();
        $('#' + nextWarning).modal('show');
    }

    $('#btn-cred').on('click', function (event) {
        event.preventDefault();

        pendingUrl = $(this).attr('href');
        pendingButton = $(this);
        warningQueue = [];

        // Add warnings to queue in order of priority
        @if($warningCredHasWrittenOffLoans)
            warningQueue.push('credWarningModal');
        @endif

        @if($warningLastCucr)
            warningQueue.push('cucrWarningModal');
        @endif

        if (warningQueue.length > 0) {
            processWarningQueue();
        } else {
            // No warnings, proceed directly
            $(this).attr('disabled', true);
            $(this).css({
                'pointer-events': 'none',
                'opacity': '0.6',
                'cursor': 'not-allowed'
            });
            window.location.href = $(this).attr('href');
        }
    });

    // disable manual reports btns on click
    $('#btn-borr').on('click', function (event) {
        event.preventDefault();

        @if($warningLastCucr)
            pendingUrl = $(this).attr('href');
            pendingButton = $(this);
            $('#cucrWarningModal').modal('show');
        @else
            $(this).attr('disabled', true);
            $(this).css({
                'pointer-events': 'none',
                'opacity': '0.6',
                'cursor': 'not-allowed'
            });
            window.location.href = $(this).attr('href');
        @endif
    });

    $('#btn-cucr').on('click', function (event) {
        $(this).attr('disabled', true);
        $(this).css({
            'pointer-events': 'none',
            'opacity': '0.6',
            'cursor': 'not-allowed'
        });
    });

    // Handle modal confirmations
    @if($warningCredHasWrittenOffLoans)
    $('#confirmCredGenerate').on('click', function() {
        isConfirmed = true;
        $('#credWarningModal').modal('hide');
    });

    $('#credWarningModal').on('hidden.bs.modal', function () {
        if (isConfirmed) {
            isConfirmed = false;
            processWarningQueue(); // Continue to next warning or proceed
        } else {
            // User cancelled
            pendingUrl = null;
            pendingButton = null;
            warningQueue = [];
        }
    });
    @endif

    @if($warningLastCucr)
    $('#confirmCucrGenerate').on('click', function() {
        isConfirmed = true;
        $('#cucrWarningModal').modal('hide');
    });

    $('#cucrWarningModal').on('hidden.bs.modal', function () {
        if (isConfirmed) {
            isConfirmed = false;
            processWarningQueue(); // Continue to next warning or proceed
        } else {
            // User cancelled
            pendingUrl = null;
            pendingButton = null;
            warningQueue = [];
        }
    });
    @endif
});
</script>
@endpush
