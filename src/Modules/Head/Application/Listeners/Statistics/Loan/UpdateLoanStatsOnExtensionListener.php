<?php

namespace Modules\Head\Application\Listeners\Statistics\Loan;

use Modules\Collect\Domain\Events\LoanWasExtended;
use Modules\Collect\Domain\Events\DueDatesWereReset;
use Modules\Head\Domain\LoanStats;

readonly class UpdateLoanStatsOnExtensionListener
{
    public function __construct(private LoanStats $loanStats){}

    public function handle(LoanWasExtended|DueDatesWereReset $event): void
    {
        $this->loanStats
            ->buildFromLoan($event->loan)
            ->setRepaymentDate()
            ->setContractEndDate()
            ->setAllDatesAtOnce()
            ->setCurrentOverdueDays()
            ->setCurrentOverdueAmount()
            ->setLoanExtensionCount()
            ->setDaysAmended()
            ->setCartonAmounts()
            ->setOverdueInstallments()
            ->save();
    }
}
