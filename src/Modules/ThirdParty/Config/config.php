<?php

return [
    'name' => 'ThirdParty',
    'cpay' => [
        'allowed_ips' => [
            // our local ip
            '************',

            // TODO: add later dev, stage, prod server

            // cpay - TODO: check it
            '***********',
            '***********',
            '***********',
            '***********',
        ],
    ],
    'insurance' => [
        'wsdl' => env('INSURANCE_WSDL_URL', base_path('Modules/ThirdParty/Resources/InsuranceWsdl/RISKCertificateFactoryWS.wsdl')),
        'service_url' => env('INSURANCE_SERVICE_URL'),
        'username' => env('INSURANCE_USERNAME'),
        'password' => env('INSURANCE_PASSWORD'),
        'auth_namespace' => env('INSURANCE_AUTH_NAMESPACE', 'urn:Auth'),
        'soap_options' => [
            'trace' => true,
            'exceptions' => true,
            'cache_wsdl' => WSDL_CACHE_NONE,
            'connection_timeout' => 15,
            'location' => env('INSURANCE_SERVICE_URL'),
        ],
    ],
    'veriff' => [
        'local' => [
            'stikcredit' => [
                'url' => 'https://stage.stikcredit.bg/uspeh'
            ],
            'lendivo' => [
                'url' => 'https://stage.lendivo.bg/uspeh'
            ]
        ],
        'stage' => [
            'stikcredit' => [
                'url' => 'https://stage.stikcredit.bg/uspeh'
            ],
            'lendivo' => [
                'url' => 'https://stage.lendivo.bg/uspeh'
            ]
        ],
        'prod' => [
            'stikcredit' => [
                'url' => 'https://stikcredit.bg/uspeh'
            ],
            'lendivo' => [
                'url' => 'https://lendivo.bg/uspeh'
            ]
        ],
    ]
];
