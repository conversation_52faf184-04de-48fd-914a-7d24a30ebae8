<?php

declare(strict_types=1);

namespace Modules\ThirdParty\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Modules\Common\Models\Loan;
use Modules\ThirdParty\Libraries\InsuranceClient;
use Modules\ThirdParty\Services\InsuranceService;
use RuntimeException;

class InsuranceInvalidateJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public const META_KEY = 'InsuranceInvalidateJob';
    public const MAX_TRIES = 1;

    public int $tries = self::MAX_TRIES;
    public int $timeout = 30; // 0.5 minute

    public function __construct(public Loan $loan, public int $try = 1)
    {
        $this->onQueue('commands');
    }

    public function handle(): void
    {
        // TMP SKIP:
        return;

        $loanId = $this->loan->loan_id ?? null;
        if (!$loanId) {
            \Log::error('Error InsuranceInvalidateJob: no loan_id provided');
            return;
        }

        try {
            $this->invalidateCertificate();
        } catch (\Throwable $e) {
            $msg = "{$e->getMessage()}, {$e->getFile()}:{$e->getLine()}";
            \Log::error('Error InsuranceInvalidateJob: ' . $msg);
        }
    }

    private function invalidateCertificate(): void
    {
        // Create insurance service instance
        $config = config('thirdparty.insurance', []);

        $meta = $this->loan->getMeta('InsuranceCertificateJob_cert');
        if (empty($meta->value) || preg_match('/(fail)/', $meta->value)) {
            $this->loan->addMeta(self::META_KEY . '_cancel_cert', 'bad certificateId: ' . $meta->value);
            throw new RuntimeException('Failed to invalidate certificate, bad certificateId');
        }


        $certificateId = $meta->value;
        $soapClient = new InsuranceClient($config);
        $service = new InsuranceService($soapClient);

        $canceled = $service->invalidateCertificate(
            $certificateId,
            $this->loan->client_id,
            $this->loan->loan_id,
        );

        if (!$canceled) {
            $this->loan->addMeta(self::META_KEY . '_cancel_cert', 'failed to invalidate certificate');
            throw new RuntimeException('Failed to invalidate certificate');
        }

        $this->loan->addMeta(self::META_KEY . '_cancel_cert', 'OK');
    }

    public static function dispatch(Loan $loan, int $delaySeconds = 0): void
    {
        $job = new self($loan);

        if ($delaySeconds > 0) {
            $job->delay($delaySeconds);
        }

        dispatch($job);
    }
}
