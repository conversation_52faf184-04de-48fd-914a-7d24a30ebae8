<?php

declare(strict_types=1);

namespace Modules\ThirdParty\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Modules\Common\Enums\FileTypeEnum;
use Modules\Common\Models\Loan;
use Modules\Docs\Services\ClientDocumentService;
use Modules\ThirdParty\Libraries\InsuranceClient;
use Modules\ThirdParty\Services\InsuranceService;
use RuntimeException;

class InsuranceCertificateJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public const META_KEY = 'InsuranceCertificateJob';
    public const MAX_TRIES = 1;

    public int $tries = self::MAX_TRIES;
    public int $timeout = 120; // 2 minutes

    public function __construct(public Loan $loan, public int $try = 1)
    {
        $this->onQueue('commands');
    }

    public function handle(): void
    {
        // TMP SKIP:
        return;

        $loanId = $this->loan->loan_id ?? null;
        if (!$loanId) {
            \Log::error('Error InsuranceCertificateJob: no loan_id provided');
            return;
        }

        try {
            self::processInsuranceCertificate($this->loan);
        } catch (\Throwable $e) {
            $msg = "{$e->getMessage()}, {$e->getFile()}:{$e->getLine()}";
            \Log::error('Error InsuranceCertificateJob: ' . $msg);
        }
    }

    // INFO: keep it public because of easy testing
    public static function processInsuranceCertificate(Loan $loan): void
    {
        $loanClient = $loan->client;

        // Create insurance service instance
        $config = config('thirdparty.insurance', []);
        $soapClient = new InsuranceClient($config);
        $service = new InsuranceService($soapClient);

        // Step 1: Create offer
        $offer = $service->createInsuranceOffer(
            [
                // 'insType' => 'LIFE', // Default insurance type
                'insAmount' => $loan->amount_approved,
                'insDuration' => $loan->period_approved,
                'insDurationType' => $loan->isPaydayLoan() ? 'd' : 'm',
                'operationType' => 'create',
                'otherParameters' => json_encode([
                    // 'loan_id' => $loan->loan_id,
                    // 'client_id' => $loan->client_id,
                    'brand' => 'cashpoint.risk',
                    'birthDate' => $loanClient->birth_date,
                ]),
            ],
            $loan->client_id,
            $loan->loan_id,
        );

        $offerUuid = $offer['offerUUID'] ?? null;
        if (!$offerUuid) {
            $loan->addMeta(self::META_KEY . '_offer', 'failed to create offer');
            throw new RuntimeException('No offer UUID returned');
        }

        $loan->addMeta(self::META_KEY . '_offer', $offerUuid);

        // Step 2: Create certificate
        $certificate = $service->createCertificate(
            $offerUuid,
            [
                'insuredNames' => [
                    $loanClient->first_name ?? '',
                    $loanClient->middle_name ?? '',
                    $loanClient->last_name ?? ''
                ],
                'insuredPersonalIdn' => $loanClient->pin ?? '',
                // 'insuredEmail' => $loanClient->email ?? '',
                // 'insuredPhone' => $loanClient->phone ?? '',
            ],
            $loan->client_id,
            $loan->loan_id,
        );

        $certificateId = $certificate['certificateId'] ?? null;
        if (!$certificateId) {
            $loan->addMeta(self::META_KEY . '_cert', 'failed to create certificate');
            throw new RuntimeException('No certificate ID returned');
        }

        $loan->addMeta(self::META_KEY . '_cert', (string) $certificateId);

        // Step 3: Download certificate PDF
        $certData = $service->getCertificatePdf(
            $certificateId,
            $loan->client_id,
            $loan->loan_id,
        );
        $certPdfBinary = $certData['certificateFileAsPDF'] ?? ''; // byte array
        if (empty($certPdfBinary)) {
            $loan->addMeta(self::META_KEY . '_get_pdf', 'failed to get certificate PDF');
            throw new RuntimeException('No PDF data returned');
        }

        $loan->addMeta(self::META_KEY . '_get_pdf', 'OK');

        // Step 4: Save certificate as client document
        $clientDocumentService = app(ClientDocumentService::class);
        $fileName = "insurance_certificate_{$certificateId}_" . time() . ".pdf";

        $clientDoc = $clientDocumentService->uploadBinaryData(
            $certPdfBinary,
            $fileName,
            'application/pdf',
            $loanClient->client_id,
            FileTypeEnum::INSURANCE_CERTIFICATE,
            $loan->loan_id,
            "Insurance certificate for loan #{$loan->loan_id}"
        );

        if (!$clientDoc) {
            $loan->addMeta(self::META_KEY . '_save_pdf', 'failed to save certificate');
            throw new RuntimeException('Failed to save certificate file');
        }

        $loan->addMeta(self::META_KEY . '_save_pdf', $clientDoc?->client_document_id);
    }

    public static function dispatch(Loan $loan, int $delaySeconds = 0): void
    {
        $job = new self($loan);

        if ($delaySeconds > 0) {
            $job->delay($delaySeconds);
        }

        dispatch($job);
    }
}
