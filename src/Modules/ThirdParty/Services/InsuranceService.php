<?php

declare(strict_types=1);

namespace Modules\ThirdParty\Services;

use RuntimeException;
use Modules\ThirdParty\Libraries\InsuranceClient;
use Modules\ThirdParty\Models\InsuranceLog;

class InsuranceService
{
    public function __construct(private InsuranceClient $client) {}

    /**
     * Create insurance offer (preview or create).
     *
     * Minimal payload keys (adapt to WSDL): insAmount, insDuration, insDurationType,
     * parameters (JSON string or array), brand, operationType ('preview'|'create'), insType
     *
     * @param array $payload
     * @return array parsed response
     */
    public function createInsuranceOffer(
        array $payload,
        ?int $clientId = null,
        ?int $loanId = null
    ): array {
        // Normalize parameters per WSDL expectations
        $payload = $this->normalizeOfferPayload($payload);

        $resp = $this->client->createInsuranceOffer($payload);

        // Log the request and response
        InsuranceLog::logAction('createInsuranceOffer', $payload, $resp, $clientId, $loanId);

        $code = $this->extractResultCode($resp);
        if ($code !== 0) {
            throw new RuntimeException('createInsuranceOffer failed, code=' . $code . ' resp=' . json_encode($resp));
        }

        return $resp;
    }

    /**
     * Updates offer details by id
     * @return array parsed response
     */
    public function updateInsuranceOffer(
        string $offerUuid,
        array $payload,
        ?int $clientId = null,
        ?int $loanId = null
    ): array {
        $payload['offerUUID'] = $offerUuid;
        $payload = $this->normalizeOfferPayload($payload);

        $resp = $this->client->updateInsuranceOffer($offerUuid, $payload);

        // Log the request and response
        InsuranceLog::logAction('updateInsuranceOffer', $payload, $resp, $clientId, $loanId);

        $code = $this->extractResultCode($resp);
        if ($code !== 0) {
            throw new RuntimeException('updateInsuranceOffer failed, code=' . $code . ' resp=' . json_encode($resp));
        }

        return $resp;
    }

    /**
     * Create certificate from offer.
     *
     * Required: offerUUID, insuredNames (array of 3 strings), insuredPersonalIdn (EGN or LNCh),
     * optionally contact info, addresses etc. See WSDL docs.
     */
    public function createCertificate(
        string $offerUuid,
        array $insuredData,
        ?int $clientId = null,
        ?int $loanId = null
    ): array {

        $params = ['offerUUID' => $offerUuid] + $insuredData;
        $this->validateInsuredData($params);

        $resp = $this->client->createCertificate($offerUuid, $params);

        // Log the request and response
        InsuranceLog::logAction('createCertificate', $params, $resp, $clientId, $loanId);

        $code = $this->extractResultCode($resp);
        if ($code !== 0) {
            throw new RuntimeException('createCertificate failed, code=' . $code . ' resp=' . json_encode($resp));
        }

        return $resp;
    }

    /**
     * Retrieve PDF binary for given certificate id.
     * Returns raw PDF binary string.
     */
    public function getCertificatePdf(
        int $certificateId,
        ?int $clientId = null,
        ?int $loanId = null
    ): string {
        $request = ['certificateId' => $certificateId];
        $pdf = $this->client->getCertificatePdf($certificateId);

        // Log the request and response (truncate PDF data for logging)
        $response = [
            'pdf_size' => strlen($pdf),
            'pdf_available' => !empty($pdf),
            'pdf_preview' => !empty($pdf) ? substr(base64_encode($pdf), 0, 100) . '...' : null
        ];
        InsuranceLog::logAction('getCertificatePdf', $request, $response, $clientId, $loanId);

        if (empty($pdf)) {
            throw new RuntimeException('Empty PDF returned for certificateId='.$certificateId);
        }

        return $pdf;
    }

    public function getInsuranceDocuments(
        ?int $clientId = null,
        ?int $loanId = null
    ): array {
        $request = [];
        $resp = $this->client->getInsuranceDocuments();

        // Log the request and response
        InsuranceLog::logAction('getInsuranceDocuments', $request, $resp, $clientId, $loanId);

        return $resp;
    }

    public function invalidateCertificate(
        int $certificateId,
        ?int $clientId = null,
        ?int $loanId = null
    ): bool {

        $request = ['certificateId' => $certificateId];
        $resp = $this->client->invalidateCertificate($certificateId);

        // Log the request and response
        InsuranceLog::logAction('invalidateCertificate', $request, $resp, $clientId, $loanId);

        $code = $this->extractResultCode($resp);
        if ($code !== 0) {
            throw new RuntimeException('invalidateCertificate failed, code=' . $code . ' resp=' . json_encode($resp));
        }

        return $resp;
    }

    /* ---------- helpers ---------- */

    private function normalizeOfferPayload(array $payload): array
    {
    	$this->validateOfferdData($payload);

        // If parameters passed as array, convert to JSON string if WS expects JSON string
        if (isset($payload['parameters']) && is_array($payload['parameters'])) {
            $payload['parameters'] = json_encode($payload['parameters'], JSON_UNESCAPED_UNICODE);
        }

        return $payload;
    }

    private function validateOfferdData(array $params): void
    {
        if (empty($params['insDurationType'])) {
            throw new RuntimeException('insDurationType is required');
        }

        if (empty($params['operationType'])) {
            throw new RuntimeException('operationType is required');
        }

        // if (empty($params['insType'])) {
        //     throw new RuntimeException('insType is required');
        // }
    }

    private function validateInsuredData(array $params): void
    {
        // Quick checks: insuredNames as array of 3 non-empty strings; insuredPersonalIdn present
        if (empty($params['insuredNames']) || !is_array($params['insuredNames']) || count($params['insuredNames']) < 1) {
            throw new RuntimeException('insuredNames is required and must be array of names');
        }
        if (empty($params['insuredPersonalIdn'])) {
            throw new RuntimeException('insuredPersonalIdn is required');
        }
    }

    /**
     * Try to extract numeric result code from response structure.
     * Different WS implementations use different fields: resultCode, result, responseCode etc.
     */
    private function extractResultCode($resp): int
    {
    	// different api calls, have different response format
        if (is_array($resp)) {
            if (isset($resp['resultCode'])) {
                return (int) $resp['resultCode'];
            }
            if (isset($resp['result'])) {
                return (int) $resp['result'];
            }
            if (isset($resp['responseCode'])) {
                return (int) $resp['responseCode'];
            }
        }

        // fallback to success
        return 404;
    }
}
