<?php

namespace Modules\Collect\Imports;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;

class OuterCollectorLoanIdsImport implements ToCollection
{
    public function __construct(
        private Collection $loanIds
    ) {
    }

    public function collection(Collection $collection)
    {
        return $collection->map(function ($row, int $index) {
            $row = $row->filter()->toArray();
            if (isset($row[0]) && $row[0] === 'LoanId' || is_string($row[0])) {
                return null;
            }

            if (empty($row)) {
                return null;
            }

            $this->loanIds->put($index, $row[0]);
        });
    }

    public function loanIds(): Collection
    {
        return $this->loanIds;
    }
}