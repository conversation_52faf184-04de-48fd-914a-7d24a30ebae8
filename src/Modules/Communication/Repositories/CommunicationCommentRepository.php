<?php

namespace Modules\Communication\Repositories;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Collection;
use Modules\Common\Entities\CommunicationPivot;
use Modules\Common\Models\CommunicationComment;
use Modules\Common\Repositories\BaseRepository;

class CommunicationCommentRepository extends BaseRepository
{
    public function getDbModel(): CommunicationComment
    {
        return new CommunicationComment();
    }

    public function getPreparedByLoanId(int $loanId, int $limit = 5): array
    {
        return DB::select("
            SELECT cc.*
            FROM communication_comment cc
            JOIN communication_pivots cp ON
                cp.communication_id = cc.communication_comment_id
                AND cp.loan_id = " . $loanId . "
                AND cp.communication_type = 'comment'
            ORDER BY cc.communication_comment_id DESC
            LIMIT " . $limit . "
        ");
    }

    public function getByLoanId(int $loanId, int $limit = 5): Collection
    {
        return CommunicationPivot::where('loan_id', $loanId)
            ->where('communication_type', 'comment')
            ->limit($limit)
            ->orderBy('id', 'desc')
            ->get();
    }

    /**
     * @param int $commentId
     *
     * @return CommunicationComment|null
     */
    public function getById(int $commentId): ?CommunicationComment
    {
        return CommunicationComment::where('communication_comment_id', $commentId)
            ->first();
    }

    /**
     * @param array $data
     *
     * @return CommunicationComment
     */
    public function create(array $data): CommunicationComment
    {
        /// check if this client already has this comment
        $row = CommunicationComment::where([
                'client_id' => $data['client_id'],
                'text' => $data['text'],
            ])
            ->whereDate('created_at', now()->toDateString())
            ->first();

        if (!empty($row->communication_comment_id)) {
            return $row;
        }

        $communicationComment = new CommunicationComment();
        $communicationComment->fill($data);
        $communicationComment->save();

        return $communicationComment;
    }

    /**
     * @param array $criteria
     *
     * @return CommunicationComment|null
     */
    public function getByCriteria(array $criteria): ?CommunicationComment
    {
        return CommunicationComment::where($criteria)
            ->first();
    }

    /**
     * @param array $communicationComments
     */
    public function bulkCreate(array $communicationComments)
    {
        CommunicationComment::insert($communicationComments);
    }
}
