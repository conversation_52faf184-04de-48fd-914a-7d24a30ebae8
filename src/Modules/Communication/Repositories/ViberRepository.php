<?php

namespace Modules\Communication\Repositories;

use Modules\Common\Models\Viber;
use Modules\Common\Repositories\BaseRepository;

class ViberRepository extends BaseRepository
{
    /**
     * @param int $limit
     * @param array $joins
     * @param array $where
     * @param array|string[] $order
     * @param bool $showDeleted
     *
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     */
    public function getAll(
        int $limit,
        array $joins = [],
        array $where = [],
        array $order = [],
        bool $showDeleted = false
    ) {
        $builder = Viber::orderByRaw(
            implode(', ', $this->prepareOrderStatement($order))
        );

        $this->setJoins($joins, $builder);

        if (!empty($where)) {
            $builder->where($where);
        }

        return $builder->paginate($limit);
    }

    /**
     * [getById description]
     *
     * @param int $viberId
     *
     * @return Viber|null
     */
    public function getById(int $viberId)
    {
        $viber = Viber::where(
            'viber_id',
            '=',
            $viberId
        )->get();

        return $viber->first();
    }

    /**
     * @param array $data
     *
     * @return Viber
     */
    public function create(array $data)
    {
        $viber = new Viber();
        $viber->fill($data);
        $viber->save();

        return $viber;
    }
}

