<?php

namespace Modules\Communication\Repositories;

use Illuminate\Database\Eloquent\Builder;
use Modules\Common\Helpers\OtherHelper;
use Modules\Common\Repositories\BaseRepository;
use Modules\Communication\Models\ViberTemplate;

class ViberTemplateRepository extends BaseRepository
{
    public function __construct(
        protected ViberTemplate $viberTemplate = new ViberTemplate
    ) {
    }

    /**
     * @param int $limit
     * @param array $joins
     * @param array $where
     * @param array|string[] $order
     * @param bool $showDeleted
     *
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     */
    public function getAll(
        int $limit,
        array $joins = [],
        array $where = [],
        array $order = ['viber_template_id' => 'DESC'],
        bool $showDeleted = false
    ) {
        $builder = ViberTemplate::orderByRaw(
            implode(', ', $this->prepareOrderStatement($order))
        );

        $this->setJoins($joins, $builder);
        $where = $this->checkForDeleted($where, $showDeleted);

        if (!empty($where)) {
            $builder->where($where);
        }

        return $builder->paginate($limit);
    }

    /**
     * [getById description]
     *
     * @param int $viberTemplateId [description]
     *
     * @return ViberTemplate|null
     */
    public function getById(int $viberTemplateId)
    {
        $viberTemplate = ViberTemplate::where(
            'viber_template_id',
            '=',
            $viberTemplateId
        )->get();

        return $viberTemplate->first();
    }

    /**
     * @param array $data
     *
     * @return ViberTemplate
     */
    public function create(array $data)
    {
        $viberTemplate = new ViberTemplate();
        $viberTemplate->fill($data);
        $viberTemplate->variables = OtherHelper::getVariablesFromText($viberTemplate->text);
        $viberTemplate->save();

        if (isset($data['offices'])) {
            $viberTemplate->adopt('officesRelation', $data['offices']);
        }

        return $viberTemplate;
    }

    /**
     * @param ViberTemplate $viberTemplate
     * @param array $data
     *
     * @return ViberTemplate
     */
    public function edit(ViberTemplate $viberTemplate, array $data)
    {
        $viberTemplate->fill($data);
        $viberTemplate->variables = OtherHelper::getVariablesFromText($viberTemplate->text);
        $viberTemplate->save();

        if (isset($data['offices'])) {
            $viberTemplate->adopt('officesRelation', $data['offices']);
        }

        return $viberTemplate;
    }

    /**
     * @param ViberTemplate $viberTemplate
     *
     * @throws \Exception
     */
    public function delete(ViberTemplate $viberTemplate)
    {
        $viberTemplate->delete();
    }

    /**
     * @param ViberTemplate $viberTemplate
     */
    public function enable(ViberTemplate $viberTemplate)
    {
        $viberTemplate->enable();
    }

    /**
     * @param ViberTemplate $viberTemplate
     */
    public function disable(ViberTemplate $viberTemplate)
    {
        $viberTemplate->disable();
    }

    public function getManual()
    {
        return ViberTemplate::where(['manual' => 1])->get();
    }

    public function getManualWithAdminOfficeRelation($adminOffices)
    {
        $adminOffices = $adminOffices->pluck('office_id')->toArray();

        return ViberTemplate::whereHas('officesRelation', function (Builder $query) use ($adminOffices) {
            $query->whereIn('office_viber_template.office_id', $adminOffices);
        })
            ->where(['manual' => 1])
            ->get();
    }
}
