<?php

namespace Modules\Communication\Repositories;

use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Modules\Common\Models\Email;
use Modules\Common\Models\Office;
use Modules\Common\Repositories\BaseRepository;
use Modules\Communication\Application\Enums\EmailTemplateKeyEnum;
use Modules\Communication\Models\EmailTemplate;
use Illuminate\Database\Eloquent\Builder;

final class EmailRepository extends BaseRepository
{
    public function getFilterBy(array $filters): LengthAwarePaginator
    {
        $adminOfficeIds = getAdminOfficeIds();

        $email = Email::leftJoin('loan', 'loan.loan_id', '=', 'email.loan_id');

        if (in_array(Office::OFFICE_ID_WEB, $adminOfficeIds)) {
            $email->whereRaw('(loan.office_id IN (' . implode(',', $adminOfficeIds) . ') OR loan.loan_id IS NULL)');
        } else {
            $email->whereRaw('(loan.office_id IN (' . implode(',', $adminOfficeIds) . ') AND loan.loan_id IS NOT NULL)');
        }

        return $email->filterBy($filters)
            ->select('email.*')
            ->with(['status:id,key,name'])
            ->orderBy('email_id', 'DESC')
            ->paginate($this->getPaginationLimit());
    }

    /**
     * @param int $limit
     * @param array $joins
     * @param array $where
     * @param array|string[] $order
     * @param bool $showDeleted
     *
     * @return mixed
     */
    public function getAll(
        int   $limit,
        array $joins = [],
        array $where = [],
        array $order = ['email_id' => 'DESC'],
        bool  $showDeleted = false
    )
    {
        $builder = Email::orderByRaw(
            implode(', ', $this->prepareOrderStatement($order))
        );

        $this->setJoins($joins, $builder);

        if (!empty($where)) {
            $builder->where($where);
        }

        return $builder->paginate($limit);
    }

    /**
     * @param int $emailId
     *
     * @return mixed
     */
    public function getById(int $emailId)
    {
        return Email::where(
            [
                'email_id' => $emailId,
                'deleted' => 0,
                'active' => 1
            ]
        )
            ->get()
            ->first();
    }

    public function create(array $data): Email
    {
        return Email::create($data);
    }

    public function getByClientId(int $clientId): ?Email
    {
        return Email::where([
            'client_id' => $clientId,
            'deleted' => 0,
            'active' => 1
        ])->first();
    }

    public function isEmailOpened(int $clientId, int $emailTemplateId): bool
    {
        // TODO refactor function after we know how we will do it

        return false;

        $isOpened = DB::select(DB::raw(
            "SELECT CASE WHEN COUNT(e.opened_at) > 0 THEN 1 ELSE 0 END
                    FROM email AS e
                    WHERE e.email_template_id = $emailTemplateId
                    AND e.client_id = $clientId
                    AND e.active = 1
                    AND e.deleted = 0
                    GROUP BY e.email_id
                    ORDER BY e.email_id DESC
                    LIMIT 1"
        ));

        return $isOpened[0]->case;
    }

    public function getEmailsForLegalQuery(int $clientId, int $loanId): Builder
    {
        $emailTemplatesKeys = "'" . implode("', '", [
            EmailTemplateKeyEnum::NEW_APPLICATION_CREATED->value,
            EmailTemplateKeyEnum::LOAN_APPROVED->value,
        ]) . "'";

        return Email::query()->fromRaw(<<<SQL
(
    with templates as (
        select email_template_id
        from email_template
        where key in ($emailTemplatesKeys)
    )
    select e.*
    from email e
        join (
            select email.client_id, email.email_template_id, email.loan_id, max(email.created_at) as latest_sent_date
            from email
                join templates on email.email_template_id = templates.email_template_id
            where email.client_id = $clientId and email.loan_id = $loanId
            group by email.email_template_id, email.client_id, email.loan_id
        ) latest on e.email_template_id = latest.email_template_id 
            and e.created_at = latest.latest_sent_date 
            and e.client_id = latest.client_id
            and e.loan_id = latest.loan_id
) as email
SQL);
    }
}

