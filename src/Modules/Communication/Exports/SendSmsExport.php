<?php

namespace Modules\Communication\Exports;

use Illuminate\Database\Eloquent\Builder;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Modules\Common\Models\Sms;

class SendSmsExport implements FromQuery, WithHeadings, WithMapping, ShouldAutoSize
{
    public function __construct(
        public array $filters
    ) {
    }

    public function query(): Builder
    {
        return Sms::filterBy($this->filters);
    }

    public function map($row): array
    {
        /** @var Sms $row ** */
        return [
            $row->sent_at,
            $row->text,
            $row->smsTemplate->key,
            $row->administrator?->username ?? '',
            $row->manual ? __('table.Yes') : __('table.No'),
            $row->identifier,
            $row->sender,
            $row->phone,
            $row->response,
            $row->tries
        ];
    }

    public function headings(): array
    {
        return [
            __('communication::smsTable.SendAt'),
            __('communication::smsTable.Text'),
            __('table.Name'),
            __('communication::smsTable.Administrator'),
            __('table.Type'),
            __('table.Manual'),
            __('table.Identifier'),
            __('communication::smsTable.Sender'),
            __('table.Phone'),
            __('communication::smsTable.Response'),
            __('communication::smsTable.Tries'),
        ];
    }

    public function chunkSize(): int
    {
        return 1000;
    }
}