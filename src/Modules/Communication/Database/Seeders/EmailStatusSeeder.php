<?php

declare(strict_types=1);

namespace Modules\Communication\Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Modules\Communication\Enums\EmailStatusEnum;

/**
 * php artisan db:seed --class=\\Modules\\Communication\\Database\\Seeders\\EmailStatusSeeder
 */
final class EmailStatusSeeder extends Seeder
{
    public function run(): void
    {
        /**
         * @var array<string, string> $statuses https://mailgun-docs.redoc.ly/docs/mailgun/user-manual/events/#event-types
         */
        $statuses = [
            EmailStatusEnum::Accepted->value => 'Accepted',
            EmailStatusEnum::Rejected->value => 'Rejected',
            EmailStatusEnum::Delivered->value => 'Delivered',
            EmailStatusEnum::Failed->value => 'Failed',
            EmailStatusEnum::Opened->value => 'Opened',
            EmailStatusEnum::Clicked->value => 'Clicked',
            EmailStatusEnum::Unsubscribed->value => 'Unsubscribed',
            EmailStatusEnum::Complained->value => 'Complained',
            EmailStatusEnum::Stored->value => 'Stored',
        ];

        foreach ($statuses as $key => $status) {
            DB::table('email_statuses')->updateOrInsert(['key' => $key], ['name' => $status]);
        }
    }
}
