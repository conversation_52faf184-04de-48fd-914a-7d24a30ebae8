<?php

namespace Modules\Communication\Database\Seeders;

use Faker\Factory as Faker;
use Illuminate\Database\Seeder;
use Modules\Common\Models\Sms;

class SmsTypeUpdateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $faker = Faker::create();
        $smses = Sms::all();

        foreach ($smses as $sms) {
            $sms->update(
                [
                    'type' => $faker->randomElement(Sms::getSmsTypes())
                ]
            );
        }
    }
}
