<?php

namespace Modules\Communication\Database\factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\Common\Models\Administrator;
use Modules\Communication\Models\ViberTemplate;

class ViberTemplateFactory extends Factory
{
    protected $model = ViberTemplate::class;

    public function definition(): array
    {
        return [
            'key' => fake()->text(),
            'description' => fake()->text(),
            'variables' => '[]',
            'name' => fake()->text(),
            'text' => fake()->text(),
            'gender' => 'common',
            'active' => 1,
            'manual' => 0,
            'type' => 'system',
            'created_by' => Administrator::factory(),
        ];
    }
}
