<?php

namespace Modules\Communication\Database\factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\Common\Models\Administrator;
use Modules\Communication\Models\EmailTemplate;

class EmailTemplateFactory extends Factory
{
    protected $model = EmailTemplate::class;

    public function definition(): array
    {
        return [
            'key' => fake()->text(),
            'description' => fake()->text(),
            'variables' => '[]',
            'title' => fake()->text(),
            'body' => fake()->text(),
            'text' => fake()->text(),
            'gender' => 'common',
            'active' => 1,
            'manual' => 0,
            'type' => 'collect',
            'created_by' => Administrator::factory(),
        ];
    }
}
