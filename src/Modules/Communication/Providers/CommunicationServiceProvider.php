<?php

namespace Modules\Communication\Providers;

use Config;
use Illuminate\Database\Eloquent\Factory;
use Illuminate\Support\ServiceProvider;
use Modules\Communication\Console\ApprovedClientCommunicationCommand;
use Modules\Communication\Console\ComingDueDateReminder;
use Modules\Communication\Console\ExecMarketingTasksCommand;
use Modules\Communication\Console\OverdueNotification;
use Modules\Communication\Console\PrepareMarketingTasksCommand;
use Modules\Communication\Console\ProcessOldMarketingTasks;
use Modules\Communication\Console\ReminderForClientsWhichNeverHadLoan;
use Modules\Communication\Console\SaleTaskNoInterestSendEmail;
use Modules\Communication\Console\UpdateManualMailingsStatsCommand;

class CommunicationServiceProvider extends ServiceProvider
{
    /**
     * @var string $moduleName
     */
    protected $moduleName = 'Communication';

    /**
     * @var string $moduleNameLower
     */
    protected $moduleNameLower = 'communication';

    /**
     * Boot the application events.
     *
     * @return void
     */
    public function boot()
    {
        $this->registerCommands();
        $this->registerTranslations();
        $this->registerConfig();
        $this->registerViews();
        $this->registerFactories();
        $this->loadMigrationsFrom(module_path($this->moduleName, 'Database/Migrations'));
    }

    /**
     * Register the service provider.
     *
     * @return void
     */
    public function register()
    {
        $this->app->register(RouteServiceProvider::class);
    }

    public function registerCommands()
    {
        $this->commands([
            ComingDueDateReminder::class,
            OverdueNotification::class,
            PrepareMarketingTasksCommand::class,
            ExecMarketingTasksCommand::class,
            ProcessOldMarketingTasks::class,
            UpdateManualMailingsStatsCommand::class,
            SaleTaskNoInterestSendEmail::class,
            ReminderForClientsWhichNeverHadLoan::class,
            ApprovedClientCommunicationCommand::class
        ]);
    }

    /**
     * Register config.
     *
     * @return void
     */
    protected function registerConfig(): void
    {
        $this->publishes([
            module_path($this->moduleName, 'Config/config.php') => config_path($this->moduleNameLower . '.php'),
            module_path($this->moduleName, 'Config/email-templates.php') => config_path($this->moduleNameLower . '.php'),
            module_path($this->moduleName, 'Config/sms-template.php') => config_path($this->moduleNameLower . '.php'),
        ], 'config');

        $this->mergeConfigFrom(
            module_path($this->moduleName, 'Config/config.php'), $this->moduleNameLower
        );
        $this->mergeConfigFrom(
            module_path($this->moduleName, 'Config/email-templates.php'), $this->moduleNameLower
        );

        $this->mergeConfigFrom(
            module_path($this->moduleName, 'Config/sms-template.php'),
            $this->moduleNameLower
        );
    }

    /**
     * Register views.
     *
     * @return void
     */
    public function registerViews()
    {
        $viewPath = resource_path('views/modules/' . $this->moduleNameLower);

        $sourcePath = module_path($this->moduleName, 'Resources/views');

        $this->publishes([
            $sourcePath => $viewPath
        ], ['views', $this->moduleNameLower . '-module-views']);

        $this->loadViewsFrom(array_merge($this->getPublishableViewPaths(), [$sourcePath]), $this->moduleNameLower);
    }

    /**
     * Register translations.
     *
     * @return void
     */
    public function registerTranslations()
    {
        $langPath = resource_path('lang/modules/' . $this->moduleNameLower);

        if (is_dir($langPath)) {
            $this->loadTranslationsFrom($langPath, $this->moduleNameLower);
        } else {
            $this->loadTranslationsFrom(module_path($this->moduleName, 'Resources/lang'), $this->moduleNameLower);
        }
    }

    /**
     * Register an additional directory of factories.
     *
     * @return void
     */
    public function registerFactories()
    {
        if (!isProd() && $this->app->runningInConsole()) {
            app(Factory::class)->load(module_path($this->moduleName, 'Database/factories'));
        }
    }

    /**
     * Get the services provided by the provider.
     *
     * @return array
     */
    public function provides()
    {
        return [];
    }

    private function getPublishableViewPaths(): array
    {
        $paths = [];
        foreach (Config::get('view.paths') as $path) {
            if (is_dir($path . '/modules/' . $this->moduleNameLower)) {
                $paths[] = $path . '/modules/' . $this->moduleNameLower;
            }
        }
        return $paths;
    }
}
