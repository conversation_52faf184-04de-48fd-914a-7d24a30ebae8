<?php

use <PERSON><PERSON>les\Communication\Http\Controllers\EmailController;
use Modules\Communication\Http\Controllers\EmailTemplateController;
use Illuminate\Support\Facades\Route;

Route::group(['middleware' => ['auth']], function () {
    Route::prefix('communication')->group(function () {
        $idPattern = '[1-9][0-9]{0,9}';

        Route::get('/email', [EmailController::class, 'list'])
            ->name('communication.email.list')
            ->defaults('description', 'View sent emails page')
            ->defaults('module_name', 'Communication')
            ->defaults('controller_name', 'Sent email')
            ->defaults('info_bubble', 'Вижда страница Email изпратени');

        Route::get('/email/export', [EmailController::class, 'export'])
            ->name('communication.email.export')
            ->defaults('module_name', 'Communication')
            ->defaults('controller_name', 'Sent email')
            ->defaults('description', 'Export data');

        Route::get('/email/{email}/preview', [EmailController::class, 'preview'])
            ->name('communication.email.preview')
            ->defaults('description', 'View sent email')
            ->defaults('module_name', 'Communication')
            ->defaults('controller_name', 'Sent email')
            ->defaults('info_bubble', 'Вижда визуализация на изпратените емейли');

        Route::post('/email/sendEmail', [EmailController::class, 'sendEmail'])
            ->name('communication.email.sendEmail');

        // Email templates
        Route::get('/email-templates', [EmailTemplateController::class, 'list'])
            ->name('communication.emailTemplate.list')
            ->defaults('description', 'View Email templates page')
            ->defaults('module_name', 'Communication')
            ->defaults('controller_name', 'Email template')
            ->defaults('info_bubble', 'Вижда страница Email темплейти');

        Route::get('/email-template/create', [EmailTemplateController::class, 'create'])
            ->name('communication.emailTemplate.create')
            ->defaults('module_name', 'Communication')
            ->defaults('controller_name', 'Email template')
            ->defaults('description', 'Create email template');

        Route::post('/email-template/store', [EmailTemplateController::class, 'store'])
            ->name('communication.emailTemplate.store');

        Route::get('/email-template/edit/{emailTemplate}', [EmailTemplateController::class, 'edit'])
            ->name('communication.emailTemplate.edit')
            ->where('id', $idPattern)
            ->defaults('module_name', 'Communication')
            ->defaults('controller_name', 'Email template')
            ->defaults('description', 'Update email template');

        Route::post('/email-template/update/{emailTemplate}', [EmailTemplateController::class, 'update'])
            ->name('communication.emailTemplate.update')
            ->where('id', $idPattern);

        Route::get('/email-template/delete/{emailTemplate}', [EmailTemplateController::class, 'delete'])
            ->name('communication.emailTemplate.delete')
            ->where('id', $idPattern)
            ->defaults('module_name', 'Communication')
            ->defaults('controller_name', 'Email template')
            ->defaults('description', 'Delete email template');

        Route::get('/email-template/enable/{emailTemplate}', [EmailTemplateController::class, 'enable'])
            ->name('communication.emailTemplate.enable')
            ->where('id', $idPattern)
            ->defaults('module_name', 'Communication')
            ->defaults('controller_name', 'Email template')
            ->defaults('description', 'Enable email template');

        Route::get('/email-template/disable/{emailTemplate}', [EmailTemplateController::class, 'disable'])
            ->name('communication.emailTemplate.disable')
            ->where('id', $idPattern)
            ->defaults('module_name', 'Communication')
            ->defaults('controller_name', 'Email template')
            ->defaults('description', 'Disable email template');

        Route::get(
            '/email-template/revert/{emailTemplate}/{logEmailTemplate}',
            [EmailTemplateController::class, 'revert']
        )
            ->name('communication.emailTemplate.revert')
            ->defaults('module_name', 'Communication')
            ->defaults('controller_name', 'Email template')
            ->defaults('description', 'Disable email template');

        Route::get('/email-template/{template}/preview', [EmailTemplateController::class, 'preview'])
            ->name('communication.emailTemplate.preview')
            ->defaults('module_name', 'Communication')
            ->defaults('controller_name', 'Email template')
            ->defaults('description', 'Preview email template');

        // Email template session
        Route::get('/email-template/filters', [EmailTemplateController::class, 'getFilters'])
            ->name('communication.emailTemplate.getFilters')
            ->defaults('description', 'Get email template filters');
        Route::put('/email-template/filters', [EmailTemplateController::class, 'setFilters'])
            ->name('communication.emailTemplate.setFilters')
            ->defaults('description', 'Update email template filters');
        Route::delete('/email-template/filters', [EmailTemplateController::class, 'cleanFilters'])
            ->name('communication.emailTemplate.cleanFilters')
            ->defaults('description', 'Cleanup filters');
        Route::get('/email-template/refresh', [EmailTemplateController::class, 'refresh'])
            ->name('communication.emailTemplate.refresh')
            ->defaults('description', 'Ajax refresh email template table');
    });
});

