@extends('layouts.app')
@section('content')

    <div class="row">
        <div class="col-lg-6">
            <x-card>
                {!! form_start($manualSendForm, ['onsubmit' => 'confirmSubmission()']) !!}

                <div class="row mb-3">
                    <div class="col-lg-8">
                        {!! form_row($manualSendForm->importClients) !!}
                    </div>
                    <div class="col-lg-4">
                        <div class="form-group">
                            <label for="control-label"></label>
                            <a href="{{asset('/assets/sample/ExampleImportManualSendSmsClients.xlsx')}}"
                               class="btn btn-warning btn-block">
                                <i class="fa fa-download"></i>&nbsp;
                                {{__('table.DownloadDiscountFile')}}
                            </a>
                        </div>
                    </div>
                </div>
                <!-- End ./row -->

                {!! form_row($manualSendForm->clientId) !!}

                {!! form_row($manualSendForm->sms_message) !!}

                <div class="form-group mb-0">
                    <button type="submit" class="btn btn-sm btn-primary">
                        <i class="fa fa-save"></i>&nbsp;
                        {{__('table.Save')}}
                    </button>
                </div>

                {!! form_end($manualSendForm) !!}
            </x-card>
        </div>
        <!-- End ./col-lg-6 -->
    </div>
    <!-- End ./row -->
@endsection

@push('scripts')
    <script>
        function confirmSubmission(event) {
            if (!confirm("Сигурен ли си, че искаш да изпратиш?")) {
                event.preventDefault();
            }
        }
        
        $(function () {
            let $findClientsEl = $('select[data-find-client="true"]');

            $findClientsEl.select2({
                width: "100%",
                ajax: {
                    url: $($findClientsEl).data('req-route'),
                    processResults: function (data) {
                        return {
                            results: $.map(data, function (item) {
                                return {
                                    text: '[' + item.pin + ']: ' + item.name,
                                    id: item.client_id
                                }
                            })
                        };
                    }
                }
            });
        })
    </script>
@endpush
