@extends('layouts.app')
@section('style')
    <style>
        .button-div{
            display: none !important;
        }
    </style>
@endsection
@section('content')
    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <form id="mailForm" class="form-inline card-body"
                      action="{{ route('communication.mail.list') }}"
                      method="PUT">
                    @csrf
                    <div class="form-row w-100">

                        <div class="col-lg-2 mb-3">
                            <input name="client_name" class="form-control w-100 mb-3" type="text"
                                   placeholder="{{__('table.Name')}}"
                                   value="{{ session($cacheKey . '.client_name') }}">
                        </div>


                        <div class="col-lg-2 mb-3">
                            <input name="administrator_name" class="form-control w-100 mb-3" type="text"
                                   placeholder="{{__('communication::smsTable.Sender')}}"
                                   value="{{ session($cacheKey . '.administrator_name') }}">
                        </div>
                        <div class="col-lg-2 mb-3">
                            <input type="text" autocomplete="off" name="sent_at" class="form-control"
                                   id="sendAt"
                                   value="{{ session($cacheKey . '.sent_at') }}"
                                   placeholder="{{__('communication::smsTable.SendAt')}}">
                        </div>
                        <div class="col-lg-12">
                            <x-btn-filter/>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <div class="row" id="container-row">
        <div class="col-lg-12">
            <div id="main-table" class="card">
                <div class="card-body">
                    <div class="table-responsive" id="mailTable">
                        @include('communication::mail.list-table')
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('scripts')
    <script type="text/javascript" src="{{ asset('js/pagination.js') }}"></script>
    <script type="text/javascript" src="{{ asset('js/jsGrid.js') }}"></script>
    <script>
        loadDateRangePicker($("#sendAt"));
        let clientControllerUrl = '{{ route('communication.mail.refresh') }}';
        let formId = $("#mailForm");
        let tableId = $('#mailTable');
        loadSimpleDataGrid(clientControllerUrl,formId,tableId);
    </script>
@endpush()
