<table class="table" >
    <thead>
    <tr>
        <th class="w-25" scope="col">{{__('table.Name')}}</th>
        <th scope="col">{{__('communication::emailTable.Administrator')}}</th>
        <th scope="col">{{__('table.Type')}}</th>
        <th scope="col">{{__('table.Manual')}}</th>
        <th scope="col">{{__('communication::emailTable.Identifier')}}</th>
        <th scope="col">{{__('communication::emailTable.Sender')}}</th>
        <th scope="col">{{__('table.PostCode')}}</th>
        <th scope="col">{{__('table.Address')}}</th>
        <th scope="col">{{__('communication::emailTable.SendAt')}}</th>
        <th style="display: none;" scope="col">{{__('table.Actions')}}</th>
    </tr>
    </thead>
    <tbody>
    @foreach($mails as $mail)
        <tr
            @if(!$mail->active)
            class="not-active"
            @endif
        >
            <td>{{$mail->client->first_name . ' '. $mail->client->last_name}}</td>
            <td>{{ $mail->administrator->username}}</td>
            <td>{{ $mail->mailType->name }}</td>
            <td>{{ $mail->manual ? __('table.Yes') : __('table.No') }}</td>
            <td>{{ $mail->identifier }}</td>
            <td>{{ $mail->sender }}</td>
            <td>{{ $mail->postcode }}</td>
            <td>{{ $mail->address }}</td>
            <td>{{ $mail->sent_at }}</td>
            <td class="button-div">
                <div class="button-actions">
                    <x-btn-edit
                        url="{{ route('communication.mail.list', $mail->mail_id) }}"/>

                </div>
            </td>
        </tr>
    @endforeach
    </tbody>
    <tfoot>
    <tr id="pagination-nav">
        <td colspan="13">
            {{ $mails->links() }}
        </td>
    </tr>
    </tfoot>
</table>
