<table class="table" >
    <thead>
    <tr>
        <th scope="col">{{__('table.Name')}}</th>

        <th scope="col">{{__('communication::smsTable.Administrator')}}</th>
        <th scope="col">{{__('table.Type')}}</th>
        <th scope="col">{{__('table.Manual')}}</th>
        <th scope="col">{{__('communication::smsTable.Identifier')}}</th>
        <th scope="col">{{__('communication::smsTable.Text')}}</th>
        <th scope="col">{{__('communication::smsTable.Sender')}}</th>
        <th scope="col">{{__('table.Phone')}}</th>
        <th scope="col">{{__('communication::smsTable.Response')}}</th>
        <th scope="col">{{__('communication::smsTable.Queue')}}</th>
        <th scope="col">{{__('communication::smsTable.QueueAt')}}</th>
        <th scope="col">{{__('communication::smsTable.Tries')}}</th>
        <th scope="col">{{__('communication::smsTable.SendAt')}}</th>
        <th style="display: none;" scope="col">{{__('table.Actions')}}</th>
    </tr>
    </thead>
    <tbody>
    @foreach($smses as $sms)
        <tr
            @if(!$sms->active)
            class="not-active"
            @endif
        >
            <td>{{ $sms->smsTemplate->key }}</td>
            <td>{{ $sms->administrator->username}}</td>
            <td>{{ $sms->type }}</td>
            <td>{{ $sms->manual ? __('table.Yes') : __('table.No') }}</td>
            <td>{{ $sms->identifier }}</td>
            <td>{{ $sms->text }}</td>
            <td>{{ $sms->sender }}</td>
            <td>{{ $sms->phone }}</td>
            <td>{{ $sms->response }}</td>
            <td>{{ $sms->queue }}</td>
            <td>{{ $sms->queued_at }}</td>
            <td>{{ $sms->tries }}</td>
            <td>{{ $sms->sent_at }}</td>
            <td class="button-div">
                <div class="button-actions">
                    <x-btn-edit
                        url="{{ route('communication.sms.list', $sms->sms_id) }}"/>
                </div>
            </td>
        </tr>
    @endforeach
    </tbody>
    <tfoot>
    <tr id="pagination-nav">
        <td colspan="13">
            {{ $smses->links() }}
        </td>
    </tr>
    </tfoot>
</table>
