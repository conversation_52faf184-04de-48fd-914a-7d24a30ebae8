<table class="table">
    <thead>
    <tr>
        <th scope="col">{{__('table.Name')}}</th>
        <th scope="col">{{__('table.Type')}}</th>
        <th scope="col">{{__('table.Manual')}}</th>
        <th scope="col">{{__('table.Active')}}</th>
        <th scope="col" class="tableHeader">{{__('table.CreatedAt')}}</th>
        <th scope="col" class="tableHeader">{{__('table.CreatedBy')}}</th>
        <th scope="col" class="tableHeader">{{__('table.UpdatedAt')}}</th>
        <th scope="col" class="tableHeader">{{__('table.UpdatedBy')}}</th>
        <th scope="col">{{__('table.Actions')}}</th>
    </tr>
    </thead>
    <tbody>

    @foreach($viberTemplates as $template)

        <tr
            @if(!$template->active)
            class="not-active"
            @endif
        >
            <td>{{ $template->name }}</td>
            <td>{{ $template->type }}</td>
            <td>{{ $template->manual ? __('table.Yes') : __('table.No') }}</td>
            <td>{{ $template->isActive() ? __('table.Yes') : __('table.No') }}</td>
            <x-timestamps :model="$template"/>
            <td class="button-div">
                <div class="button-actions">
                    <x-btn-edit
                        url="{{ route('communication.viberTemplate.edit', $template->viber_template_id) }}"/>
                    <x-btn-delete
                        url="{{ route('communication.viberTemplate.delete', $template->viber_template_id) }}"/>
                    @if($template->isActive())
                        <x-btn-disable
                            url="{{ route('communication.viberTemplate.disable', $template->viber_template_id) }}"/>
                    @else
                        <x-btn-enable
                            url="{{ route('communication.viberTemplate.enable', $template->viber_template_id) }}"/>
                    @endif
                </div>
            </td>
        </tr>
    @endforeach
    </tbody>
    <tfoot>
    <tr id="pagination-nav">
        <td colspan="10">
            {{ $viberTemplates->links() }}
        </td>
    </tr>
    </tfoot>
</table>
