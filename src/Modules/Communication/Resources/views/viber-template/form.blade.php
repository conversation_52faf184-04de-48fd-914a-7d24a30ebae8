<div class="row" style="padding-left: 15px;">
    <div class="col-lg-3">
        <form id="ViberTemplateForm" method="POST"
              action="{{
                        !empty($viberTemplate) ?
                    route('communication.viberTemplate.update', $viberTemplate->viber_template_id)
                    : route('communication.viberTemplate.store')
                    }}"
              accept-charset="UTF-8">
            @csrf
            <div class="card">
                <div class="card-body">
                    <div class="form-group">
                        <label for="name" class="control-label required">{{ __('table.Name') }}</label>
                        <input class="form-control" required="required" minlength="2" maxlength="50"
                               name="name" type="text"
                               value="{{ old('name') ?? ($viberTemplate->name ?? '')}}"
                               id="name">
                    </div>
                    <div class="form-group">
                        <label for="description"
                               class="control-label required">{{ __('table.Description') }}</label>
                        <input class="form-control" required="required" minlength="2" maxlength="30"
                               name="description" type="text"
                               value="{{ old('description') ?? ($viberTemplate->description ?? '')}}"
                               id="description">
                    </div>
                    <div class="form-group">
                        <label for="description"
                               class="control-label required">{{ __('communication::smsTable.Gender') }}</label>
                        <x-select-sex-with-common
                            sex="{{ old('gender') ?? ($viberTemplate->gender ?? '') }}"
                            componentName="gender"/>
                    </div>

                    <div class="form-group">
                        <label for="description"
                               class="control-label required">{{ __('table.Type') }}</label>
                        <x-select-communication-type
                            :typeOptions="$getViberTemplateTypes"
                            type="{{ old('type') ?? ($viberTemplate->type ?? '') }}"
                            componentName="type"/>
                    </div>

                    <div class="form-group">
                        <label for="offices">{{__('table.Office')}}</label>
                        <select id="offices" title="{{__('table.ChooseOffice')}}" name="offices[]"
                                class="form-control w-100 live-search-city" data-live-search="true"
                                data-actions-box="true" data-selected-text-format="count > 3" multiple>
                            @foreach($offices as $office)
                                <option
                                    @if(!empty($viberTemplate) && $viberTemplate->officesRelation->contains($office->office_id))
                                        selected
                                    @endif
                                    value="{{$office->office_id}}">{{$office->name}}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <div class="form-group mb-5">
                        <div class="form-group pt-3">
                            <label for="key">{{__('table.Key')}}</label>
                            <select title="{{__('table.ChooseKey')}}" name="key"
                                    class="form-control w-100">
                                @foreach($viberTemplateClass::getKeys() as $keys)
                                    <option
                                        value="{{$keys}}">{{ __('communication::templates.' . $keys)}}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="form-group d-flex flex-row align-content-center ">
                        <label for="manual"
                               class="required align-self-center">{{ __('table.canBeGeneratedManually') }}</label>
                        <input class="form-control w-5-hard ml-auto"  type="checkbox" name="manual" id="manual"
                               @if(!empty($viberTemplate->manual))
                                   checked="checked"
                            @endif
                        >
                    </div>
                </div>
            </div>
            <x-button-bottom-bar
                url="{{route('communication.viberTemplate.list')}}"
                saveEditName="{{ !empty($viberTemplate) ? __('btn.Update') : __('btn.Create')}}"
                cancelName="{{ __('btn.Cancel') }}"
            />
        </form>

        @if(!empty($viberTemplate))

            <div class="card">
                <div class="card-body">
                    <form method="POST"
                          action="{{route('communication.viber.send')}}"
                          accept-charset="UTF-8">
                        @csrf
                        <input class="form-control" required="required" minlength="1" maxlength="30"
                               name="viber_template_id" type="hidden"
                               value="{{$viberTemplate->viber_template_id}}">
                        <div class="form-group">
                            <label for="loanId"
                                   class="control-label required">{{ __('docs::document.LoanId') }}</label>
                            <input class="form-control" required="required" minlength="1" maxlength="30"
                                   name="loan_id" type="text"
                                   id="loanId">
                        </div>
                        <div class="form-group mb-5">
                            <label for="phone"
                                   class="control-label required">{{ __('table.Phone') }}</label>
                            <select name="phone" id="phone"
                                    class="form-control live-search-city show-tick"
                            >
                                @foreach(config('communication.test_phones') as $phone)
                                    <option value="{{$phone['phone']}}">{{$phone['phone']}}</option>
                                @endforeach
                            </select>
                        </div>
                        <button type="submit" value="false" name="download"
                                class="btn btn-success default-btn-last">{{ __('btn.Send') }}</button>
                    </form>
                </div>
            </div>
        @endif
    </div>
    <div class="col-lg-9">
        <div class="card">
            <div class="card-body">
                <div class="form-group">
                    <label for="content"
                           class="control-label required">{{ __('table.Content') }}</label>
                    <textarea form="ViberTemplateForm" class="form-control" name="text"
                              id="content">
                                    {{old('text') ?? ($viberTemplate->text ?? '')}}
                                </textarea>
                </div>
            </div>
        </div>

    </div>
</div>
