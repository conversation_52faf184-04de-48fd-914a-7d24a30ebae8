@extends('layouts.app')
@section('style')
    <style>
        .button-div {
            display: none !important;
        }
    </style>
@endsection
@section('content')
    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <form id="viberForm" class="form-inline card-body"
                      action="{{ route('communication.viber.list') }}"
                      method="PUT">
                    @csrf
                    <div class="form-row w-100">

                        <div class="col-lg-2 mb-3">
                            <x-select-communication-type
                                :typeOptions="$getViberTypes"
                                type="{{ old('type') ?? (session($cacheKey . '.type') ?? '') }}"
                                componentName="type"/>
                        </div>

                        <div class="col-lg-2 mb-3">
                            <input type="text" autocomplete="off" name="sent_at" class="form-control"
                                   id="sendAt"
                                   value="{{ session($cacheKey . '.sent_at') }}"
                                   placeholder="{{__('communication::viberTable.SendAt')}}">
                        </div>
                        <div class="col-lg-12">
                            <x-btn-filter/>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <div class="row" id="container-row">
        <div class="col-lg-12">
            <div id="main-table" class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <div id="viberTableDiv">
                            @include('communication::viber.list-table')
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script type="text/javascript" src="{{ asset('js/jsGrid.js') }}"></script>
    <script type="text/javascript" src="{{ asset('js/pagination.js') }}"></script>
    <script type="text/javascript" src="{{ asset('js/jquery.doubleScroll.js') }}"></script>
    <script>
        loadDateRangePicker($("#sendAt"));
        let viberRefreshUrl = '{{ route('communication.viber.refresh') }}';
        let formId = $('#viberForm');
        let tableId = $('#viberTableDiv');
        loadSimpleDataGrid(viberRefreshUrl, formId, tableId);
    </script>
@endpush
