<table class="table">
    <thead>
    <tr>
        <th scope="col">{{__('table.Phone')}}</th>
        <th scope="col">{{__('table.Name')}}</th>
        <th scope="col">{{__('table.Type')}}</th>
        <th scope="col">{{__('table.Manual')}}</th>
        <th scope="col">{{__('communication::viberTable.Text')}}</th>

        <th scope="col">{{__('communication::viberTable.SendAt')}}</th>
        <th style="display: none;" scope="col">{{__('table.Actions')}}</th>
    </tr>
    </thead>
    <tbody id="viberTable">
    @foreach($vibers as $viber)

        <tr
            @if(!$viber->active)
            class="not-active"
            @endif
        >
            <td>{{ $viber->phone }}</td>
            <td>{{ $viber->viberTemplate->key }}</td>
            <td>{{__('communication::smsTable.' . ucfirst($viber->type))}}</td>
            <td>{{ $viber->manual ? __('table.Yes') : __('table.No') }}</td>
            <td>{{ $viber->text }}</td>
            <td>{{ $viber->created_at }}</td>
            <td class="button-div">
                <div class="button-actions">
                    <x-btn-edit
                        url="{{ route('communication.viber.list', $viber->viber_id) }}"/>
                </div>
            </td>
        </tr>
    @endforeach
    </tbody>
    <tfoot>
    <tr id="pagination-nav">
        <td colspan="13">
            {{ $vibers->links() }}
        </td>
    </tr>
    </tfoot>
</table>
