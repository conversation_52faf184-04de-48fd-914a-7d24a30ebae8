<?php

namespace Modules\Communication\FilterForms;

use Modules\Common\FilterForms\BaseFilterForm;
use Modules\Common\Models\Email;
use Modules\Communication\Models\EmailStatus;
use Modules\Communication\Models\EmailTemplate;

final class EmailFilterForm extends BaseFilterForm
{
    protected static string $route = 'communication.email.list';

    protected static string $exportRoute = 'communication.email.export';

    public function buildForm(): void
    {
        $emailTemplateOptions = EmailTemplate::select(['email_template_id', 'title'])->pluck(
            'title',
            'email_template_id'
        )->toArray();
        $this->addSimpleSelectFilter('email_template_id', $emailTemplateOptions, __('menu.EmailTemplates'), false);
        $this->modify('email_template_id', 'select', [
            'attr' => [
                'multiple' => 'multiple',
                'data-live-search' => 'true'
            ]
        ]);

        $this->add('is_manual', 'select', [
            'label' => __('table.Manual'),
            'empty_value' => __('table.SelectOption'),
            'choices' => [
                1 => __('table.Yes'),
                0 => __('table.No'),
            ],
            'selected' => $this->request->get('is_manual')
        ]);

        $this->add('title', 'text', [
            'label' => __('table.Name')
        ]);

        $this->addSimpleSelectFilter('type', $this->getEmailTypes(), __('table.Type'));
        $this->addSimpleSelectFilter('status', $this->getEmailStatuses(), __('table.Status'));
        $this->modify('status', 'select', [
            'empty_value' => '',
            'attr' => [
                'data-live-search' => 'true',
                'data-actions-box' => 'true',
                'multiple' => 'true',
            ]
        ]);

        $this->add('sender_from', 'text', [
            'label' => __('communication::emailTable.Sender')
        ]);

        $this->add('send_to', 'text', [
            'label' => __('table.Email')
        ]);

        $this->addPinFilter('send_to_pin');

        $this->addDateFilterFromTo('sent_at', __('communication::emailTable.SendAt'));

        $this->add('offer_without_credit', 'select', [
            'label' => __('table.OfferWithoutCredit'),
            'empty_value' => __('table.SelectOption'),
            'choices' => [
                1 => __('table.Yes'),
                0 => __('table.No'),
            ],
            'selected' => $this->request->get('offer_without_credit')
        ]);
    }


    private function getEmailTypes(): array
    {
        $rows = Email::getEmailTypes();
        $options = [];

        foreach ($rows as $row) {
            $options[$row] = __('communication::smsTable.' . ucfirst($row));
        }

        return $options;
    }

    private function getEmailStatuses(): array
    {
        $rows = EmailStatus::pluck('name', 'key');
        $options = [];

        foreach ($rows as $key => $name) {
            $options[$key] = __('communication::emailTable.Statuses.' . $name);
        }

        return $options;
    }
}
