<?php

declare(strict_types=1);

namespace Modules\Communication\Services;

use Carbon\CarbonImmutable;
use Carbon\CarbonInterface;
use Illuminate\Support\Facades\Log;
use Modules\Common\Models\Email;
use Modules\Communication\Enums\EmailStatusEnum;
use Modules\Communication\Models\EmailStatus;
use RuntimeException;

final readonly class MailgunWebhookService
{
    public function handleWebhook(array $eventData): void
    {
        match (EmailStatusEnum::tryFrom($eventData['event-data']['event'] ?? null)) {
            EmailStatusEnum::Delivered => $this->handleDeliveredEvent($eventData),
            EmailStatusEnum::Failed => $this->handleFailedEvent($eventData),
            EmailStatusEnum::Opened => $this->handleOpenedEvent($eventData),
            EmailStatusEnum::Clicked => $this->handleClickedEvent($eventData),

            EmailStatusEnum::Accepted => $this->handleAcceptedEvent($eventData),
            EmailStatusEnum::Rejected => $this->handleRejectedEvent($eventData),
            EmailStatusEnum::Unsubscribed => $this->handleUnsubscribedEvent($eventData),
            EmailStatusEnum::Complained => $this->handleComplainedEvent($eventData),
            EmailStatusEnum::Stored => $this->handleStoredEvent($eventData),
            default => Log::warning('Unhandled Mailgun event', $eventData),
        };
    }

    private function handleDeliveredEvent(array $eventData): void
    {
        $email = $this->getEmail($eventData);

        if (!$email) {
            return;
        }

        $email->status_id = $this->getStatusId(EmailStatusEnum::Delivered);
        $email->received_at = $this->getDateTime($eventData);

        $email->save();
    }

    private function getEmail(array $eventData): ?Email
    {
        $externalId = $eventData['event-data']['message']['headers']['message-id'];
        if (!$externalId) {
            throw new RuntimeException('Failed to get mailgun message id');
        }

        $email = Email::where('external_id', $externalId)->first();
        if (!$email) {
            Log::warning('Email with mailgun message id ' . $externalId . ' not found');

            return null;
        }

        return $email;
    }

    private function getStatusId(EmailStatusEnum $statusEnum): ?int
    {
        return EmailStatus::ofKey($statusEnum)->value('id');
    }

    private function getDateTime(array $eventData): CarbonInterface
    {
        return CarbonImmutable::createFromTimestamp($eventData['event-data']['timestamp']);
    }

    private function handleFailedEvent(array $eventData): void
    {
        $email = $this->getEmail($eventData);

        if (!$email) {
            return;
        }

        $email->status_id = $this->getStatusId(EmailStatusEnum::Failed);

        $email->save();
    }

    private function handleOpenedEvent(array $eventData): void
    {
        $email = $this->getEmail($eventData);

        if (!$email) {
            return;
        }

        $email->status_id = $this->getStatusId(EmailStatusEnum::Opened);
        $email->opened_at = $this->getDateTime($eventData);

        $email->save();
    }

    private function handleClickedEvent(array $eventData): void
    {
        $email = $this->getEmail($eventData);

        if (!$email) {
            return;
        }

        $email->status_id = $this->getStatusId(EmailStatusEnum::Clicked);

        $email->save();
    }

    private function handleAcceptedEvent(array $eventData): void
    {
        $email = $this->getEmail($eventData);

        if (!$email) {
            return;
        }

        $email->status_id = $this->getStatusId(EmailStatusEnum::Accepted);

        $email->save();
    }

    private function handleRejectedEvent(array $eventData): void
    {
        $email = $this->getEmail($eventData);

        if (!$email) {
            return;
        }

        $email->status_id = $this->getStatusId(EmailStatusEnum::Rejected);

        $email->save();
    }

    private function handleUnsubscribedEvent(array $eventData): void
    {
        $email = $this->getEmail($eventData);

        if (!$email) {
            return;
        }

        $email->status_id = $this->getStatusId(EmailStatusEnum::Unsubscribed);

        $email->save();
    }

    private function handleComplainedEvent(array $eventData): void
    {
        $email = $this->getEmail($eventData);

        if (!$email) {
            return;
        }

        $email->status_id = $this->getStatusId(EmailStatusEnum::Complained);

        $email->save();
    }

    private function handleStoredEvent(array $eventData): void
    {
        $email = $this->getEmail($eventData);

        if (!$email) {
            return;
        }

        $email->status_id = $this->getStatusId(EmailStatusEnum::Stored);

        $email->save();
    }
}
