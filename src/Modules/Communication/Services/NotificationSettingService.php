<?php

namespace Modules\Communication\Services;

use Modules\Common\Models\Client;
use Modules\Common\Models\NotificationSetting;
use Modules\Common\Services\BaseService;
use Modules\Communication\Repositories\NotificationSettingRepository;

class NotificationSettingService extends BaseService
{
    private NotificationSettingRepository $notificationSettingRepository;

    public function __construct(
        NotificationSettingRepository $notificationSettingRepository
    )
    {
        $this->notificationSettingRepository = $notificationSettingRepository;

        parent::__construct();
    }

    public function getById(int $id)
    {
        return $this->notificationSettingRepository->getById($id);
    }

    public function create(Client $client)
    {
        $notificationSettings = NotificationSetting::notificationDefaultValue();

        $data = [];
        foreach ($notificationSettings as $type => $channels) {
            foreach ($channels as $channel) {
                $data['client_id'] = $client->client_id;
                $data['type'] = $type;
                $data['channel'] = $channel;
                $data['value'] = NotificationSetting::NOTIFICATION_SETTING_DEFAULT_VALUE;
                $this->notificationSettingRepository->create($data);
            }
        }
    }

    public function massiveUpdate(array $notificationSettings)
    {
        foreach ($notificationSettings as $id => $value) {
            $this->notificationSettingRepository->updateById($id, $value);
        }
    }

    public function update(NotificationSetting $notificationSetting, array $data): NotificationSetting
    {
        return $this->notificationSettingRepository->update(
            $notificationSetting,
            $data
        );
    }

    public function getByCriteria(array $criteria): ?NotificationSetting
    {
        return $this->notificationSettingRepository->getByData(
            $criteria['client_id'],
            $criteria['type'],
            $criteria['channel'],
        );
    }
}
