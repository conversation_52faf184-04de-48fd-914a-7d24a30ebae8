<?php

namespace Modules\Communication\Observers;

use Modules\Common\Models\NotificationSetting;
use Modules\Common\Models\NotificationSettingHistory;

class NotificationSettingObserver
{

    public function __construct()
    {
    }

    /**
     * @param NotificationSetting $notificationSetting
     */
    public function updated(NotificationSetting $notificationSetting)
    {
//        $notificationSettingHistory = new NotificationSettingHistory();
//        $notificationSettingHistory->fill($notificationSetting->getOriginal());
//        $notificationSettingHistory->save();
    }
}