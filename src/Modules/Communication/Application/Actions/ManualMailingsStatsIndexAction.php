<?php

declare(strict_types=1);

namespace Modules\Communication\Application\Actions;

use Modules\Communication\Forms\ManualMailingsStatsFiltersForm;
use Modules\Communication\Repositories\SendingStatsRepository;

final readonly class ManualMailingsStatsIndexAction
{
    public function __construct(private SendingStatsRepository $repository)
    {
    }

    public function execute(array $filters = [], int $perPage = 10): array
    {
        return [
            'stats' => $this->repository->getPaginatorByFilters($filters, $perPage),
            'statsFilterForm' => ManualMailingsStatsFiltersForm::create(),
        ];
    }
}
