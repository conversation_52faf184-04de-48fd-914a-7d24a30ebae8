<?php

namespace Modules\Communication\Application\Actions;

use <PERSON>\LaravelFormBuilder\Form;
use <PERSON>\LaravelFormBuilder\FormBuilder;
use Modules\Communication\FilterForms\SmsFilterForm;
use Modules\Communication\Repositories\SmsRepository;

class SmsDataAction
{
    public function __construct(
        private readonly SmsRepository $smsRepository
    ) {
    }

    public function execute(array $filters = []): array
    {
        return [
            'smses' => $this->smsRepository->getFilterBy($filters),
            'smsFilterForm' => SmsFilterForm::create(),
        ];
    }
}
