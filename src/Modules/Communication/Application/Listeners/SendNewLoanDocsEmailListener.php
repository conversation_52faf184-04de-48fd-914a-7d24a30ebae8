<?php

namespace Modules\Communication\Application\Listeners;

use Modules\Common\Models\Document;
use Modules\Common\Models\DocumentTemplate;
use Modules\Common\Models\LoanStatus;
use Modules\Communication\Application\Enums\EmailTemplateKeyEnum;
use Modules\Communication\Models\EmailTemplate;
use Modules\Communication\Services\EmailService;
use Modules\Sales\Domain\Events\NewLoanDocsGenerated;

class SendNewLoanDocsEmailListener
{
    public function __construct() {}

    public function handle(NewLoanDocsGenerated $event)
    {
        $dbLoan = $event->loan->refresh();

        if (!$dbLoan->isOnlineLoan()) {
            return 'success';
        }

        $attachments = self::getNewLoanAttachments($dbLoan);


        // auto-approve logic, we send custom email
        $meta = $dbLoan->getMeta('ap_changed_loan_params_email');
        if (!empty($meta->value)) {

            $data = json_decode($meta->value, true);
            $emailVars = $data['vars'] ?? [];
            $tplKey = $data['tpl'] ?? '';

            if (!empty($emailVars) && !empty($tplKey)) {
                app(EmailService::class)->sendByTemplateKey(
                    $tplKey,
                    $dbLoan->client,
                    $dbLoan,
                    $emailVars,
                    $attachments,
                    false, // $force
                    2, // $delayInSec
                );

                // when we used it once, we delete it
                $meta->forceDelete();
            }

            return;
        }


        $alreadyHasNew = $dbLoan->loanStatusHistory
            ->where('loan_status_id', LoanStatus::NEW_STATUS_ID)
            ->count();
        $loanParamsChanged = ($alreadyHasNew > 1);

        $tplKey = EmailTemplateKeyEnum::LOAN_PARAMS_CHANGED->value; // CHANGED LOAN PARAMS
        if (!$loanParamsChanged) {
            $tplKey = EmailTemplateKeyEnum::NEW_APPLICATION_CREATED->value; // NEW LOAN
        }

        app(EmailService::class)->sendByTemplateKeyAndLoanId(
            $tplKey,
            $dbLoan->loan_id,
            0, // delay
            [], // vars
            $attachments
        );


        return 'success';
    }

    public static function getNewLoanAttachments($dbLoan): array
    {
        $tpls = [DocumentTemplate::TPL_SEF, DocumentTemplate::TPL_GEN_TERMS];
        $docTemplateIds = DocumentTemplate::whereIn('type', $tpls)
            ->pluck('document_template_id')
            ->toArray();
        $documents = $dbLoan->documents->whereIn('document_template_id', $docTemplateIds);


        $attachments = [];
        $documents->each(function (Document $document) use (&$attachments) {
            $mimeContentType = mime_content_type($document->file->filepath());

            $attachments[] = [
                'file_id' => $document->file->getKey(),
                'path' => $document->file->filepath(),
                'name' => $document->getHumanFileName(),
                'mime' => $mimeContentType,
            ];
        });

        return $attachments;
    }
}
