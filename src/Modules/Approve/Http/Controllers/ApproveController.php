<?php

namespace Modules\Approve\Http\Controllers;

use Illuminate\Contracts\View\View;
use Modules\Approve\Application\Action\ExportAgentStatsAction;
use Modules\Approve\Application\Action\GetAgentStatsAction;
use Modules\Approve\Http\Forms\AgentStatsFilterForm;
use Modules\Approve\Http\Requests\AgentStatsFiltersRequest;
use Modules\Common\Http\Controllers\BaseController;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

use Modules\ThirdParty\Jobs\InsuranceCertificateJob;
use Modules\Common\Models\Loan;

final class ApproveController extends BaseController
{
    public function approveAgentStats(
        AgentStatsFiltersRequest $request,
        GetAgentStatsAction $action
    ): View {


        $loan = Loan::find(468827);
        InsuranceCertificateJob::processInsuranceCertificate($loan);
        dd('tuk');

        return view('approve::statistics.agent', [
            'stats' => $action->execute($request->validated()),
            'agentStatsFilterForm' => AgentStatsFilterForm::create()
        ]);
    }

    public function approveAgentStatsExport(
        AgentStatsFiltersRequest $request,
        ExportAgentStatsAction $action
    ): BinaryFileResponse {
        return $action->execute($request->validated())->download('stats_export_' . time() . '.xlsx');
    }
}
