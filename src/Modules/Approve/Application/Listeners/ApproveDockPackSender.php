<?php

namespace Modules\Approve\Application\Listeners;

use Modules\Approve\Domain\Events\LoanWasApproved;
use Modules\Communication\Services\SmsService;
use Modules\Head\Jobs\SendApprovalEmailJob;

class ApproveDockPackSender
{
    public function __construct(private SmsService $smsService) {}

    public function handle(LoanWasApproved $event): string
    {
        $dbLoan = $event->loan->dbModel();

        // send sms
        $this->smsService->sendApproveCongrats($dbLoan);

        SendApprovalEmailJob::dispatch($dbLoan);

        return 'success';
    }
}
