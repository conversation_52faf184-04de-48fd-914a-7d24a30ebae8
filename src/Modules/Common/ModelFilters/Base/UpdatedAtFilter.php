<?php

namespace Modules\Common\ModelFilters\Base;

use Carbon\Carbon;
use Modules\Common\ModelFilters\ModelFilterAbstract;

class UpdatedAtFilter extends ModelFilterAbstract
{
    public function handle(mixed $filterValue): void
    {
        $dates = explode(' - ', $filterValue);

        if (count($dates) == 2) {
            $dateFrom = new Carbon($dates[0]);
            $dateTo = new Carbon($dates[1]);

            $this->query->whereBetween('updated_at', [
                $dateFrom->startOfDay()->toDateTimeString(),
                $dateTo->endOfDay()->toDateTimeString()
            ]);
        }
    }
}
