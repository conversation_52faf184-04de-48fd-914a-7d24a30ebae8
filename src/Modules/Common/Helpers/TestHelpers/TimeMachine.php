<?php

namespace Modules\Common\Helpers\TestHelpers;

use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Artisan;
use Modules\Collect\Application\Console\DispatchNewDayForClients;
use Modules\Common\Database\Collections\CustomEloquentCollection;
use Modules\Common\Models\Client;
use Modules\Common\Models\ClientActualStats;
use Modules\Common\Models\Installment;
use Modules\Common\Models\Loan;
use Modules\Common\Models\LoanActualStats;
use Modules\Common\Models\LoanStatusHistory;
use Modules\Common\Models\Payment;

class TimeMachine
{
    public function sendBackInTime(Client $client, int $days): Client
    {
        $client = $client->fresh();
        $clientDateColumns = [
            'created_at'=>'',
            'updated_at'=>''
        ];
        $this->moveDatesBackInTime($client, $clientDateColumns, $days);
        foreach ($client->loans as $loan){
            $this->moveLoanBackInTime($loan, $days);
        }
        $this->moveClientStatsBackInTime($client->clientActualStats, $days);
        Artisan::call('script:daily-installment-refresh');
        Artisan::call('script:new-day-active-loan');
        Artisan::call('script:new-day-active-client');
        return $client->fresh();
    }

    private function moveDatesBackInTime(
        Client|Loan|LoanActualStats|ClientActualStats|Installment|Payment|LoanStatusHistory $object, array $dateColumns, int $days
    ): void
    {
        foreach ($dateColumns as $key=>$format){
            $oldDate = $object->getAttribute($key);
            if(! $oldDate){
                continue;
            }
            if(is_string($oldDate)){
                $oldDate = Carbon::parse($oldDate);
            }
            $newDate = $oldDate->subDays($days);
            $object->setAttribute($key, $format? $newDate->format($format) : $newDate);
        }
        $object->saveQuietly();
    }

    private function moveLoanBackInTime(Loan $loan, int $days): void
    {
        $this->moveLoanStatsBackInTime($loan->loanActualStats, $days);
        $this->moveInstallmentsBackInTime($loan, $days);
        $this->moveLoanStatusHistoryBackInTime($loan, $days);
        $this->movePaymentsBackInTime($loan, $days);
        $loanDateColumns = [
            'last_status_update_date'=>'',
            'repaid_at'=>'Y-m-d',
            'interest_updated_at'=>'',
            'loan_changed_at'=>'',
            'created_at'=>'',
            'updated_at'=>'',
            'activated_at'=>''
        ];
        $this->moveDatesBackInTime($loan, $loanDateColumns, $days);
    }

    private function moveInstallmentsBackInTime(Loan $loan, int $days): void
    {
        $installmentDateColumns = [
            'due_date'=>'',
            'paid_at'=>'',
            'created_at'=>'',
            'updated_at'=>''
        ];
        foreach ($loan->installments as $installment) {
            $this->moveDatesBackInTime($installment, $installmentDateColumns, $days);
        }
    }

    private function movePaymentsBackInTime(Loan $loan, int $days): void
    {
        $paymentDateColumns = [
            'handled_at'=>'',
            'sent_at'=>'',
            'created_at'=>'',
            'updated_at'=>''
        ];
        foreach ($loan->payments as $payment) {
            $this->moveDatesBackInTime($payment, $paymentDateColumns, $days);
        }
    }

    private function moveClientStatsBackInTime(ClientActualStats $clientActualStats, int $days): void
    {
        $clientStatsDateColumns = [
            'first_loan_created_at'=>'Y-m-d',
            'first_loan_activated_at'=>'Y-m-d',
            'first_loan_repaid_at'=>'Y-m-d',
            'created_at'=>'',
            'updated_at'=>''
        ];
        $this->moveDatesBackInTime($clientActualStats, $clientStatsDateColumns, $days);
    }

    private function moveLoanStatsBackInTime(LoanActualStats $loanActualStats, int $days): void
    {
        $loanStatsDateColumns = [
            'date'=>'Y-m-d',
            'current_installment_date'=>'Y-m-d',
            'first_installment_date'=>'Y-m-d',
            'next_installment_date'=>'Y-m-d',
            'last_installment_date'=>'Y-m-d',
            'contract_end_date'=>'Y-m-d',
            'repayment_date'=>'Y-m-d',
            'created_at'=>'',
            'updated_at'=>'',
            'credit_limit_updated_at'=>'Y-m-d',
            'first_repayment_date'=>'Y-m-d',
            'prev_repayment_date'=>'Y-m-d',
            'real_repayment_date'=>'Y-m-d',
            'first_payment_received_at'=>'Y-m-d',
        ];
        $this->moveDatesBackInTime($loanActualStats, $loanStatsDateColumns, $days);
    }

    private function moveLoanStatusHistoryBackInTime(Loan $loan, $days) {
        $loanStatusDateColumns = [
            'date'=>'Y-m-d',
            'created_at'=>'',
            'updated_at'=>''
        ];
        foreach ($loan->loanStatusHistory as $statusH){
            $this->moveDatesBackInTime($statusH, $loanStatusDateColumns, $days);
        }
    }
}
