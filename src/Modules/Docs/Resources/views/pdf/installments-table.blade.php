<table>
    <tr>
        <td>
        </td>
        <td colspan="4">
            Вноски
        </td>
        <td colspan="3">При хипотезата на чл.20 от договора за кредит</td>
    </tr>
    <tr>
        <td>
            № на вноска
        </td>
        <td>
            Дата/Падеж
        </td>
        <td>
            Вноска Главница
        </td>
        <td>
            Месечна Лихва
        </td>
        <td>
            Вноска Фиксирана
        </td>
        <td>
            Неустойка
        </td>
        <td>
            Месечна вноска с неустойка в лева
        </td>
        <td>
            Месечна вноска с неустойка в евро
        </td>
    </tr>
    @foreach($loan->installments as $installment)
    <tr>
        <td>
            {{$installment->seq_num}}
        </td>
        <td>
            {{formatDate($installment->due_date,'d.m.Y')}}
        </td>
        <td>
            {{amount($installment->principal ?: 0)}}
        </td>
        <td>
             {{amount($installment->interest ?: 0)}}
        </td>
        <td>
            {{amount($installment->installment_amount ?: 0)}}
        </td>
        <td>
            {{amount($installment->penalty ?: 0)}}
        </td>
        <td>
            {{amount($installment->penalty + $installment->installment_amount ?: 0)}}
        </td>
        <td>
            {{amountEur(($installment->penalty + $installment->installment_amount ?: 0))}}
        </td>
    </tr>
    @endforeach

</table>
